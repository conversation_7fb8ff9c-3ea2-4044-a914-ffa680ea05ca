{"version": 3, "file": "wish.js", "sources": ["store/wish.js"], "sourcesContent": ["/**\n * 心愿数据管理 Store\n * \n * 重要说明：已彻底移除所有自动同步功能\n * - 不再有定时同步器\n * - 网络恢复时不会自动同步\n * - 所有同步都需要手动触发\n * \n * 手动同步方法：\n * - manualSync(): 完整同步（用户主动调用）\n * - syncPendingData(): 同步待上传数据\n * - performIncrementalSync(): 增量同步\n * \n * 同步状态检查：\n * - pendingSyncCount: 待同步数量\n * - hasSyncErrors: 是否有同步错误\n * - syncStatusText: 同步状态文本\n * - syncInfo: 完整同步信息\n */\n\nimport { defineStore } from 'pinia'\nimport loadingManager from '@/utils/loadingManager.js'\nimport { useUserStore } from './user.js'\n\n// 错误码常量\nconst ERROR_CODES = {\n  SUCCESS: 0,\n  NETWORK_ERROR: 1001,\n  AUTH_ERROR: 1002,\n  VALIDATION_ERROR: 1003,\n  PERMISSION_ERROR: 1004,\n  NOT_FOUND_ERROR: 1005,\n  SERVER_ERROR: 1006,\n  UNKNOWN_ERROR: 9999\n}\n\n// 🚀 简化版本冲突处理 - 完全静默\nconst ConflictManager = {\n  // 静默处理版本冲突，不显示任何弹窗\n  handleVersionConflict(error, context = 'unknown') {\n    console.log(`[ConflictManager] 静默处理版本冲突: ${context}`, error.message)\n    return true // 已处理，不显示弹窗\n  }\n}\n\n// 错误处理工具类\nconst ErrorHandler = {\n  // 获取用户友好的错误信息\n  getUserFriendlyMessage(error) {\n    if (typeof error === 'string') return error\n    \n    if (error.errCode) {\n      switch (error.errCode) {\n        case ERROR_CODES.NETWORK_ERROR:\n          return '网络连接失败，请检查网络设置'\n        case ERROR_CODES.AUTH_ERROR:\n          return '登录已过期，请重新登录'\n        case ERROR_CODES.VALIDATION_ERROR:\n          return error.errMsg || '数据验证失败'\n        case ERROR_CODES.PERMISSION_ERROR:\n          return '没有权限执行此操作'\n        case ERROR_CODES.NOT_FOUND_ERROR:\n          return '数据不存在或已被删除'\n        case ERROR_CODES.SERVER_ERROR:\n          return '服务器错误，请稍后重试'\n        default:\n          return error.errMsg || '操作失败'\n      }\n    }\n    \n    // 处理网络错误\n    if (error.message && (\n      error.message.includes('网络') || \n      error.message.includes('network') ||\n      error.message.includes('timeout') ||\n      error.code === 'NETWORK_ERROR'\n    )) {\n      return '网络连接失败，数据已保存到本地'\n    }\n    \n    return error.message || error.errMsg || '未知错误'\n  },\n  \n  // 判断是否为网络错误\n  isNetworkError(error) {\n    if (error.errCode === ERROR_CODES.NETWORK_ERROR) return true\n    if (error.code === 'NETWORK_ERROR') return true\n    if (error.message && (\n      error.message.includes('网络') || \n      error.message.includes('network') ||\n      error.message.includes('timeout')\n    )) return true\n    \n    return false\n  },\n  \n  // 🚀 检查是否是版本冲突错误\n  isVersionConflictError(error) {\n    return error && error.message && (\n      error.message.includes('数据版本冲突') ||\n      error.message.includes('逻辑时钟冲突') ||\n      error.message.includes('version conflict') ||\n      error.message.includes('conflict')\n    )\n  },\n\n  // 显示错误提示 - 🔧 版本冲突完全静默处理\n  showError(error, title = '操作失败', context = 'unknown') {\n    // 🚀 版本冲突完全静默处理，不显示任何弹窗\n    if (this.isVersionConflictError(error)) {\n      ConflictManager.handleVersionConflict(error, context)\n      return // 不显示弹窗\n    }\n\n    const message = this.getUserFriendlyMessage(error)\n\n    // 🔧 直接显示非冲突错误\n    uni.showToast({\n      title: message,\n      icon: 'none',\n      duration: 3000\n    })\n\n    console.error(`${title}:`, error)\n  }\n}\n\nexport const useWishStore = defineStore('wish', {\n  state: () => ({\n    wishList: [],\n    currentGroupId: 'all', // 默认显示全部分组\n    isLoading: false,\n    lastSyncTime: null,\n    isOnline: true, // 网络状态\n    \n    // 添加一个用于强制触发响应式更新的标记\n    listUpdateCounter: 0,\n\n    // 新增同步状态管理\n    syncStatus: {\n      issyncing: false,\n      lastSyncResult: null,\n      pendingCount: 0,\n      errorCount: 0,\n      lastError: null\n    },\n\n    // 🚀 增强同步状态管理\n    _syncOperations: new Set(), // 正在进行的同步操作\n    _lastSyncTime: 0, // 上次同步时间\n    _recentlyDeleted: new Map() // 最近删除的心愿ID记录\n  }),\n  \n  getters: {\n    // 获取当前分组的心愿列表\n    currentGroupWishes: (state) => {\n      // 访问listUpdateCounter确保响应式更新\n      const updateCounter = state.listUpdateCounter;\n      \n      // 先过滤出未完成且未被删除的心愿\n      const activeWishes = state.wishList.filter(wish => !wish.isCompleted && !wish._deleted);\n      \n      // 然后根据分组进一步过滤\n      if (state.currentGroupId === 'all') {\n        return activeWishes;\n      } else if (state.currentGroupId === 'friend-visible') {\n        // 特殊处理：朋友可见分组，按权限过滤\n        return activeWishes.filter(wish => wish.permission === 'friends');\n      } else if (state.currentGroupId === 'private') {\n        // 特殊处理：私密分组，按权限过滤\n        return activeWishes.filter(wish => wish.permission === 'private');\n      } else if (state.currentGroupId === 'public') {\n        // 特殊处理：公开分组，按权限过滤\n        return activeWishes.filter(wish => wish.permission === 'public');\n      } else if (state.currentGroupId === 'gift') {\n        // 特殊处理：礼物分组，按标签或特殊字段过滤\n        return activeWishes.filter(wish => \n          wish.groupIds && wish.groupIds.includes('gift')\n        );\n      } else {\n        // 普通分组：按groupIds过滤\n        return activeWishes.filter(wish => \n          wish.groupIds && wish.groupIds.includes(state.currentGroupId)\n        );\n      }\n    },\n    \n    // 获取单个心愿\n    getWishById: (state) => (id) => {\n      return state.wishList.find(wish => \n        (wish._id === id || wish.id === id) && !wish._deleted\n      ) || null\n    },\n    \n    // 新增同步状态相关getters\n    // 获取待同步的心愿数量\n    pendingSyncCount: (state) => {\n      return state.wishList.filter(wish => wish._needSync).length;\n    },\n    \n    // 检查是否有同步错误\n    hasSyncErrors: (state) => {\n      return state.syncStatus.errorCount > 0 || state.syncStatus.lastError !== null;\n    },\n    \n    // 获取同步状态描述\n    syncStatusText: (state) => {\n      if (state.syncStatus.issyncing) {\n        return '正在同步...';\n      }\n      if (state.syncStatus.errorCount > 0) {\n        return `同步失败 (${state.syncStatus.errorCount}个错误)`;\n      }\n      if (state.syncStatus.pendingCount > 0) {\n        return `待同步 (${state.syncStatus.pendingCount}项)`;\n      }\n      if (state.syncStatus.lastSyncResult === 'success') {\n        return '同步完成';\n      }\n      return '未同步';\n    },\n    \n    // 获取详细同步信息\n    syncInfo: (state) => {\n      return {\n        ...state.syncStatus,\n        pendingCount: state.wishList.filter(wish => wish._needSync).length,\n        lastSyncTime: state.lastSyncTime,\n        isOnline: state.isOnline\n      };\n    }\n  },\n  \n  actions: {\n    // 初始化心愿数据\n    async initWishList() {\n      console.log('[wishStore] Initializing wish list...');\n      \n      // 先尝试从本地存储加载\n      const storedWishes = uni.getStorageSync('wishList')\n      if (storedWishes) {\n        const parsed = JSON.parse(storedWishes)\n        // 确保每个心愿对象都有 id 字段\n        this.wishList = parsed.map(wish => ({\n          ...wish,\n          id: wish.id || wish._id // 如果没有 id 字段，使用 _id\n        }))\n        console.log('[wishStore] Loaded wishes from storage:', this.wishList.length);\n      }\n      \n      // 初始化网络监听\n      this.initNetworkMonitor()\n      \n      // 尝试从云端同步数据\n      await this.syncFromCloud()\n      \n      console.log('[wishStore] Wish list initialization completed');\n    },\n    \n    // 初始化网络监听\n    initNetworkMonitor() {\n      // 检查当前网络状态\n      this.checkNetworkStatus()\n      \n      // 监听网络状态变化\n      uni.onNetworkStatusChange((res) => {\n        const wasOnline = this.isOnline\n        this.isOnline = res.isConnected\n        \n        console.log(`网络状态变化: ${wasOnline ? '在线' : '离线'} -> ${this.isOnline ? '在线' : '离线'}`)\n        \n        // 如果从离线变为在线，自动同步\n        // 网络恢复时只记录状态，不自动同步\n        if (!wasOnline && this.isOnline) {\n          console.log('网络恢复，但不启用自动同步')\n        }\n        \n        // 如果变为离线，显示提示\n        if (wasOnline && !this.isOnline) {\n          uni.showToast({\n            title: '网络连接已断开，将保存到本地',\n            icon: 'none',\n            duration: 2000\n          })\n        }\n      })\n    },\n    \n    // 检查网络状态\n    async checkNetworkStatus() {\n      try {\n        const networkType = await uni.getNetworkType()\n        this.isOnline = networkType.networkType !== 'none'\n        console.log('当前网络状态:', this.isOnline ? '在线' : '离线')\n      } catch (error) {\n        console.error('检查网络状态失败:', error)\n        this.isOnline = false\n      }\n    },\n    \n    // 手动同步逻辑（原autoSync方法重命名）\n    async _performSync() {\n      // 避免重复同步\n      if (this.syncStatus.issyncing) {\n        console.log('[wishStore] Sync already in progress, skipping...');\n        return;\n      }\n      \n      try {\n        this._updateSyncStatus({ issyncing: true });\n        \n        // 检查是否有需要同步的数据\n        const pendingWishes = this.wishList.filter(wish => wish._needSync)\n        this._updateSyncStatus({ pendingCount: pendingWishes.length });\n        \n        // 优先处理本地待同步数据\n        if (pendingWishes.length > 0) {\n          console.log(`[wishStore] 发现${pendingWishes.length}个待同步心愿，开始上传同步...`)\n          const result = await this.syncPendingData()\n          \n          if (result.syncedCount > 0) {\n            // 上传同步成功后，再检查是否有云端更新\n            await this.performIncrementalSync()\n          }\n        } else {\n          // 没有待同步数据时，执行增量同步检查云端更新\n          console.log('[wishStore] 执行增量同步检查云端更新...')\n          await this.performIncrementalSync()\n        }\n        \n        // 同步成功\n        this._updateSyncStatus({ \n          lastSyncResult: 'success',\n          errorCount: 0,\n          lastError: null \n        });\n        \n      } catch (error) {\n        console.error('[wishStore] 手动同步失败:', error)\n        \n        // 更新错误状态\n        this._updateSyncStatus({ \n          lastSyncResult: 'failed',\n          errorCount: this.syncStatus.errorCount + 1,\n          lastError: error.message || '同步失败'\n        });\n        \n        // 只有在非网络错误时才显示错误提示\n        if (!ErrorHandler.isNetworkError(error)) {\n          console.warn('[wishStore] 手动同步遇到错误')\n        }\n      } finally {\n        this._updateSyncStatus({ issyncing: false });\n      }\n    },\n    \n    // 更新同步状态\n    _updateSyncStatus(updates) {\n      this.syncStatus = {\n        ...this.syncStatus,\n        ...updates\n      };\n      console.log('[wishStore] Sync status updated:', this.syncStatus);\n    },\n    \n    // 手动触发同步（供用户主动调用）\n    async manualSync(silent = false) {\n      // 重置错误状态\n      this._updateSyncStatus({\n        errorCount: 0,\n        lastError: null\n      });\n\n      // 使用统一加载管理器\n      try {\n        await loadingManager.wrap(\n          () => this._performSync(),\n          {\n            title: '正在同步数据...',\n            id: 'wish_manual_sync',\n            timeout: 15000,\n            silent: silent,\n            showSuccess: !silent && this.syncStatus.lastSyncResult === 'success',\n            successTitle: '同步完成',\n            showError: !silent,\n            errorTitle: '同步失败'\n          }\n        )\n      } catch (error) {\n        console.error('[wishStore] Manual sync failed:', error);\n        if (!silent) {\n          throw error;\n        }\n      }\n    },\n    \n    // 重置同步状态\n    resetSyncStatus() {\n      this._updateSyncStatus({\n        issyncing: false,\n        lastSyncResult: null,\n        pendingCount: 0,\n        errorCount: 0,\n        lastError: null\n      });\n    },\n\n    // 🚀 新增：检查同步操作是否可以执行\n    _canStartSync(operation) {\n      const now = Date.now()\n\n      // 检查是否有相同操作正在进行\n      if (this._syncOperations.has(operation)) {\n        console.log(`[wishStore] 同步操作 ${operation} 已在进行中`)\n        return false\n      }\n\n      // 检查是否距离上次同步太近（防抖）\n      if (now - this._lastSyncTime < 1000) {\n        console.log(`[wishStore] 同步操作过于频繁，跳过`)\n        return false\n      }\n\n      return true\n    },\n\n    // 🚀 新增：开始同步操作\n    _startSyncOperation(operation) {\n      this._syncOperations.add(operation)\n      this._lastSyncTime = Date.now()\n      console.log(`[wishStore] 开始同步操作: ${operation}`)\n    },\n\n    // 🚀 新增：结束同步操作\n    _endSyncOperation(operation) {\n      this._syncOperations.delete(operation)\n      console.log(`[wishStore] 结束同步操作: ${operation}`)\n    },\n\n    // 🔧 新增：记录最近删除的心愿ID\n    _recordDeletedWish(wishId) {\n      if (!this._recentlyDeleted) {\n        this._recentlyDeleted = new Map()\n      }\n      this._recentlyDeleted.set(wishId, Date.now())\n\n      // 清理5分钟前的记录\n      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000\n      for (const [id, timestamp] of this._recentlyDeleted.entries()) {\n        if (timestamp < fiveMinutesAgo) {\n          this._recentlyDeleted.delete(id)\n        }\n      }\n    },\n\n    // 🔧 新增：获取最近删除的心愿ID列表\n    _getRecentlyDeletedWishIds() {\n      if (!this._recentlyDeleted) {\n        return []\n      }\n\n      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000\n      const recentIds = []\n\n      for (const [id, timestamp] of this._recentlyDeleted.entries()) {\n        if (timestamp >= fiveMinutesAgo) {\n          recentIds.push(id)\n        }\n      }\n\n      return recentIds\n    },\n    \n    // 从云端同步数据 - 🚀 增强防重复机制\n    async syncFromCloud() {\n      const operation = 'syncFromCloud'\n\n      // 🚀 检查是否可以开始同步\n      if (!this._canStartSync(operation)) {\n        return\n      }\n\n      const userStore = useUserStore() // Get user store instance\n      if (!userStore.isLogin) {\n        console.log('[store/wish.js syncFromCloud] User not logged in, skipping cloud sync.');\n        // 如果未登录，可以选择是否加载示例数据或清空列表\n        if (this.wishList.length === 0) {\n           // this._initExampleData() // 根据产品需求决定是否加载示例\n        }\n        return; // Do not proceed if not logged in\n      }\n\n      try {\n        this._startSyncOperation(operation)\n        this.isLoading = true\n        console.log('[wishStore] Starting intelligent sync...');\n\n        // 第一步：轻量级同步检查\n        const syncNeded = await this._checkSyncNeeded();\n\n        if (!syncNeded) {\n          console.log('[wishStore] Local data is up to date, no sync needed');\n          return;\n        }\n\n        // 第二步：如果需要同步，获取完整数据并进行智能合并\n        await this._performIntelligentSync();\n        \n      } catch (error) {\n        console.error('[wishStore] 智能同步失败:', error)\n        // 如果云端同步失败，且本地没有数据，使用示例数据\n        if (this.wishList.length === 0) {\n          console.log('[wishStore] Initializing example data due to sync failure...');\n          this._initExampleData()\n        }\n      } finally {\n        this.isLoading = false\n        this._endSyncOperation(operation)\n      }\n    },\n    \n    // 检查是否需要同步（轻量级调用）\n    async _checkSyncNeeded() {\n      // 临时特性开关：如果云端API未准备好，直接返回需要同步\n      const enableLightweightCheck = false; // TODO: 等云端API就绪后设为true\n      \n      if (!enableLightweightCheck) {\n        console.log('[wishStore] Lightweight sync check disabled, performing full sync');\n        return true;\n      }\n      \n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        \n        // 调用轻量级API，只获取同步摘要信息\n        const result = await wishCenter.getSyncSummary({\n          lastSyncTime: this.lastSyncTime,\n          localDataCount: this.wishList.length,\n          localLastModified: this._getLocalLastModified()\n        })\n        \n        if (result.errCode === 0) {\n          const { needFullSync, cloudLastModified, cloudDataCount } = result.data;\n          \n          // 比较时间戳和数据量来判断是否需要同步\n          const localLastModified = this._getLocalLastModified();\n          const needSync = needFullSync || \n                          !localLastModified || \n                          !cloudLastModified ||\n                          new Date(cloudLastModified) > new Date(localLastModified) ||\n                          cloudDataCount !== this.wishList.length;\n          \n          console.log('[wishStore] Sync check result:', {\n            needSync,\n            localLastModified,\n            cloudLastModified,\n            localCount: this.wishList.length,\n            cloudCount: cloudDataCount\n          });\n          \n          return needSync;\n        } else {\n          // 如果轻量级检查失败，默认进行同步\n          console.warn('[wishStore] Sync check failed, defaulting to sync');\n          return true;\n        }\n      } catch (error) {\n        console.error('[wishStore] Sync check error:', error);\n        // 网络错误时，不进行同步\n        if (ErrorHandler.isNetworkError(error)) {\n          return false;\n        }\n        // 其他错误时，进行同步\n        return true;\n      }\n    },\n    \n    // 获取本地数据的最后修改时间\n    _getLocalLastModified() {\n      if (this.wishList.length === 0) return null;\n      \n      const latestWish = this.wishList.reduce((latest, current) => {\n        const currentTime = new Date(current.updateDate || current.createDate);\n        const latestTime = new Date(latest.updateDate || latest.createDate);\n        return currentTime > latestTime ? current : latest;\n      });\n      \n      return latestWish.updateDate || latestWish.createDate;\n    },\n    \n    // 执行智能同步（合并数据而不是覆盖）\n    async _performIntelligentSync() {\n      console.log('[wishStore] Performing intelligent data merge...');\n      \n      const wishCenter = uniCloud.importObject('wish-center')\n      const result = await wishCenter.getWishList({\n        page: 1,\n        pageSize: 1000,\n        includeCompleted: true,\n        includeDeleted: true // 包含已删除的数据用于冲突解决\n      })\n      \n      if (result.errCode === 0) {\n        const cloudWishes = result.data || [];\n        console.log('[wishStore] Cloud data received:', cloudWishes.length, 'items');\n        \n        // 执行智能数据合并\n        const mergedData = this._mergeWishData(this.wishList, cloudWishes);\n        \n        // 更新本地数据\n        this.wishList = mergedData.map(wish => ({\n          ...wish,\n          id: wish._id // 确保有 id 字段供前端组件使用\n        }));\n        \n        this._clearWishesCache() // 清除缓存\n        this.lastSyncTime = new Date().toISOString()\n        this._saveToStorage()\n        \n        console.log('[wishStore] Intelligent sync completed, merged data:', this.wishList.length, 'items');\n      } else {\n        throw new Error(result.errMsg || '获取云端数据失败');\n      }\n    },\n    \n    // 智能合并本地和云端数据\n    _mergeWishData(localWishes, cloudWishes) {\n      console.log('[wishStore] Merging local and cloud data...');\n\n      const mergedMap = new Map();\n\n      // 🔧 获取最近删除的心愿ID列表（5分钟内删除的）\n      const recentlyDeletedIds = this._getRecentlyDeletedWishIds();\n\n      // 首先处理云端数据，但排除最近删除的心愿\n      cloudWishes.forEach(cloudWish => {\n        // 🔧 跳过最近删除的心愿，避免重新加入\n        if (recentlyDeletedIds.includes(cloudWish._id)) {\n          console.log(`[wishStore] 跳过最近删除的心愿: ${cloudWish.title}`);\n          return;\n        }\n\n        mergedMap.set(cloudWish._id, {\n          ...cloudWish,\n          _source: 'cloud'\n        });\n      });\n      \n      // 然后处理本地数据，根据时间戳决定是否覆盖\n      localWishes.forEach(localWish => {\n        const id = localWish._id || localWish.id;\n        const existingWish = mergedMap.get(id);\n        \n        if (!existingWish) {\n          // 本地独有的数据（可能是离线创建的）\n          mergedMap.set(id, {\n            ...localWish,\n            _source: 'local',\n            _needSync: true // 标记需要同步到云端\n          });\n        } else {\n          // 存在冲突，比较时间戳\n          const localTime = new Date(localWish.updateDate || localWish.createDate);\n          const cloudTime = new Date(existingWish.updateDate || existingWish.createDate);\n          \n          if (localTime > cloudTime) {\n            // 本地数据更新，使用本地数据\n            mergedMap.set(id, {\n              ...localWish,\n              _source: 'local-newer',\n              _needSync: true // 需要同步到云端\n            });\n          } else if (localTime < cloudTime) {\n            // 云端数据更新，使用云端数据\n            // 保持云端数据，无需额外处理\n          } else {\n            // 时间戳相同，检查内容是否有差异\n            if (this._hasContentDifference(localWish, existingWish)) {\n              // 内容有差异但时间戳相同，优先使用云端数据\n            }\n          }\n        }\n      });\n      \n      // 转换为数组并过滤已删除的项目\n      const mergedArray = Array.from(mergedMap.values())\n        .filter(wish => !wish._deleted)\n        .sort((a, b) => (a.order || 0) - (b.order || 0));\n      \n      console.log('[wishStore] Data merge completed:', {\n        localCount: localWishes.length,\n        cloudCount: cloudWishes.length,\n        mergedCount: mergedArray.length\n      });\n      \n      return mergedArray;\n    },\n    \n    // 检查两个心愿对象是否有内容差异\n    _hasContentDifference(wish1, wish2) {\n      const keys = ['title', 'description', 'image', 'video', 'audio', 'groupIds', 'permission'];\n      \n      return keys.some(key => {\n        const val1 = wish1[key];\n        const val2 = wish2[key];\n        \n        // 对于数组，需要深度比较\n        if (Array.isArray(val1) && Array.isArray(val2)) {\n          return JSON.stringify(val1.sort()) !== JSON.stringify(val2.sort());\n        }\n        \n        return val1 !== val2;\n      });\n    },\n    \n    // 初始化示例数据\n    _initExampleData() {\n        this.wishList = [\n          {\n          _id: '1',\n          id: '1', // 添加 id 字段以保持兼容性\n            title: '买一台新笔记本电脑',\n            description: '想要一台性能好的笔记本，用于工作和娱乐',\n          image: [],\n          video: [],\n          audio: [],\n            createDate: new Date().toISOString(),\n          updateDate: new Date().toISOString(),\n            startDate: null,\n            completeDate: null,\n            isCompleted: false,\n            permission: 'friends',\n            groupIds: ['all', 'gift', 'friend-visible'],\n            order: 1,\n          groupOrder: 1\n          },\n          {\n          _id: '2',\n          id: '2', // 添加 id 字段以保持兼容性\n            title: '去日本旅行',\n            description: '计划明年去日本旅行，看樱花',\n          image: [],\n          video: [],\n          audio: [],\n            createDate: new Date().toISOString(),\n          updateDate: new Date().toISOString(),\n            startDate: null,\n            completeDate: null,\n            isCompleted: false,\n            permission: 'private',\n            groupIds: ['all'],\n            order: 2,\n          groupOrder: 1\n          },\n          {\n          _id: '3',\n          id: '3', // 添加 id 字段以保持兼容性\n            title: '学习一门新语言',\n            description: '学习法语，已经完成了初级课程',\n          image: [],\n          video: [],\n          audio: [],\n          createDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),\n          updateDate: new Date().toISOString(),\n            startDate: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),\n          completeDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\n            isCompleted: true,\n            permission: 'friends',\n            groupIds: ['all', 'friend-visible'],\n            order: 3,\n          groupOrder: 1\n          }\n        ]\n        this._saveToStorage()\n    },\n    \n    // 从本地存储刷新心愿列表数据\n    refreshWishList() {\n      const storedWishes = uni.getStorageSync('wishList')\n      if (storedWishes) {\n        const parsed = JSON.parse(storedWishes)\n        // 确保每个心愿对象都有 id 字段\n        this.wishList = parsed.map(wish => ({\n          ...wish,\n          id: wish.id || wish._id // 如果没有 id 字段，使用 _id\n        }))\n      }\n    },\n    \n    // 设置当前分组\n    setCurrentGroup(groupId) {\n      this.currentGroupId = groupId\n      // 清除缓存，因为分组变化了\n      this._clearWishesCache()\n    },\n    \n    // 清除心愿列表缓存（内部方法） - 已废弃，保留以防兼容性问题\n    _clearWishesCache() {\n      // 缓存机制已移除\n    },\n    \n    // 添加心愿\n    async addWish(wishData) {\n      try {\n        // 添加时间戳\n        const timestamp = Date.now();\n        const enhancedWishData = {\n          ...wishData,\n          timestamp,\n          deviceId: uni.getSystemInfoSync().deviceId || 'unknown'\n        };\n\n        // 检查网络状态\n        if (!this.isOnline) {\n          console.log('当前离线，心愿将保存到本地')\n          return this._addWishOffline(enhancedWishData)\n        }\n\n        // 先调用云函数创建\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.createWish(enhancedWishData)\n\n        if (result.errCode === 0) {\n          // 云端创建成功，添加到本地列表\n          const wishWithId = {\n            ...result.data,\n            id: result.data._id // 确保有 id 字段供前端组件使用\n          }\n          this.wishList.push(wishWithId)\n          this._clearWishesCache() // 清除缓存\n          this._saveToStorage()\n\n          // 立即触发响应式更新\n          this._triggerReactiveUpdate('add', wishWithId._id)\n\n          return wishWithId\n        } else {\n          throw new Error(result.errMsg || '创建心愿失败')\n        }\n      } catch (error) {\n        console.error('添加心愿失败:', error)\n\n        // 网络错误时，保存到本地（离线模式）\n        if (ErrorHandler.isNetworkError(error)) {\n          this.isOnline = false // 更新网络状态\n          return this._addWishOffline(enhancedWishData)\n        }\n\n        // 🚀 优化：使用统一的版本冲突处理\n        if (ErrorHandler.isVersionConflictError(error)) {\n          ConflictManager.handleVersionConflict(error, 'create_wish')\n          // 冲突时使用离线模式保存\n          return this._addWishOffline(enhancedWishData)\n        }\n\n        // 其他错误，显示错误信息并抛出\n        ErrorHandler.showError(error, '创建心愿失败', 'create_wish')\n        throw error\n      }\n    },\n    \n    // 🚀 离线添加心愿 - 支持逻辑时钟\n    _addWishOffline(wishData) {\n      const wishId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n\n      const wish = {\n        _id: wishId,\n        id: wishId, // 同时添加 id 字段以保持兼容性\n        title: wishData.title || '',\n        description: wishData.description || '',\n        image: Array.isArray(wishData.image) ? wishData.image : (wishData.image ? [wishData.image] : []),\n        video: Array.isArray(wishData.video) ? wishData.video : (wishData.video ? [wishData.video] : []),\n        audio: Array.isArray(wishData.audio) ? wishData.audio : (wishData.audio ? [wishData.audio] : []),\n        createDate: new Date().toISOString(),\n        updateDate: new Date().toISOString(),\n        startDate: wishData.startDate || null,\n        completeDate: null,\n        isCompleted: false,\n        permission: wishData.permission || 'private',\n        groupIds: wishData.groupIds || ['all'],\n        order: this.wishList.length + 1,\n        groupOrder: this._getNextGroupOrder(wishData.groupIds || ['all']),\n        _needSync: true, // 标记需要同步到云端\n\n        // 时间戳支持\n        timestamp: wishData.timestamp || Date.now(),\n        lastModifiedDevice: wishData.deviceId || uni.getSystemInfoSync().deviceId || 'unknown',\n        version: 1\n      }\n\n      // 确保groupIds包含'all'\n      if (!wish.groupIds.includes('all')) {\n        wish.groupIds.push('all')\n      }\n\n      // 根据权限设置分组\n      if (wish.permission === 'friends' && !wish.groupIds.includes('friend-visible')) {\n          wish.groupIds.push('friend-visible')\n      }\n\n      this.wishList.push(wish)\n      this._clearWishesCache() // 清除缓存\n      this._saveToStorage()\n\n      // 立即触发响应式更新\n      this._triggerReactiveUpdate('add', wish._id)\n\n      // 显示离线提示\n      uni.showToast({\n        title: this.isOnline ? '已保存到本地，稍后将同步到云端' : '当前离线，已保存到本地',\n        icon: 'none',\n        duration: 2500\n      })\n\n      return wish\n    },\n    \n    // 获取分组内下一个排序号\n    _getNextGroupOrder(groupIds) {\n      if (!groupIds || groupIds.length === 0) return 1\n      \n      // 获取这些分组中的最大groupOrder\n      const maxOrder = this.wishList\n        .filter(wish => {\n          return groupIds.some(groupId => wish.groupIds && wish.groupIds.includes(groupId))\n        })\n        .reduce((max, wish) => Math.max(max, wish.groupOrder || 0), 0)\n      \n      return maxOrder + 1\n    },\n    \n    // 更新心愿\n    async updateWish(updatedWish) {\n      console.log('[wishStore] updateWish called with:', updatedWish);\n      \n      try {\n        // 验证更新数据的完整性\n        if (!updatedWish || (!updatedWish._id && !updatedWish.id)) {\n          throw new Error('更新数据缺少必要的ID字段');\n        }\n        \n        // 检查网络状态\n        if (!this.isOnline) {\n          console.log('当前离线，心愿将保存到本地')\n          this._updateLocalWish(updatedWish)\n\n          // 标记需要同步\n          const index = this.wishList.findIndex(w => w._id === updatedWish._id || w.id === updatedWish.id)\n          if (index !== -1) {\n            this.wishList[index]._needSync = true\n          }\n\n          // 立即触发响应式更新\n          this._triggerReactiveUpdate('update', updatedWish._id || updatedWish.id)\n\n          uni.showToast({\n            title: '当前离线，已保存到本地',\n            icon: 'none',\n            duration: 2500\n          })\n          return\n        }\n        \n        // 🔧 准备更新数据，确保版本号和时间戳正确\n        const updateData = {\n          ...updatedWish,\n          updateDate: new Date().toISOString(), // 设置当前时间戳\n          version: (updatedWish.version || 1) + 1, // 递增版本号\n          _forceUpdate: false // 不强制更新，允许冲突检测\n        }\n\n        // 先更新云端\n        const wishCenter = uniCloud.importObject('wish-center')\n        console.log('[wishStore] Calling cloud updateWish with ID:', updatedWish._id || updatedWish.id);\n        const result = await wishCenter.updateWish(updatedWish._id || updatedWish.id, updateData)\n        \n        if (result.errCode === 0) {\n          // 云端更新成功，更新本地数据\n          console.log('[wishStore] Cloud update successful, updating local data');\n\n          // 🔧 重要：使用云端返回的最新数据，包含正确的版本号\n          if (result.data) {\n            this._updateLocalWish(result.data)\n          } else {\n            // 如果云端没有返回数据，手动更新版本号\n            updatedWish.version = (updatedWish.version || 1) + 1\n            this._updateLocalWish(updatedWish)\n          }\n\n          // 立即触发响应式更新\n          this._triggerReactiveUpdate('update', updatedWish._id || updatedWish.id)\n        } else {\n          throw new Error(result.errMsg || '更新心愿失败')\n        }\n      } catch (error) {\n        console.error('更新心愿失败:', error)\n        \n        // 网络错误时，先更新本地（离线模式）\n        if (ErrorHandler.isNetworkError(error)) {\n          console.log('[wishStore] Network error, saving locally');\n          this.isOnline = false\n          this._updateLocalWish(updatedWish)\n\n          // 标记需要同步\n          const index = this.wishList.findIndex(w => w._id === updatedWish._id || w.id === updatedWish.id)\n          if (index !== -1) {\n            this.wishList[index]._needSync = true\n          }\n\n          // 立即触发响应式更新\n          this._triggerReactiveUpdate('update', updatedWish._id || updatedWish.id)\n          \n          uni.showToast({\n            title: '网络连接失败，已保存到本地',\n            icon: 'none',\n            duration: 2500\n          })\n        } else if (error.message && (\n          error.message.includes('心愿不存在') || \n          error.message.includes('无权限') ||\n          error.message.includes('not found') ||\n          error.message.includes('permission')\n        )) {\n          // 多设备冲突处理 - 心愿可能已被其他设备删除\n          console.log(`[wishStore] 多设备更新冲突: 心愿可能已被其他设备删除，移除本地记录`);\n          const index = this.wishList.findIndex(w => w._id === updatedWish._id || w.id === updatedWish.id);\n          if (index !== -1) {\n            this.wishList.splice(index, 1);\n            // 重新计算序号\n            this._reorderAfterDelete();\n            this._saveToStorage();\n          }\n          \n          uni.showToast({\n            title: '心愿已不存在',\n            icon: 'none',\n            duration: 1500\n          });\n        } else if (ErrorHandler.isVersionConflictError(error)) {\n          // 🚀 优化：使用统一的版本冲突处理\n          ConflictManager.handleVersionConflict(error, 'update_wish')\n          // 冲突时使用本地数据，标记需要同步\n          const index = this.wishList.findIndex(w => w._id === updatedWish._id || w.id === updatedWish.id)\n          if (index !== -1) {\n            this.wishList[index]._needSync = true\n          }\n        } else {\n          // 其他错误，显示错误信息\n          ErrorHandler.showError(error, '更新心愿失败', 'update_wish')\n          throw error\n        }\n      }\n    },\n    \n    // 更新本地心愿数据\n    _updateLocalWish(updatedWish) {\n      console.log('[wishStore] _updateLocalWish called with:', updatedWish);\n      \n      const index = this.wishList.findIndex(w => w._id === updatedWish._id || w.id === updatedWish.id)\n      console.log('[wishStore] Found wish at index:', index);\n      \n      if (index !== -1) {\n        const originalWish = this.wishList[index]\n        console.log('[wishStore] Original wish:', originalWish);\n        \n        // 合并数据，确保使用正确的字段名和默认值\n        const mergedWish = {\n          ...originalWish,\n          ...updatedWish,\n          updateDate: new Date().toISOString(),\n          // 确保ID字段兼容性\n          id: updatedWish.id || updatedWish._id || originalWish.id || originalWish._id,\n          _id: updatedWish._id || updatedWish.id || originalWish._id || originalWish.id,\n          // 确保必要字段有默认值\n          permission: updatedWish.permission || originalWish.permission || 'private',\n          groupIds: updatedWish.groupIds || originalWish.groupIds || ['all'],\n          tags: updatedWish.tags || originalWish.tags || [],\n          title: updatedWish.title || originalWish.title || '',\n          description: updatedWish.description || originalWish.description || ''\n        }\n        \n        console.log('[wishStore] Merged wish:', mergedWish);\n        \n        // 确保多媒体字段为数组\n        ['image', 'video', 'audio'].forEach(field => {\n          if (mergedWish[field] && !Array.isArray(mergedWish[field])) {\n            mergedWish[field] = [mergedWish[field]]\n          } else if (!mergedWish[field]) {\n            mergedWish[field] = []\n          }\n        })\n          \n        // 权限变更时更新分组\n        if (updatedWish.hasOwnProperty('permission')) {\n          console.log('[wishStore] Permission change detected:', updatedWish.permission);\n          \n          // 确保 groupIds 是数组\n          if (!Array.isArray(mergedWish.groupIds)) {\n            console.log('[wishStore] groupIds is not array, setting default');\n            mergedWish.groupIds = ['all']\n          }\n          \n          if (mergedWish.permission === 'friends') {\n            if (!mergedWish.groupIds.includes('friend-visible')) {\n              mergedWish.groupIds.push('friend-visible')\n            }\n          } else {\n            mergedWish.groupIds = mergedWish.groupIds.filter(id => id !== 'friend-visible')\n            if (mergedWish.groupIds.length === 0) {\n              mergedWish.groupIds.push('all')\n            }\n          }\n        }\n        \n        console.log('[wishStore] Final merged wish:', mergedWish);\n        this.wishList[index] = mergedWish\n        this._saveToStorage()\n      } else {\n        console.error('[wishStore] Could not find wish to update with ID:', updatedWish._id || updatedWish.id);\n      }\n    },\n    \n    // 删除心愿\n    async deleteWish(id) {\n      try {\n        // 检查网络状态\n        if (!this.isOnline) {\n          console.log('当前离线，心愿删除将保存到本地')\n          // 先备份心愿数据，以防需要恢复\n          const wish = this.wishList.find(w => w._id === id || w.id === id)\n          if (wish) {\n            wish._deleted = true\n            wish._needSync = true\n            wish.updateDate = new Date().toISOString()\n          }\n          \n          // 数据已标记为需要同步，等待网络恢复后自动同步\n          \n          uni.showToast({\n            title: '当前离线，删除操作已保存',\n            icon: 'none',\n            duration: 2500\n          })\n          return\n        }\n        \n        // 先删除云端\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.deleteWish(id)\n        \n        if (result.errCode === 0) {\n          // 云端删除成功，删除本地数据\n          this._deleteLocalWish(id)\n\n          // 🔧 立即更新同步时间，避免下拉刷新时重新获取已删除的数据\n          this.lastSyncTime = new Date().toISOString()\n          this._saveToStorage()\n\n          // 数据变化通知已通过云函数推送处理\n        } else {\n          throw new Error(result.errMsg || '删除心愿失败')\n        }\n      } catch (error) {\n        // 特殊处理：多设备同步冲突（心愿可能已被其他设备删除）\n        if (error.message && (\n          error.message.includes('心愿不存在') || \n          error.message.includes('无权限') ||\n          error.message.includes('not found') ||\n          error.message.includes('permission')\n        )) {\n          // 多设备冲突处理 - 不显示错误，静默处理\n          console.log(`[wishStore] 多设备删除冲突: 心愿可能已被其他设备删除，直接清理本地数据`);\n          this._deleteLocalWish(id);\n          \n          uni.showToast({\n            title: '心愿已删除',\n            icon: 'success',\n            duration: 1500\n          });\n          return;\n        }\n        \n        console.error('删除心愿失败:', error)\n        \n        // 网络错误时，标记为删除（离线模式）\n        if (ErrorHandler.isNetworkError(error)) {\n          this.isOnline = false\n          const wish = this.wishList.find(w => w._id === id || w.id === id)\n          if (wish) {\n            wish._deleted = true\n            wish._needSync = true\n            wish.updateDate = new Date().toISOString()\n          }\n          \n          // 数据已标记为需要同步，等待网络恢复后自动同步\n          \n          uni.showToast({\n            title: '网络连接失败，删除操作已保存',\n            icon: 'none',\n            duration: 2500\n          })\n        } else {\n          // 其他错误，显示错误信息\n          ErrorHandler.showError(error, '删除心愿失败')\n          throw error\n        }\n      }\n    },\n    \n    // 删除本地心愿数据\n    _deleteLocalWish(id) {\n      console.log(`[wishStore] 删除心愿 ${id}`);\n\n      // 🔧 记录删除的心愿ID，避免下次同步时重新加入\n      this._recordDeletedWish(id)\n\n      // 找到要删除的项目索引\n      const index = this.wishList.findIndex(wish => wish._id === id || wish.id === id)\n\n      if (index !== -1) {\n        // 使用splice原地删除，保持数组引用不变，让Vue更好地追踪变化\n        this.wishList.splice(index, 1)\n\n        console.log(`[wishStore] 删除后剩余心愿数量: ${this.wishList.length}`);\n\n        // 重新计算序号\n        this._reorderAfterDelete()\n\n        // 强制触发响应式更新\n        this.listUpdateCounter++\n\n        // 保存到本地存储\n        this._saveToStorage()\n\n        // 立即触发响应式更新\n        this._triggerReactiveUpdate('delete', id)\n\n        console.log(`[wishStore] 心愿删除完成，序号已更新`);\n      } else {\n        console.warn(`[wishStore] 未找到要删除的心愿: ${id}`);\n      }\n    },\n\n    // 删除后重新计算序号的统一方法\n    _reorderAfterDelete() {\n        // 先按原序号排序，确保顺序正确\n        this.wishList.sort((a, b) => (a.order || 0) - (b.order || 0))\n        \n        // 重新分配连续的序号\n        this.wishList.forEach((wish, index) => {\n          wish.order = index + 1;\n          wish.updateDate = new Date().toISOString();\n        })\n        \n        console.log(`[wishStore] 序号重新计算完成，共${this.wishList.length}个心愿`);\n    },\n    \n    // 完成心愿\n    async completeWish(id) {\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.completeWish(id)\n        \n        if (result.errCode === 0) {\n          // 云端操作成功，更新本地数据\n          const wish = this.wishList.find(w => w._id === id || w.id === id)\n          if (wish) {\n            wish.isCompleted = true\n            wish.completeDate = new Date().toISOString()\n            wish.updateDate = new Date().toISOString()\n            // 🔧 重要：同步云端的版本号，避免后续冲突\n            if (result.data && result.data.version) {\n              wish.version = result.data.version\n            } else {\n              // 如果云端没有返回版本号，手动递增\n              wish.version = (wish.version || 1) + 1\n            }\n            this._saveToStorage()\n\n            // 立即触发响应式更新\n            this._triggerReactiveUpdate('complete', id)\n          }\n        } else {\n          throw new Error(result.errMsg || '完成心愿失败')\n        }\n      } catch (error) {\n        // 特殊处理：多设备同步冲突（心愿可能已被其他设备删除）\n        if (error.message && (\n          error.message.includes('心愿不存在') || \n          error.message.includes('无权限') ||\n          error.message.includes('not found') ||\n          error.message.includes('permission')\n        )) {\n          // 多设备冲突处理 - 心愿可能已被其他设备删除\n          console.log(`[wishStore] 多设备完成冲突: 心愿可能已被其他设备删除，移除本地记录`);\n          const index = this.wishList.findIndex(w => w._id === id || w.id === id);\n          if (index !== -1) {\n            this.wishList.splice(index, 1);\n            // 重新计算序号\n            this._reorderAfterDelete();\n            this._saveToStorage();\n          }\n          return;\n        }\n        \n        console.error('完成心愿失败:', error)\n        \n        // 云端失败时，先更新本地\n        const wish = this.wishList.find(w => w._id === id || w.id === id)\n        if (wish) {\n          wish.isCompleted = true\n          wish.completeDate = new Date().toISOString()\n          wish.updateDate = new Date().toISOString()\n          wish._needSync = true\n          this._saveToStorage()\n\n          // 立即触发响应式更新\n          this._triggerReactiveUpdate('complete', id)\n        }\n      }\n    },\n    \n    // 恢复心愿\n    async restoreWish(id) {\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.restoreWish(id)\n        \n        if (result.errCode === 0) {\n          // 云端操作成功，更新本地数据\n          const wish = this.wishList.find(w => w._id === id || w.id === id)\n          if (wish && wish.isCompleted) {\n            wish.isCompleted = false\n            wish.completeDate = null\n            wish.updateDate = new Date().toISOString()\n            // 🔧 重要：同步云端的版本号，避免后续冲突\n            if (result.data && result.data.version) {\n              wish.version = result.data.version\n            } else {\n              // 如果云端没有返回版本号，手动递增\n              wish.version = (wish.version || 1) + 1\n            }\n            this._saveToStorage()\n\n            // 立即触发响应式更新\n            this._triggerReactiveUpdate('restore', id)\n\n            return true\n          }\n        } else {\n          throw new Error(result.errMsg || '恢复心愿失败')\n        }\n      } catch (error) {\n        // 特殊处理：多设备同步冲突（心愿可能已被其他设备删除）\n        if (error.message && (\n          error.message.includes('心愿不存在') || \n          error.message.includes('无权限') ||\n          error.message.includes('not found') ||\n          error.message.includes('permission')\n        )) {\n          // 多设备冲突处理 - 心愿可能已被其他设备删除\n          console.log(`[wishStore] 多设备恢复冲突: 心愿可能已被其他设备删除，移除本地记录`);\n          const index = this.wishList.findIndex(w => w._id === id || w.id === id);\n          if (index !== -1) {\n            this.wishList.splice(index, 1);\n            // 重新计算序号\n            this._reorderAfterDelete();\n            this._saveToStorage();\n          }\n          return false;\n        }\n        \n        console.error('恢复心愿失败:', error)\n        \n        // 云端失败时，先更新本地\n        const wish = this.wishList.find(w => w._id === id || w.id === id)\n        if (wish && wish.isCompleted) {\n          wish.isCompleted = false\n          wish.completeDate = null\n          wish.updateDate = new Date().toISOString()\n          wish._needSync = true\n        this._saveToStorage()\n          return true\n        }\n      }\n      return false\n    },\n    \n    // 更新心愿排序（支持分组内排序）\n    async updateWishOrder(orderedIds, groupId = 'all') {\n      if (!orderedIds || orderedIds.length === 0) return\n      \n      try {\n        // 先更新云端\n        const wishCenter = uniCloud.importObject('wish-center')\n        const sortType = groupId === 'all' ? 'global' : 'group'\n        const result = await wishCenter.updateWishOrder(orderedIds, sortType, groupId)\n        \n        if (result.errCode === 0) {\n          // 云端更新成功，更新本地排序\n          this._updateLocalOrder(orderedIds, groupId)\n          \n          // 数据变化通知已通过云函数推送处理\n        } else {\n          throw new Error(result.errMsg || '更新排序失败')\n        }\n      } catch (error) {\n        console.error('更新排序失败:', error)\n        \n        // 云端失败时，先更新本地\n        this._updateLocalOrder(orderedIds, groupId)\n      }\n    },\n    \n    // 更新本地排序\n    _updateLocalOrder(orderedIds, groupId) {\n      // 更新分组内排序\n      orderedIds.forEach((id, index) => {\n        const wish = this.wishList.find(w => w._id === id || w.id === id)\n        if (wish) {\n          if (groupId === 'all') {\n            wish.order = index + 1\n          } else {\n            wish.groupOrder = index + 1\n          }\n          wish.updateDate = new Date().toISOString()\n          wish._needSync = true\n        }\n      })\n      \n      // 排序列表\n      this.wishList.sort((a, b) => {\n        if (a.isCompleted !== b.isCompleted) {\n          return a.isCompleted ? 1 : -1\n        }\n        return (a.order || 0) - (b.order || 0)\n      })\n      \n      this._saveToStorage()\n      console.log('心愿排序已更新并保存')\n    },\n    \n    // 临时设置当前列表的顺序，用于拖拽排序过程中的实时显示\n    setCurrentList(newList) {\n      if (!newList || newList.length === 0) return\n      \n      // 创建ID映射\n      const idToWishMap = new Map()\n      this.wishList.forEach(wish => {\n        const key = wish._id || wish.id\n        idToWishMap.set(key, wish)\n      })\n      \n      if (this.currentGroupId === 'all') {\n        // 全部分组：更新全局顺序\n        const reorderedWishes = []\n        const completedWishes = this.wishList.filter(wish => wish.isCompleted)\n        \n        newList.forEach(wish => {\n          if (idToWishMap.has(wish._id || wish.id)) {\n            reorderedWishes.push(idToWishMap.get(wish._id || wish.id))\n          }\n        })\n        \n        const unorderedWishes = this.wishList.filter(wish => \n          !wish.isCompleted && !reorderedWishes.some(w => (w._id || w.id) === (wish._id || wish.id))\n        )\n        \n        this.wishList = [...reorderedWishes, ...unorderedWishes, ...completedWishes]\n      } else {\n        // 特定分组：更新分组内顺序\n        const groupWishes = this.wishList.filter(wish => \n          wish.groupIds && wish.groupIds.includes(this.currentGroupId) && !wish.isCompleted\n        )\n        \n        const otherWishes = this.wishList.filter(wish => \n          !wish.groupIds || !wish.groupIds.includes(this.currentGroupId) || wish.isCompleted\n        )\n        \n        const reorderedGroupWishes = []\n        newList.forEach(wish => {\n          const key = wish._id || wish.id\n          if (idToWishMap.has(key)) {\n            reorderedGroupWishes.push(idToWishMap.get(key))\n          }\n        })\n        \n        const unorderedGroupWishes = groupWishes.filter(wish => \n          !reorderedGroupWishes.some(w => (w._id || w.id) === (wish._id || wish.id))\n        )\n        \n        this.wishList = [...reorderedGroupWishes, ...unorderedGroupWishes, ...otherWishes]\n      }\n    },\n    \n    // 同步需要上传的本地数据到云端\n    async syncPendingData() {\n      const pendingWishes = this.wishList.filter(wish => wish._needSync)\n      let syncedCount = 0\n      let failedCount = 0\n      \n      if (pendingWishes.length === 0) {\n        console.log('[wishStore] No pending data to sync');\n        return { syncedCount: 0, failedCount: 0 };\n      }\n      \n      console.log(`[wishStore] Syncing ${pendingWishes.length} pending wishes...`);\n      \n      for (const wish of pendingWishes) {\n        try {\n          const wishCenter = uniCloud.importObject('wish-center')\n          \n          if (wish._deleted) {\n            // 处理删除操作\n            const result = await wishCenter.deleteWish(wish._id)\n            if (result.errCode === 0) {\n              // 删除成功，从本地移除\n              this.wishList = this.wishList.filter(w => w._id !== wish._id && w.id !== wish.id)\n              syncedCount++\n              console.log(`[wishStore] Deleted wish synced: ${wish.title}`);\n            } else {\n              throw new Error(result.errMsg || '删除心愿失败')\n            }\n          } else if (wish._id.startsWith('temp_')) {\n            // 临时ID，需要创建\n            const result = await wishCenter.createWish(wish)\n            if (result.errCode === 0) {\n              // 更新本地ID\n              const oldId = wish._id;\n              wish._id = result.data._id;\n              wish.id = result.data._id;\n              delete wish._needSync;\n              syncedCount++;\n              console.log(`[wishStore] New wish synced: ${wish.title} (${oldId} -> ${wish._id})`);\n            } else if (result.errCode === 409) {\n              // 冲突：云端已有相同数据，使用云端数据\n              console.log(`[wishStore] Conflict detected for new wish: ${wish.title}, using cloud data`);\n              const cloudWish = result.data;\n              Object.assign(wish, cloudWish);\n              delete wish._needSync;\n              syncedCount++;\n            } else {\n              throw new Error(result.errMsg || '创建心愿失败')\n            }\n          } else {\n            // 已有ID，需要更新\n            const result = await wishCenter.updateWish(wish._id, wish)\n            if (result.errCode === 0) {\n              delete wish._needSync\n              syncedCount++\n              console.log(`[wishStore] Updated wish synced: ${wish.title}`);\n            } else if (result.errCode === 409) {\n              // 冲突：云端数据已被其他设备修改 - 静默处理\n              console.log(`[wishStore] Update conflict for wish: ${wish.title}, using cloud data silently`);\n\n              // 🔧 直接使用云端数据，不显示冲突弹窗\n              if (result.data) {\n                Object.assign(wish, result.data);\n                delete wish._needSync;\n                syncedCount++;\n                console.log(`[wishStore] Conflict resolved silently: cloud wins for ${wish.title}`);\n              } else {\n                // 如果没有云端数据，标记为失败但不显示错误\n                console.log(`[wishStore] Conflict resolution failed for ${wish.title}, will retry later`);\n                failedCount++;\n              }\n            } else {\n              throw new Error(result.errMsg || '更新心愿失败')\n            }\n          }\n          \n          // 每同步一个项目后稍作延迟，避免请求过于频繁\n          await new Promise(resolve => setTimeout(resolve, 100));\n          \n        } catch (error) {\n          // 特殊处理：心愿不存在或无权限操作（通常是多设备同步冲突）\n          if (error.message && (\n            error.message.includes('心愿不存在') || \n            error.message.includes('无权限') ||\n            error.message.includes('not found') ||\n            error.message.includes('permission')\n          )) {\n            // 多设备冲突处理 - 不显示错误，只输出信息日志\n            console.log(`[wishStore] 多设备同步冲突处理: 心愿 \"${wish.title}\" 在云端不存在，可能已被其他设备删除`);\n            \n            // 移除本地的同步标记，避免重复尝试\n            delete wish._needSync;\n            \n            // 如果心愿在云端不存在，从本地也移除\n            const localIndex = this.wishList.findIndex(w => w._id === wish._id);\n            if (localIndex !== -1) {\n              console.log(`[wishStore] 清理本地已删除的心愿: ${wish.title}`);\n              this.wishList.splice(localIndex, 1);\n            }\n            \n            // 这种情况不算失败，算是成功处理了冲突\n            syncedCount++;\n          } else {\n            // 其他类型的错误才输出错误日志\n          console.error('同步心愿失败:', wish.title, error)\n            failedCount++;\n          \n          // 如果是网络错误，停止同步\n          if (ErrorHandler.isNetworkError(error)) {\n            this.isOnline = false\n            console.log('网络错误，停止同步')\n            break\n            }\n          }\n        }\n      }\n        \n      // 保存到本地存储\n      this._saveToStorage()\n      \n      // 显示同步结果\n      if (syncedCount > 0) {\n        console.log(`[wishStore] 同步成功: ${syncedCount}个心愿`)\n      }\n      if (failedCount > 0) {\n        console.log(`[wishStore] 同步失败: ${failedCount}个心愿`)\n      }\n      \n      return { syncedCount, failedCount }\n    },\n    \n    // 解决更新冲突\n    async _resolveUpdateConflict(localWish, cloudWish) {\n      console.log(`[wishStore] Resolving conflict for: ${localWish.title}`);\n      \n      const localTime = new Date(localWish.updateDate || localWish.createDate);\n      const cloudTime = new Date(cloudWish.updateDate || cloudWish.createDate);\n      \n      if (localTime > cloudTime) {\n        // 本地数据更新，强制更新云端\n        try {\n          const wishCenter = uniCloud.importObject('wish-center');\n          const result = await wishCenter.forceUpdateWish(localWish._id, {\n            ...localWish,\n            _forceUpdate: true,\n            _conflictResolution: 'local-wins'\n          });\n          \n          if (result.errCode === 0) {\n            delete localWish._needSync;\n            console.log(`[wishStore] Conflict resolved: local wins for ${localWish.title}`);\n            return true;\n          }\n        } catch (error) {\n          console.error('强制更新失败:', error);\n        }\n      } else {\n        // 云端数据更新，使用云端数据\n        console.log(`[wishStore] Conflict resolved: cloud wins for ${cloudWish.title}`);\n        Object.assign(localWish, cloudWish);\n        delete localWish._needSync;\n        return true;\n      }\n      \n      return false;\n    },\n    \n    // 增量同步：只同步变更的数据\n    async performIncrementalSync() {\n      if (!this.lastSyncTime) {\n        console.log('[wishStore] No last sync time, performing full sync');\n        return await this.syncFromCloud();\n      }\n      \n      try {\n        const wishCenter = uniCloud.importObject('wish-center');\n        const result = await wishCenter.getIncrementalChanges({\n          since: this.lastSyncTime,\n          includeDeleted: true\n        });\n        \n        if (result.errCode === 0) {\n          const { additions, updates, deletions } = result.data;\n          let changesApplied = 0;\n          \n          // 处理新增的心愿\n          if (additions && additions.length > 0) {\n            additions.forEach(cloudWish => {\n              const existingIndex = this.wishList.findIndex(w => w._id === cloudWish._id);\n              if (existingIndex === -1) {\n                this.wishList.push({\n                  ...cloudWish,\n                  id: cloudWish._id\n                });\n                changesApplied++;\n                console.log(`[wishStore] Added new wish from cloud: ${cloudWish.title}`);\n              }\n            });\n          }\n          \n          // 处理更新的心愿\n          if (updates && updates.length > 0) {\n            updates.forEach(cloudWish => {\n              const existingIndex = this.wishList.findIndex(w => w._id === cloudWish._id);\n              if (existingIndex !== -1) {\n                const localWish = this.wishList[existingIndex];\n                const localTime = new Date(localWish.updateDate || localWish.createDate);\n                const cloudTime = new Date(cloudWish.updateDate || cloudWish.createDate);\n                \n                // 只有云端数据更新时才应用更改\n                if (cloudTime > localTime && !localWish._needSync) {\n                  this.wishList[existingIndex] = {\n                    ...cloudWish,\n                    id: cloudWish._id\n                  };\n                  changesApplied++;\n                  console.log(`[wishStore] Updated wish from cloud: ${cloudWish.title}`);\n                } else {\n                  console.log(`[wishStore] Skipped cloud update for ${cloudWish.title} (local is newer or has pending changes)`);\n                }\n              }\n            });\n          }\n          \n          // 处理删除的心愿\n          if (deletions && deletions.length > 0) {\n            deletions.forEach(deletedId => {\n              const existingIndex = this.wishList.findIndex(w => w._id === deletedId);\n              if (existingIndex !== -1) {\n                const removedWish = this.wishList[existingIndex];\n                this.wishList.splice(existingIndex, 1);\n                changesApplied++;\n                console.log(`[wishStore] Removed deleted wish: ${removedWish.title}`);\n              }\n            });\n          }\n          \n          if (changesApplied > 0) {\n            this.lastSyncTime = new Date().toISOString();\n            this._saveToStorage();\n            console.log(`[wishStore] Incremental sync completed: ${changesApplied} changes applied`);\n          } else {\n            console.log('[wishStore] No changes to apply from incremental sync');\n          }\n          \n          return { changesApplied };\n        } else {\n          throw new Error(result.errMsg || '增量同步失败');\n        }\n      } catch (error) {\n        console.error('[wishStore] Incremental sync failed:', error);\n        // 增量同步失败时，回退到检查是否需要完整同步\n        if (!ErrorHandler.isNetworkError(error)) {\n          return await this.syncFromCloud();\n        }\n        throw error;\n      }\n    },\n    \n    // 保存到本地存储\n    _saveToStorage() {\n      uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n      uni.setStorageSync('wishLastSyncTime', this.lastSyncTime)\n    },\n    \n    // 强制重新初始化（用于登录后重新同步）\n    async forceInit() {\n      console.log('[wishStore] Force initialization...');\n      \n      // 清空当前数据\n      this.wishList = [];\n      this.currentGroupId = 'all';\n      this.lastSyncTime = null;\n      this.isLoading = false;\n      \n      // 清理本地存储\n      uni.removeStorageSync('wishList');\n      uni.removeStorageSync('wishLastSyncTime');\n      \n      // 强制从云端同步\n      await this.syncFromCloud();\n      \n      console.log('[wishStore] Force initialization completed');\n    },\n    \n    // 清理本地数据（用于重新登录时清除旧用户数据）\n    clearLocalData() {\n      console.log('[wishStore] Clearing user-specific data...');\n      \n      // 清空用户的心愿数据\n      this.wishList = [];\n      \n      // 重置为默认分组（全部）\n      this.currentGroupId = 'all';\n      \n      this.lastSyncTime = null;\n      this.isLoading = false;\n      \n      // 清除本地存储中的用户数据\n      uni.removeStorageSync('wishList');\n      uni.removeStorageSync('wishLastSyncTime');\n      \n      console.log('[wishStore] User-specific data cleared, system functionality preserved');\n    },\n\n    // 调试方法：检查心愿数据的ID字段\n    debugWishIds() {\n      console.log('=== Wish ID Debug Info ===');\n      console.log('Total wishes:', this.wishList.length);\n\n      const issues = [];\n      this.wishList.forEach((wish, index) => {\n        if (!wish.id && !wish._id) {\n          issues.push(`Wish ${index} (${wish.title}): Missing both ID fields`);\n        } else if (!wish.id) {\n          issues.push(`Wish ${index} (${wish.title}): Missing id field`);\n        } else if (!wish._id) {\n          issues.push(`Wish ${index} (${wish.title}): Missing _id field`);\n        }\n      });\n\n      if (issues.length > 0) {\n        console.warn('ID字段问题:', issues);\n      } else {\n        console.log('所有心愿ID字段正常');\n      }\n\n      console.log('=========================');\n    },\n\n    // 修复心愿数据中的ID字段\n    fixWishIds() {\n      console.log('[wishStore] Fixing wish ID fields...');\n      let fixedCount = 0;\n      \n      this.wishList = this.wishList.map(wish => {\n        const hasIdIssue = !wish.id || !wish._id;\n        \n        if (hasIdIssue) {\n          fixedCount++;\n          return {\n            ...wish,\n            id: wish.id || wish._id,\n            _id: wish._id || wish.id\n          };\n        }\n        \n        return wish;\n      });\n      \n      if (fixedCount > 0) {\n        this._saveToStorage();\n        console.log(`[wishStore] Fixed ${fixedCount} wishes with ID issues`);\n        uni.showToast({\n          title: `修复了 ${fixedCount} 个心愿的ID问题`,\n          icon: 'success'\n        });\n      } else {\n        console.log('[wishStore] No ID issues found');\n        uni.showToast({\n          title: '所有心愿ID字段正常',\n          icon: 'success'\n        });\n      }\n      \n      return fixedCount;\n    },\n\n    /**\n     * 🚀 处理实时数据更新\n     * 当收到实时推送时调用此方法\n     */\n    async _updateLocalFromRealtimeData(realtimeData) {\n      try {\n        console.log('[wishStore] 📝 收到实时心愿数据更新:', realtimeData.length, '个心愿');\n        \n        // 合并实时数据到本地\n        const mergedData = this._mergeWishData(this.wishList, realtimeData);\n        \n        // 更新本地数据\n        this.wishList = mergedData;\n        \n        // 清除缓存\n        this._clearWishesCache();\n        \n        // 保存到本地存储\n        this._saveToStorage();\n        \n        console.log('[wishStore] ✅ 实时心愿数据更新完成');\n        \n      } catch (error) {\n        console.error('[wishStore] 处理实时心愿数据更新失败:', error);\n      }\n    },\n\n    // 🚀 新增：支持新同步架构的方法\n\n    /**\n     * 添加心愿到列表（供实时同步调用）\n     */\n    addWishToList(wish) {\n      const existingIndex = this.wishList.findIndex(w => w._id === wish._id);\n      if (existingIndex === -1) {\n        this.wishList.push({\n          ...wish,\n          id: wish._id // 确保有 id 字段\n        });\n        this._saveToStorage();\n        console.log('[wishStore] 🚀 添加心愿到列表:', wish._id);\n      }\n    },\n\n    /**\n     * 更新列表中的心愿（供实时同步调用）\n     */\n    updateWishInList(wish) {\n      const index = this.wishList.findIndex(w => w._id === wish._id);\n      if (index !== -1) {\n        this.wishList[index] = {\n          ...wish,\n          id: wish._id // 确保有 id 字段\n        };\n        this._saveToStorage();\n        console.log('[wishStore] 🚀 更新列表中的心愿:', wish._id);\n      }\n    },\n\n    /**\n     * 从列表中移除心愿（供实时同步调用）\n     */\n    removeWishFromList(wishId) {\n      const index = this.wishList.findIndex(w => w._id === wishId);\n      if (index !== -1) {\n        this.wishList.splice(index, 1);\n        this._saveToStorage();\n        console.log('[wishStore] 🚀 从列表中移除心愿:', wishId);\n      }\n    },\n\n    /**\n     * 从云端同步数据（供新同步管理器调用）- 优化版本\n     */\n    async syncFromCloud() {\n      try {\n        console.log('[wishStore] 🔄 从云端同步心愿数据...');\n\n        const userStore = useUserStore();\n        if (!userStore.isLogin) {\n          console.warn('[wishStore] 用户未登录，跳过同步');\n          return;\n        }\n\n        const wishCenter = uniCloud.importObject('wish-center');\n        const result = await wishCenter.getWishList({\n          page: 1,\n          pageSize: 1000,\n          includeCompleted: true\n        });\n\n        if (result.errCode === 0) {\n          // 合并云端数据\n          this.wishList = result.data.map(wish => ({\n            ...wish,\n            id: wish._id // 确保有 id 字段\n          }));\n\n          this._saveToStorage();\n          console.log('[wishStore] ✅ 云端同步完成，共', result.data.length, '个心愿');\n        } else {\n          throw new Error(result.errMsg || '同步失败');\n        }\n      } catch (error) {\n        console.error('[wishStore] 云端同步失败:', error);\n        throw error;\n      }\n    },\n\n    // ==================== 新增同步优化方法 ====================\n\n    /**\n     * 从同步中添加心愿（避免触发推送）\n     */\n    addWishFromSync(wish) {\n      const existingIndex = this.wishList.findIndex(w => w.id === wish.id || w._id === wish._id)\n      if (existingIndex === -1) {\n        this.wishList.push({\n          ...wish,\n          id: wish._id || wish.id\n        })\n        this._saveToStorage()\n        console.log(`[wishStore] 从同步添加心愿: ${wish.title}`)\n      }\n    },\n\n    /**\n     * 从同步中更新心愿（避免触发推送）\n     */\n    updateWishFromSync(wish) {\n      const index = this.wishList.findIndex(w => w.id === wish.id || w._id === wish._id)\n      if (index !== -1) {\n        this.wishList[index] = {\n          ...wish,\n          id: wish._id || wish.id\n        }\n        this._saveToStorage()\n        console.log(`[wishStore] 从同步更新心愿: ${wish.title}`)\n      }\n    },\n\n    /**\n     * 从同步中删除心愿（避免触发推送）\n     */\n    removeWishById(wishId) {\n      const index = this.wishList.findIndex(w => w.id === wishId || w._id === wishId)\n      if (index !== -1) {\n        const removedWish = this.wishList.splice(index, 1)[0]\n        this._saveToStorage()\n        console.log(`[wishStore] 从同步删除心愿: ${removedWish.title}`)\n      }\n    },\n\n    // 智能同步 - 只同步有差异的数据（用于下拉刷新）\n    async smartSync() {\n      console.log('[wishStore] Starting smart sync...');\n      \n      if (!this.isOnline) {\n        console.warn('[wishStore] Network unavailable, skipping smart sync');\n        return { hasUpdates: false, updatedCount: 0, reason: 'offline' };\n      }\n\n      try {\n        // 1. 先上传本地待同步的数据\n        const pendingWishes = this.wishList.filter(wish => wish._needSync);\n        if (pendingWishes.length > 0) {\n          console.log(`[wishStore] Uploading ${pendingWishes.length} pending wishes...`);\n          await this.syncPendingData();\n        }\n\n        // 2. 获取云端数据摘要信息\n        const wishCenter = uniCloud.importObject('wish-center');\n        const summaryResult = await wishCenter.getSyncSummary();\n        \n        if (summaryResult.errCode !== 0) {\n          throw new Error(summaryResult.errMsg || '获取云端心愿摘要失败');\n        }\n\n        const cloudSummary = summaryResult.data || {};\n        console.log('[wishStore] Cloud summary received:', cloudSummary);\n\n        // 3. 对比本地数据摘要\n        const localSummary = this._generateLocalWishSummary();\n        console.log('[wishStore] Local summary:', localSummary);\n\n        // 4. 判断是否需要同步\n        const needsSync = this._compareWishDataSummaries(localSummary, cloudSummary);\n        \n        if (!needsSync) {\n          console.log('[wishStore] Data is up to date, no sync needed');\n          return { hasUpdates: false, updatedCount: 0, reason: 'up_to_date' };\n        }\n\n        // 5. 执行增量同步\n        console.log('[wishStore] Data differences detected, performing incremental sync...');\n        const syncResult = await this._performWishIncrementalSync(cloudSummary);\n        \n        // 6. 清理可能的无效同步标记\n        this.cleanupInvalidSyncMarkers();\n        \n        console.log('[wishStore] Smart sync completed:', syncResult);\n        return syncResult;\n\n      } catch (error) {\n        console.error('[wishStore] Smart sync failed:', error);\n        throw error;\n      }\n    },\n\n    // 生成本地心愿数据摘要\n    _generateLocalWishSummary() {\n      return {\n        count: this.wishList.length,\n        lastModified: this._getLocalLastModified(),\n        checksum: this._calculateWishesChecksum(this.wishList)\n      };\n    },\n\n    // 计算心愿数据校验和\n    _calculateWishesChecksum(wishes) {\n      const dataStr = wishes\n        .map(w => `${w._id}:${w.title}:${w.updateDate || w.createDate}:${w.isCompleted}`)\n        .sort()\n        .join('|');\n      \n      // 简单的字符串哈希（用于快速对比）\n      let hash = 0;\n      for (let i = 0; i < dataStr.length; i++) {\n        const char = dataStr.charCodeAt(i);\n        hash = ((hash << 5) - hash) + char;\n        hash = hash & hash; // Convert to 32bit integer\n      }\n      return hash.toString(36);\n    },\n\n    // 对比心愿数据摘要\n    _compareWishDataSummaries(localSummary, cloudSummary) {\n      // 如果数量不同，需要同步\n      if (localSummary.count !== cloudSummary.count) {\n        console.log('[wishStore] Count difference detected:', localSummary.count, 'vs', cloudSummary.count);\n        return true;\n      }\n\n      // 如果校验和不同，需要同步\n      if (localSummary.checksum !== cloudSummary.checksum) {\n        console.log('[wishStore] Checksum difference detected:', localSummary.checksum, 'vs', cloudSummary.checksum);\n        return true;\n      }\n\n      // 如果云端最后修改时间更新，需要同步\n      if (cloudSummary.lastModified && localSummary.lastModified) {\n        const cloudTime = new Date(cloudSummary.lastModified).getTime();\n        const localTime = new Date(localSummary.lastModified).getTime();\n        \n        if (cloudTime > localTime) {\n          console.log('[wishStore] Cloud data is newer:', cloudSummary.lastModified, 'vs', localSummary.lastModified);\n          return true;\n        }\n      }\n\n      return false;\n    },\n\n    // 执行心愿增量同步\n    async _performWishIncrementalSync(cloudSummary) {\n      // 获取完整的云端数据\n      const wishCenter = uniCloud.importObject('wish-center');\n      const result = await wishCenter.getWishList({\n        page: 1,\n        pageSize: 1000,\n        includeCompleted: true,\n        includeDeleted: true\n      });\n      \n      if (result.errCode !== 0) {\n        throw new Error(result.errMsg || '获取云端心愿数据失败');\n      }\n\n      const cloudWishes = result.data || [];\n      const beforeCount = this.wishList.length;\n\n      // 使用现有的智能合并逻辑\n      const mergedData = this._mergeWishData(this.wishList, cloudWishes);\n      \n      // 安全更新数据\n      await this._safeUpdateWishes(mergedData);\n      \n      const afterCount = this.wishList.length;\n      const updatedCount = Math.abs(afterCount - beforeCount);\n\n      this.lastSyncTime = new Date().toISOString();\n      this._saveToStorage();\n\n      return {\n        hasUpdates: true,\n        updatedCount: updatedCount,\n        beforeCount: beforeCount,\n        afterCount: afterCount,\n        reason: 'incremental_sync'\n      };\n    },\n\n    // 安全更新心愿数据\n    async _safeUpdateWishes(newData) {\n      try {\n        // 使用 nextTick 确保DOM更新时机合适\n        await new Promise((resolve) => {\n          if (this.$nextTick) {\n            this.$nextTick(() => {\n              this.wishList = newData.map(wish => ({\n                ...wish,\n                id: wish._id // 确保有 id 字段供前端组件使用\n              }));\n              resolve();\n            });\n          } else {\n            setTimeout(() => {\n              this.wishList = newData.map(wish => ({\n                ...wish,\n                id: wish._id // 确保有 id 字段供前端组件使用\n              }));\n              resolve();\n            }, 0);\n          }\n        });\n      } catch (error) {\n        console.error('[wishStore] Error in safe update, falling back to direct assignment:', error);\n        this.wishList = newData.map(wish => ({\n          ...wish,\n          id: wish._id // 确保有 id 字段供前端组件使用\n        }));\n      }\n    },\n\n    /**\n     * 统一的响应式更新触发器\n     * @param {string} action - 操作类型 ('complete', 'delete', 'add', 'update')\n     * @param {string} wishId - 心愿ID\n     */\n    _triggerReactiveUpdate(action, wishId) {\n      console.log(`[wishStore] 触发响应式更新: ${action} - ${wishId}`);\n\n      // 1. 强制触发 getter 重新计算\n      this.listUpdateCounter++\n\n      // 2. 发送全局事件通知UI组件\n      uni.$emit('wish-list-updated', {\n        timestamp: Date.now(),\n        action: action,\n        wishId: wishId,\n        updateCounter: this.listUpdateCounter\n      })\n\n      // 3. 使用 nextTick 确保DOM更新\n      this.$nextTick && this.$nextTick(() => {\n        console.log(`[wishStore] 响应式更新完成: ${action} - ${wishId}`)\n      })\n\n      console.log(`[wishStore] 响应式更新已触发，当前计数器: ${this.listUpdateCounter}`)\n    },\n\n    // 清理无效的同步标记（多设备冲突后的清理）\n    cleanupInvalidSyncMarkers() {\n      let cleanedCount = 0;\n      \n      this.wishList.forEach(wish => {\n        // 如果心愿有同步标记但缺少必要字段，清除标记\n        if (wish._needSync && (!wish._id || !wish.title)) {\n          console.log(`[wishStore] 清理无效的同步标记: ${wish.title || '未知心愿'}`);\n          delete wish._needSync;\n          cleanedCount++;\n        }\n      });\n      \n      if (cleanedCount > 0) {\n        console.log(`[wishStore] 已清理 ${cleanedCount} 个无效的同步标记`);\n        this._saveToStorage();\n      }\n      \n      return cleanedCount;\n    }\n  }\n})"], "names": ["uni", "defineStore", "loadingManager", "useUserStore", "uniCloud", "enhancedWishData"], "mappings": ";;;;;AAyBA,MAAM,cAAc;AAAA,EAClB,SAAS;AAAA,EACT,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,eAAe;AACjB;AAGA,MAAM,kBAAkB;AAAA;AAAA,EAEtB,sBAAsB,OAAO,UAAU,WAAW;AAChDA,wBAAY,MAAA,OAAA,uBAAA,+BAA+B,OAAO,IAAI,MAAM,OAAO;AACnE,WAAO;AAAA,EACR;AACH;AAGA,MAAM,eAAe;AAAA;AAAA,EAEnB,uBAAuB,OAAO;AAC5B,QAAI,OAAO,UAAU;AAAU,aAAO;AAEtC,QAAI,MAAM,SAAS;AACjB,cAAQ,MAAM,SAAO;AAAA,QACnB,KAAK,YAAY;AACf,iBAAO;AAAA,QACT,KAAK,YAAY;AACf,iBAAO;AAAA,QACT,KAAK,YAAY;AACf,iBAAO,MAAM,UAAU;AAAA,QACzB,KAAK,YAAY;AACf,iBAAO;AAAA,QACT,KAAK,YAAY;AACf,iBAAO;AAAA,QACT,KAAK,YAAY;AACf,iBAAO;AAAA,QACT;AACE,iBAAO,MAAM,UAAU;AAAA,MAC1B;AAAA,IACF;AAGD,QAAI,MAAM,YACR,MAAM,QAAQ,SAAS,IAAI,KAC3B,MAAM,QAAQ,SAAS,SAAS,KAChC,MAAM,QAAQ,SAAS,SAAS,KAChC,MAAM,SAAS,kBACd;AACD,aAAO;AAAA,IACR;AAED,WAAO,MAAM,WAAW,MAAM,UAAU;AAAA,EACzC;AAAA;AAAA,EAGD,eAAe,OAAO;AACpB,QAAI,MAAM,YAAY,YAAY;AAAe,aAAO;AACxD,QAAI,MAAM,SAAS;AAAiB,aAAO;AAC3C,QAAI,MAAM,YACR,MAAM,QAAQ,SAAS,IAAI,KAC3B,MAAM,QAAQ,SAAS,SAAS,KAChC,MAAM,QAAQ,SAAS,SAAS;AAC/B,aAAO;AAEV,WAAO;AAAA,EACR;AAAA;AAAA,EAGD,uBAAuB,OAAO;AAC5B,WAAO,SAAS,MAAM,YACpB,MAAM,QAAQ,SAAS,QAAQ,KAC/B,MAAM,QAAQ,SAAS,QAAQ,KAC/B,MAAM,QAAQ,SAAS,kBAAkB,KACzC,MAAM,QAAQ,SAAS,UAAU;AAAA,EAEpC;AAAA;AAAA,EAGD,UAAU,OAAO,QAAQ,QAAQ,UAAU,WAAW;AAEpD,QAAI,KAAK,uBAAuB,KAAK,GAAG;AACtC,sBAAgB,sBAAsB,OAAO,OAAO;AACpD;AAAA,IACD;AAED,UAAM,UAAU,KAAK,uBAAuB,KAAK;AAGjDA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IAChB,CAAK;AAEDA,wBAAA,MAAA,SAAA,wBAAc,GAAG,KAAK,KAAK,KAAK;AAAA,EACjC;AACH;AAEY,MAAC,eAAeC,cAAW,YAAC,QAAQ;AAAA,EAC9C,OAAO,OAAO;AAAA,IACZ,UAAU,CAAE;AAAA,IACZ,gBAAgB;AAAA;AAAA,IAChB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA;AAAA;AAAA,IAGV,mBAAmB;AAAA;AAAA,IAGnB,YAAY;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,IACZ;AAAA;AAAA,IAGD,iBAAiB,oBAAI,IAAK;AAAA;AAAA,IAC1B,eAAe;AAAA;AAAA,IACf,kBAAkB,oBAAI,IAAK;AAAA;AAAA,EAC/B;AAAA,EAEE,SAAS;AAAA;AAAA,IAEP,oBAAoB,CAAC,UAAU;AAEP,YAAM;AAG5B,YAAM,eAAe,MAAM,SAAS,OAAO,UAAQ,CAAC,KAAK,eAAe,CAAC,KAAK,QAAQ;AAGtF,UAAI,MAAM,mBAAmB,OAAO;AAClC,eAAO;AAAA,MACf,WAAiB,MAAM,mBAAmB,kBAAkB;AAEpD,eAAO,aAAa,OAAO,UAAQ,KAAK,eAAe,SAAS;AAAA,MACxE,WAAiB,MAAM,mBAAmB,WAAW;AAE7C,eAAO,aAAa,OAAO,UAAQ,KAAK,eAAe,SAAS;AAAA,MACxE,WAAiB,MAAM,mBAAmB,UAAU;AAE5C,eAAO,aAAa,OAAO,UAAQ,KAAK,eAAe,QAAQ;AAAA,MACvE,WAAiB,MAAM,mBAAmB,QAAQ;AAE1C,eAAO,aAAa;AAAA,UAAO,UACzB,KAAK,YAAY,KAAK,SAAS,SAAS,MAAM;AAAA,QACxD;AAAA,MACA,OAAa;AAEL,eAAO,aAAa;AAAA,UAAO,UACzB,KAAK,YAAY,KAAK,SAAS,SAAS,MAAM,cAAc;AAAA,QACtE;AAAA,MACO;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,CAAC,UAAU,CAAC,OAAO;AAC9B,aAAO,MAAM,SAAS;AAAA,QAAK,WACxB,KAAK,QAAQ,MAAM,KAAK,OAAO,OAAO,CAAC,KAAK;AAAA,MACrD,KAAW;AAAA,IACN;AAAA;AAAA;AAAA,IAID,kBAAkB,CAAC,UAAU;AAC3B,aAAO,MAAM,SAAS,OAAO,UAAQ,KAAK,SAAS,EAAE;AAAA,IACtD;AAAA;AAAA,IAGD,eAAe,CAAC,UAAU;AACxB,aAAO,MAAM,WAAW,aAAa,KAAK,MAAM,WAAW,cAAc;AAAA,IAC1E;AAAA;AAAA,IAGD,gBAAgB,CAAC,UAAU;AACzB,UAAI,MAAM,WAAW,WAAW;AAC9B,eAAO;AAAA,MACR;AACD,UAAI,MAAM,WAAW,aAAa,GAAG;AACnC,eAAO,SAAS,MAAM,WAAW,UAAU;AAAA,MAC5C;AACD,UAAI,MAAM,WAAW,eAAe,GAAG;AACrC,eAAO,QAAQ,MAAM,WAAW,YAAY;AAAA,MAC7C;AACD,UAAI,MAAM,WAAW,mBAAmB,WAAW;AACjD,eAAO;AAAA,MACR;AACD,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,UAAU,CAAC,UAAU;AACnB,aAAO;AAAA,QACL,GAAG,MAAM;AAAA,QACT,cAAc,MAAM,SAAS,OAAO,UAAQ,KAAK,SAAS,EAAE;AAAA,QAC5D,cAAc,MAAM;AAAA,QACpB,UAAU,MAAM;AAAA,MACxB;AAAA,IACK;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,eAAe;AACnBD,oBAAAA,MAAY,MAAA,OAAA,wBAAA,uCAAuC;AAGnD,YAAM,eAAeA,cAAAA,MAAI,eAAe,UAAU;AAClD,UAAI,cAAc;AAChB,cAAM,SAAS,KAAK,MAAM,YAAY;AAEtC,aAAK,WAAW,OAAO,IAAI,WAAS;AAAA,UAClC,GAAG;AAAA,UACH,IAAI,KAAK,MAAM,KAAK;AAAA;AAAA,QAC9B,EAAU;AACFA,4BAAY,MAAA,OAAA,wBAAA,2CAA2C,KAAK,SAAS,MAAM;AAAA,MAC5E;AAGD,WAAK,mBAAoB;AAGzB,YAAM,KAAK,cAAe;AAE1BA,oBAAAA,2CAAY,gDAAgD;AAAA,IAC7D;AAAA;AAAA,IAGD,qBAAqB;AAEnB,WAAK,mBAAoB;AAGzBA,0BAAI,sBAAsB,CAAC,QAAQ;AACjC,cAAM,YAAY,KAAK;AACvB,aAAK,WAAW,IAAI;AAEpBA,sBAAA,MAAA,MAAA,OAAA,wBAAY,WAAW,YAAY,OAAO,IAAI,OAAO,KAAK,WAAW,OAAO,IAAI,EAAE;AAIlF,YAAI,CAAC,aAAa,KAAK,UAAU;AAC/BA,wBAAAA,MAAA,MAAA,OAAA,wBAAY,eAAe;AAAA,QAC5B;AAGD,YAAI,aAAa,CAAC,KAAK,UAAU;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACtB,CAAW;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,qBAAqB;AACzB,UAAI;AACF,cAAM,cAAc,MAAMA,cAAG,MAAC,eAAgB;AAC9C,aAAK,WAAW,YAAY,gBAAgB;AAC5CA,iEAAY,WAAW,KAAK,WAAW,OAAO,IAAI;AAAA,MACnD,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,wBAAA,aAAa,KAAK;AAChC,aAAK,WAAW;AAAA,MACjB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,eAAe;AAEnB,UAAI,KAAK,WAAW,WAAW;AAC7BA,sBAAAA,MAAA,MAAA,OAAA,wBAAY,mDAAmD;AAC/D;AAAA,MACD;AAED,UAAI;AACF,aAAK,kBAAkB,EAAE,WAAW,KAAM,CAAA;AAG1C,cAAM,gBAAgB,KAAK,SAAS,OAAO,UAAQ,KAAK,SAAS;AACjE,aAAK,kBAAkB,EAAE,cAAc,cAAc,OAAQ,CAAA;AAG7D,YAAI,cAAc,SAAS,GAAG;AAC5BA,8BAAA,MAAA,OAAA,wBAAY,iBAAiB,cAAc,MAAM,kBAAkB;AACnE,gBAAM,SAAS,MAAM,KAAK,gBAAiB;AAE3C,cAAI,OAAO,cAAc,GAAG;AAE1B,kBAAM,KAAK,uBAAwB;AAAA,UACpC;AAAA,QACX,OAAe;AAELA,wBAAAA,MAAA,MAAA,OAAA,wBAAY,6BAA6B;AACzC,gBAAM,KAAK,uBAAwB;AAAA,QACpC;AAGD,aAAK,kBAAkB;AAAA,UACrB,gBAAgB;AAAA,UAChB,YAAY;AAAA,UACZ,WAAW;AAAA,QACrB,CAAS;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,uBAAuB,KAAK;AAG1C,aAAK,kBAAkB;AAAA,UACrB,gBAAgB;AAAA,UAChB,YAAY,KAAK,WAAW,aAAa;AAAA,UACzC,WAAW,MAAM,WAAW;AAAA,QACtC,CAAS;AAGD,YAAI,CAAC,aAAa,eAAe,KAAK,GAAG;AACvCA,wBAAAA,MAAa,MAAA,QAAA,wBAAA,sBAAsB;AAAA,QACpC;AAAA,MACT,UAAgB;AACR,aAAK,kBAAkB,EAAE,WAAW,MAAO,CAAA;AAAA,MAC5C;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,SAAS;AACzB,WAAK,aAAa;AAAA,QAChB,GAAG,KAAK;AAAA,QACR,GAAG;AAAA,MACX;AACMA,oBAAY,MAAA,MAAA,OAAA,wBAAA,oCAAoC,KAAK,UAAU;AAAA,IAChE;AAAA;AAAA,IAGD,MAAM,WAAW,SAAS,OAAO;AAE/B,WAAK,kBAAkB;AAAA,QACrB,YAAY;AAAA,QACZ,WAAW;AAAA,MACnB,CAAO;AAGD,UAAI;AACF,cAAME,qBAAc,eAAC;AAAA,UACnB,MAAM,KAAK,aAAc;AAAA,UACzB;AAAA,YACE,OAAO;AAAA,YACP,IAAI;AAAA,YACJ,SAAS;AAAA,YACT;AAAA,YACA,aAAa,CAAC,UAAU,KAAK,WAAW,mBAAmB;AAAA,YAC3D,cAAc;AAAA,YACd,WAAW,CAAC;AAAA,YACZ,YAAY;AAAA,UACb;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdF,sBAAc,MAAA,MAAA,SAAA,wBAAA,mCAAmC,KAAK;AACtD,YAAI,CAAC,QAAQ;AACX,gBAAM;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,kBAAkB;AAAA,QACrB,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,WAAW;AAAA,MACnB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,WAAW;AACvB,YAAM,MAAM,KAAK,IAAK;AAGtB,UAAI,KAAK,gBAAgB,IAAI,SAAS,GAAG;AACvCA,iEAAY,oBAAoB,SAAS,QAAQ;AACjD,eAAO;AAAA,MACR;AAGD,UAAI,MAAM,KAAK,gBAAgB,KAAM;AACnCA,sBAAAA,MAAY,MAAA,OAAA,wBAAA,yBAAyB;AACrC,eAAO;AAAA,MACR;AAED,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,oBAAoB,WAAW;AAC7B,WAAK,gBAAgB,IAAI,SAAS;AAClC,WAAK,gBAAgB,KAAK,IAAK;AAC/BA,oBAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB,SAAS,EAAE;AAAA,IAC/C;AAAA;AAAA,IAGD,kBAAkB,WAAW;AAC3B,WAAK,gBAAgB,OAAO,SAAS;AACrCA,oBAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB,SAAS,EAAE;AAAA,IAC/C;AAAA;AAAA,IAGD,mBAAmB,QAAQ;AACzB,UAAI,CAAC,KAAK,kBAAkB;AAC1B,aAAK,mBAAmB,oBAAI,IAAK;AAAA,MAClC;AACD,WAAK,iBAAiB,IAAI,QAAQ,KAAK,IAAG,CAAE;AAG5C,YAAM,iBAAiB,KAAK,IAAK,IAAG,IAAI,KAAK;AAC7C,iBAAW,CAAC,IAAI,SAAS,KAAK,KAAK,iBAAiB,WAAW;AAC7D,YAAI,YAAY,gBAAgB;AAC9B,eAAK,iBAAiB,OAAO,EAAE;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,6BAA6B;AAC3B,UAAI,CAAC,KAAK,kBAAkB;AAC1B,eAAO,CAAE;AAAA,MACV;AAED,YAAM,iBAAiB,KAAK,IAAK,IAAG,IAAI,KAAK;AAC7C,YAAM,YAAY,CAAE;AAEpB,iBAAW,CAAC,IAAI,SAAS,KAAK,KAAK,iBAAiB,WAAW;AAC7D,YAAI,aAAa,gBAAgB;AAC/B,oBAAU,KAAK,EAAE;AAAA,QAClB;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,YAAM,YAAY;AAGlB,UAAI,CAAC,KAAK,cAAc,SAAS,GAAG;AAClC;AAAA,MACD;AAED,YAAM,YAAYG,WAAAA,aAAc;AAChC,UAAI,CAAC,UAAU,SAAS;AACtBH,sBAAAA,MAAA,MAAA,OAAA,wBAAY,wEAAwE;AAEpF,YAAI,KAAK,SAAS,WAAW;AAAG;AAGhC;AAAA,MACD;AAED,UAAI;AACF,aAAK,oBAAoB,SAAS;AAClC,aAAK,YAAY;AACjBA,sBAAAA,MAAY,MAAA,OAAA,wBAAA,0CAA0C;AAGtD,cAAM,YAAY,MAAM,KAAK;AAE7B,YAAI,CAAC,WAAW;AACdA,wBAAAA,MAAA,MAAA,OAAA,wBAAY,sDAAsD;AAClE;AAAA,QACD;AAGD,cAAM,KAAK;MAEZ,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,uBAAuB,KAAK;AAE1C,YAAI,KAAK,SAAS,WAAW,GAAG;AAC9BA,wBAAAA,2CAAY,8DAA8D;AAC1E,eAAK,iBAAkB;AAAA,QACxB;AAAA,MACT,UAAgB;AACR,aAAK,YAAY;AACjB,aAAK,kBAAkB,SAAS;AAAA,MACjC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,mBAAmB;AAIM;AAC3BA,sBAAAA,MAAA,MAAA,OAAA,wBAAY,mEAAmE;AAC/E,eAAO;AAAA,MACR;AAAA,IA8CF;AAAA;AAAA,IAGD,wBAAwB;AACtB,UAAI,KAAK,SAAS,WAAW;AAAG,eAAO;AAEvC,YAAM,aAAa,KAAK,SAAS,OAAO,CAAC,QAAQ,YAAY;AAC3D,cAAM,cAAc,IAAI,KAAK,QAAQ,cAAc,QAAQ,UAAU;AACrE,cAAM,aAAa,IAAI,KAAK,OAAO,cAAc,OAAO,UAAU;AAClE,eAAO,cAAc,aAAa,UAAU;AAAA,MACpD,CAAO;AAED,aAAO,WAAW,cAAc,WAAW;AAAA,IAC5C;AAAA;AAAA,IAGD,MAAM,0BAA0B;AAC9BA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,kDAAkD;AAE9D,YAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,YAAM,SAAS,MAAM,WAAW,YAAY;AAAA,QAC1C,MAAM;AAAA,QACN,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,gBAAgB;AAAA;AAAA,MACxB,CAAO;AAED,UAAI,OAAO,YAAY,GAAG;AACxB,cAAM,cAAc,OAAO,QAAQ;AACnCJ,4BAAY,MAAA,OAAA,wBAAA,oCAAoC,YAAY,QAAQ,OAAO;AAG3E,cAAM,aAAa,KAAK,eAAe,KAAK,UAAU,WAAW;AAGjE,aAAK,WAAW,WAAW,IAAI,WAAS;AAAA,UACtC,GAAG;AAAA,UACH,IAAI,KAAK;AAAA;AAAA,QACV,EAAC;AAEF,aAAK,kBAAmB;AACxB,aAAK,gBAAe,oBAAI,KAAI,GAAG,YAAa;AAC5C,aAAK,eAAgB;AAErBA,iEAAY,wDAAwD,KAAK,SAAS,QAAQ,OAAO;AAAA,MACzG,OAAa;AACL,cAAM,IAAI,MAAM,OAAO,UAAU,UAAU;AAAA,MAC5C;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,aAAa,aAAa;AACvCA,oBAAAA,2CAAY,6CAA6C;AAEzD,YAAM,YAAY,oBAAI;AAGtB,YAAM,qBAAqB,KAAK;AAGhC,kBAAY,QAAQ,eAAa;AAE/B,YAAI,mBAAmB,SAAS,UAAU,GAAG,GAAG;AAC9CA,8BAAA,MAAA,OAAA,wBAAY,0BAA0B,UAAU,KAAK,EAAE;AACvD;AAAA,QACD;AAED,kBAAU,IAAI,UAAU,KAAK;AAAA,UAC3B,GAAG;AAAA,UACH,SAAS;AAAA,QACnB,CAAS;AAAA,MACT,CAAO;AAGD,kBAAY,QAAQ,eAAa;AAC/B,cAAM,KAAK,UAAU,OAAO,UAAU;AACtC,cAAM,eAAe,UAAU,IAAI,EAAE;AAErC,YAAI,CAAC,cAAc;AAEjB,oBAAU,IAAI,IAAI;AAAA,YAChB,GAAG;AAAA,YACH,SAAS;AAAA,YACT,WAAW;AAAA;AAAA,UACvB,CAAW;AAAA,QACX,OAAe;AAEL,gBAAM,YAAY,IAAI,KAAK,UAAU,cAAc,UAAU,UAAU;AACvE,gBAAM,YAAY,IAAI,KAAK,aAAa,cAAc,aAAa,UAAU;AAE7E,cAAI,YAAY,WAAW;AAEzB,sBAAU,IAAI,IAAI;AAAA,cAChB,GAAG;AAAA,cACH,SAAS;AAAA,cACT,WAAW;AAAA;AAAA,YACzB,CAAa;AAAA,UACb,WAAqB,YAAY;AAAW;AAAA,eAG3B;AAEL,gBAAI,KAAK,sBAAsB,WAAW,YAAY;AAAG;AAAA,UAG1D;AAAA,QACF;AAAA,MACT,CAAO;AAGD,YAAM,cAAc,MAAM,KAAK,UAAU,OAAM,CAAE,EAC9C,OAAO,UAAQ,CAAC,KAAK,QAAQ,EAC7B,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAEjDA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,qCAAqC;AAAA,QAC/C,YAAY,YAAY;AAAA,QACxB,YAAY,YAAY;AAAA,QACxB,aAAa,YAAY;AAAA,MACjC,CAAO;AAED,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,sBAAsB,OAAO,OAAO;AAClC,YAAM,OAAO,CAAC,SAAS,eAAe,SAAS,SAAS,SAAS,YAAY,YAAY;AAEzF,aAAO,KAAK,KAAK,SAAO;AACtB,cAAM,OAAO,MAAM,GAAG;AACtB,cAAM,OAAO,MAAM,GAAG;AAGtB,YAAI,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ,IAAI,GAAG;AAC9C,iBAAO,KAAK,UAAU,KAAK,KAAM,CAAA,MAAM,KAAK,UAAU,KAAK,KAAI,CAAE;AAAA,QAClE;AAED,eAAO,SAAS;AAAA,MACxB,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AACf,WAAK,WAAW;AAAA,QACd;AAAA,UACA,KAAK;AAAA,UACL,IAAI;AAAA;AAAA,UACF,OAAO;AAAA,UACP,aAAa;AAAA,UACf,OAAO,CAAE;AAAA,UACT,OAAO,CAAE;AAAA,UACT,OAAO,CAAE;AAAA,UACP,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,UACtC,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,UAClC,WAAW;AAAA,UACX,cAAc;AAAA,UACd,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,UAAU,CAAC,OAAO,QAAQ,gBAAgB;AAAA,UAC1C,OAAO;AAAA,UACT,YAAY;AAAA,QACX;AAAA,QACD;AAAA,UACA,KAAK;AAAA,UACL,IAAI;AAAA;AAAA,UACF,OAAO;AAAA,UACP,aAAa;AAAA,UACf,OAAO,CAAE;AAAA,UACT,OAAO,CAAE;AAAA,UACT,OAAO,CAAE;AAAA,UACP,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,UACtC,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,UAClC,WAAW;AAAA,UACX,cAAc;AAAA,UACd,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,UAAU,CAAC,KAAK;AAAA,UAChB,OAAO;AAAA,UACT,YAAY;AAAA,QACX;AAAA,QACD;AAAA,UACA,KAAK;AAAA,UACL,IAAI;AAAA;AAAA,UACF,OAAO;AAAA,UACP,aAAa;AAAA,UACf,OAAO,CAAE;AAAA,UACT,OAAO,CAAE;AAAA,UACT,OAAO,CAAE;AAAA,UACT,YAAY,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACzE,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,UAClC,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UAC1E,cAAc,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACxE,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,UAAU,CAAC,OAAO,gBAAgB;AAAA,UAClC,OAAO;AAAA,UACT,YAAY;AAAA,QACX;AAAA,MACF;AACD,WAAK,eAAgB;AAAA,IACxB;AAAA;AAAA,IAGD,kBAAkB;AAChB,YAAM,eAAeA,cAAAA,MAAI,eAAe,UAAU;AAClD,UAAI,cAAc;AAChB,cAAM,SAAS,KAAK,MAAM,YAAY;AAEtC,aAAK,WAAW,OAAO,IAAI,WAAS;AAAA,UAClC,GAAG;AAAA,UACH,IAAI,KAAK,MAAM,KAAK;AAAA;AAAA,QAC9B,EAAU;AAAA,MACH;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACvB,WAAK,iBAAiB;AAEtB,WAAK,kBAAmB;AAAA,IACzB;AAAA;AAAA,IAGD,oBAAoB;AAAA,IAEnB;AAAA;AAAA,IAGD,MAAM,QAAQ,UAAU;AACtB,UAAI;AAEF,cAAM,YAAY,KAAK;AACvB,cAAMK,oBAAmB;AAAA,UACvB,GAAG;AAAA,UACH;AAAA,UACA,UAAUL,cAAG,MAAC,kBAAmB,EAAC,YAAY;AAAA,QACxD;AAGQ,YAAI,CAAC,KAAK,UAAU;AAClBA,wBAAAA,MAAA,MAAA,OAAA,wBAAY,eAAe;AAC3B,iBAAO,KAAK,gBAAgBK,iBAAgB;AAAA,QAC7C;AAGD,cAAM,aAAaD,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,WAAWC,iBAAgB;AAE3D,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,aAAa;AAAA,YACjB,GAAG,OAAO;AAAA,YACV,IAAI,OAAO,KAAK;AAAA;AAAA,UACjB;AACD,eAAK,SAAS,KAAK,UAAU;AAC7B,eAAK,kBAAmB;AACxB,eAAK,eAAgB;AAGrB,eAAK,uBAAuB,OAAO,WAAW,GAAG;AAEjD,iBAAO;AAAA,QACjB,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,QAC1C;AAAA,MACF,SAAQ,OAAO;AACdL,sBAAAA,MAAA,MAAA,SAAA,wBAAc,WAAW,KAAK;AAG9B,YAAI,aAAa,eAAe,KAAK,GAAG;AACtC,eAAK,WAAW;AAChB,iBAAO,KAAK,gBAAgB,gBAAgB;AAAA,QAC7C;AAGD,YAAI,aAAa,uBAAuB,KAAK,GAAG;AAC9C,0BAAgB,sBAAsB,OAAO,aAAa;AAE1D,iBAAO,KAAK,gBAAgB,gBAAgB;AAAA,QAC7C;AAGD,qBAAa,UAAU,OAAO,UAAU,aAAa;AACrD,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,UAAU;AACxB,YAAM,SAAS,QAAQ,KAAK,IAAG,CAAE,IAAI,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,CAAC;AAE5E,YAAM,OAAO;AAAA,QACX,KAAK;AAAA,QACL,IAAI;AAAA;AAAA,QACJ,OAAO,SAAS,SAAS;AAAA,QACzB,aAAa,SAAS,eAAe;AAAA,QACrC,OAAO,MAAM,QAAQ,SAAS,KAAK,IAAI,SAAS,QAAS,SAAS,QAAQ,CAAC,SAAS,KAAK,IAAI,CAAA;AAAA,QAC7F,OAAO,MAAM,QAAQ,SAAS,KAAK,IAAI,SAAS,QAAS,SAAS,QAAQ,CAAC,SAAS,KAAK,IAAI,CAAA;AAAA,QAC7F,OAAO,MAAM,QAAQ,SAAS,KAAK,IAAI,SAAS,QAAS,SAAS,QAAQ,CAAC,SAAS,KAAK,IAAI,CAAA;AAAA,QAC7F,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,QACpC,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,QACpC,WAAW,SAAS,aAAa;AAAA,QACjC,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY,SAAS,cAAc;AAAA,QACnC,UAAU,SAAS,YAAY,CAAC,KAAK;AAAA,QACrC,OAAO,KAAK,SAAS,SAAS;AAAA,QAC9B,YAAY,KAAK,mBAAmB,SAAS,YAAY,CAAC,KAAK,CAAC;AAAA,QAChE,WAAW;AAAA;AAAA;AAAA,QAGX,WAAW,SAAS,aAAa,KAAK,IAAK;AAAA,QAC3C,oBAAoB,SAAS,YAAYA,cAAG,MAAC,kBAAiB,EAAG,YAAY;AAAA,QAC7E,SAAS;AAAA,MACV;AAGD,UAAI,CAAC,KAAK,SAAS,SAAS,KAAK,GAAG;AAClC,aAAK,SAAS,KAAK,KAAK;AAAA,MACzB;AAGD,UAAI,KAAK,eAAe,aAAa,CAAC,KAAK,SAAS,SAAS,gBAAgB,GAAG;AAC5E,aAAK,SAAS,KAAK,gBAAgB;AAAA,MACtC;AAED,WAAK,SAAS,KAAK,IAAI;AACvB,WAAK,kBAAmB;AACxB,WAAK,eAAgB;AAGrB,WAAK,uBAAuB,OAAO,KAAK,GAAG;AAG3CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,WAAW,oBAAoB;AAAA,QAC3C,MAAM;AAAA,QACN,UAAU;AAAA,MAClB,CAAO;AAED,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,mBAAmB,UAAU;AAC3B,UAAI,CAAC,YAAY,SAAS,WAAW;AAAG,eAAO;AAG/C,YAAM,WAAW,KAAK,SACnB,OAAO,UAAQ;AACd,eAAO,SAAS,KAAK,aAAW,KAAK,YAAY,KAAK,SAAS,SAAS,OAAO,CAAC;AAAA,MAC1F,CAAS,EACA,OAAO,CAAC,KAAK,SAAS,KAAK,IAAI,KAAK,KAAK,cAAc,CAAC,GAAG,CAAC;AAE/D,aAAO,WAAW;AAAA,IACnB;AAAA;AAAA,IAGD,MAAM,WAAW,aAAa;AAC5BA,oBAAY,MAAA,MAAA,OAAA,wBAAA,uCAAuC,WAAW;AAE9D,UAAI;AAEF,YAAI,CAAC,eAAgB,CAAC,YAAY,OAAO,CAAC,YAAY,IAAK;AACzD,gBAAM,IAAI,MAAM,eAAe;AAAA,QAChC;AAGD,YAAI,CAAC,KAAK,UAAU;AAClBA,wBAAAA,MAAA,MAAA,OAAA,wBAAY,eAAe;AAC3B,eAAK,iBAAiB,WAAW;AAGjC,gBAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,YAAY,OAAO,EAAE,OAAO,YAAY,EAAE;AAC/F,cAAI,UAAU,IAAI;AAChB,iBAAK,SAAS,KAAK,EAAE,YAAY;AAAA,UAClC;AAGD,eAAK,uBAAuB,UAAU,YAAY,OAAO,YAAY,EAAE;AAEvEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACtB,CAAW;AACD;AAAA,QACD;AAGD,cAAM,aAAa;AAAA,UACjB,GAAG;AAAA,UACH,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA;AAAA,UACpC,UAAU,YAAY,WAAW,KAAK;AAAA;AAAA,UACtC,cAAc;AAAA;AAAA,QACf;AAGD,cAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtDJ,4BAAY,MAAA,OAAA,wBAAA,iDAAiD,YAAY,OAAO,YAAY,EAAE;AAC9F,cAAM,SAAS,MAAM,WAAW,WAAW,YAAY,OAAO,YAAY,IAAI,UAAU;AAExF,YAAI,OAAO,YAAY,GAAG;AAExBA,wBAAAA,MAAY,MAAA,OAAA,wBAAA,0DAA0D;AAGtE,cAAI,OAAO,MAAM;AACf,iBAAK,iBAAiB,OAAO,IAAI;AAAA,UAC7C,OAAiB;AAEL,wBAAY,WAAW,YAAY,WAAW,KAAK;AACnD,iBAAK,iBAAiB,WAAW;AAAA,UAClC;AAGD,eAAK,uBAAuB,UAAU,YAAY,OAAO,YAAY,EAAE;AAAA,QACjF,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,QAC1C;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,WAAW,KAAK;AAG9B,YAAI,aAAa,eAAe,KAAK,GAAG;AACtCA,wBAAAA,MAAA,MAAA,OAAA,yBAAY,2CAA2C;AACvD,eAAK,WAAW;AAChB,eAAK,iBAAiB,WAAW;AAGjC,gBAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,YAAY,OAAO,EAAE,OAAO,YAAY,EAAE;AAC/F,cAAI,UAAU,IAAI;AAChB,iBAAK,SAAS,KAAK,EAAE,YAAY;AAAA,UAClC;AAGD,eAAK,uBAAuB,UAAU,YAAY,OAAO,YAAY,EAAE;AAEvEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACtB,CAAW;AAAA,QACX,WAAmB,MAAM,YACf,MAAM,QAAQ,SAAS,OAAO,KAC9B,MAAM,QAAQ,SAAS,KAAK,KAC5B,MAAM,QAAQ,SAAS,WAAW,KAClC,MAAM,QAAQ,SAAS,YAAY,IAClC;AAEDA,wBAAA,MAAA,MAAA,OAAA,yBAAY,0CAA0C;AACtD,gBAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,YAAY,OAAO,EAAE,OAAO,YAAY,EAAE;AAC/F,cAAI,UAAU,IAAI;AAChB,iBAAK,SAAS,OAAO,OAAO,CAAC;AAE7B,iBAAK,oBAAmB;AACxB,iBAAK,eAAc;AAAA,UACpB;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACtB,CAAW;AAAA,QACF,WAAU,aAAa,uBAAuB,KAAK,GAAG;AAErD,0BAAgB,sBAAsB,OAAO,aAAa;AAE1D,gBAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,YAAY,OAAO,EAAE,OAAO,YAAY,EAAE;AAC/F,cAAI,UAAU,IAAI;AAChB,iBAAK,SAAS,KAAK,EAAE,YAAY;AAAA,UAClC;AAAA,QACX,OAAe;AAEL,uBAAa,UAAU,OAAO,UAAU,aAAa;AACrD,gBAAM;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,aAAa;AAC5BA,oBAAA,MAAA,MAAA,OAAA,yBAAY,6CAA6C,WAAW;AAEpE,YAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,YAAY,OAAO,EAAE,OAAO,YAAY,EAAE;AAC/FA,oBAAY,MAAA,MAAA,OAAA,yBAAA,oCAAoC,KAAK;AAErD,UAAI,UAAU,IAAI;AAChB,cAAM,eAAe,KAAK,SAAS,KAAK;AACxCA,sBAAY,MAAA,MAAA,OAAA,yBAAA,8BAA8B,YAAY;AAGtD,cAAM,aAAa;AAAA,UACjB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA;AAAA,UAEpC,IAAI,YAAY,MAAM,YAAY,OAAO,aAAa,MAAM,aAAa;AAAA,UACzE,KAAK,YAAY,OAAO,YAAY,MAAM,aAAa,OAAO,aAAa;AAAA;AAAA,UAE3E,YAAY,YAAY,cAAc,aAAa,cAAc;AAAA,UACjE,UAAU,YAAY,YAAY,aAAa,YAAY,CAAC,KAAK;AAAA,UACjE,MAAM,YAAY,QAAQ,aAAa,QAAQ,CAAE;AAAA,UACjD,OAAO,YAAY,SAAS,aAAa,SAAS;AAAA,UAClD,aAAa,YAAY,eAAe,aAAa,eAAe;AAAA,QACrE;AAEDA,kEAAY,4BAA4B,UAAU;AAGlD,SAAC,SAAS,SAAS,OAAO,EAAE,QAAQ,WAAS;AAC3C,cAAI,WAAW,KAAK,KAAK,CAAC,MAAM,QAAQ,WAAW,KAAK,CAAC,GAAG;AAC1D,uBAAW,KAAK,IAAI,CAAC,WAAW,KAAK,CAAC;AAAA,UAClD,WAAqB,CAAC,WAAW,KAAK,GAAG;AAC7B,uBAAW,KAAK,IAAI,CAAE;AAAA,UACvB;AAAA,QACX,CAAS;AAGD,YAAI,YAAY,eAAe,YAAY,GAAG;AAC5CA,wBAAA,MAAA,MAAA,OAAA,yBAAY,2CAA2C,YAAY,UAAU;AAG7E,cAAI,CAAC,MAAM,QAAQ,WAAW,QAAQ,GAAG;AACvCA,0BAAAA,MAAA,MAAA,OAAA,yBAAY,oDAAoD;AAChE,uBAAW,WAAW,CAAC,KAAK;AAAA,UAC7B;AAED,cAAI,WAAW,eAAe,WAAW;AACvC,gBAAI,CAAC,WAAW,SAAS,SAAS,gBAAgB,GAAG;AACnD,yBAAW,SAAS,KAAK,gBAAgB;AAAA,YAC1C;AAAA,UACb,OAAiB;AACL,uBAAW,WAAW,WAAW,SAAS,OAAO,QAAM,OAAO,gBAAgB;AAC9E,gBAAI,WAAW,SAAS,WAAW,GAAG;AACpC,yBAAW,SAAS,KAAK,KAAK;AAAA,YAC/B;AAAA,UACF;AAAA,QACF;AAEDA,sBAAA,MAAA,MAAA,OAAA,yBAAY,kCAAkC,UAAU;AACxD,aAAK,SAAS,KAAK,IAAI;AACvB,aAAK,eAAgB;AAAA,MAC7B,OAAa;AACLA,oEAAc,sDAAsD,YAAY,OAAO,YAAY,EAAE;AAAA,MACtG;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,WAAW,IAAI;AACnB,UAAI;AAEF,YAAI,CAAC,KAAK,UAAU;AAClBA,wBAAAA,MAAY,MAAA,OAAA,yBAAA,iBAAiB;AAE7B,gBAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,cAAI,MAAM;AACR,iBAAK,WAAW;AAChB,iBAAK,YAAY;AACjB,iBAAK,cAAa,oBAAI,KAAI,GAAG,YAAa;AAAA,UAC3C;AAIDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACtB,CAAW;AACD;AAAA,QACD;AAGD,cAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,WAAW,EAAE;AAE7C,YAAI,OAAO,YAAY,GAAG;AAExB,eAAK,iBAAiB,EAAE;AAGxB,eAAK,gBAAe,oBAAI,KAAI,GAAG,YAAa;AAC5C,eAAK,eAAgB;AAAA,QAG/B,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,QAC1C;AAAA,MACF,SAAQ,OAAO;AAEd,YAAI,MAAM,YACR,MAAM,QAAQ,SAAS,OAAO,KAC9B,MAAM,QAAQ,SAAS,KAAK,KAC5B,MAAM,QAAQ,SAAS,WAAW,KAClC,MAAM,QAAQ,SAAS,YAAY,IAClC;AAEDJ,wBAAA,MAAA,MAAA,OAAA,yBAAY,4CAA4C;AACxD,eAAK,iBAAiB,EAAE;AAExBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACtB,CAAW;AACD;AAAA,QACD;AAEDA,sBAAAA,MAAA,MAAA,SAAA,yBAAc,WAAW,KAAK;AAG9B,YAAI,aAAa,eAAe,KAAK,GAAG;AACtC,eAAK,WAAW;AAChB,gBAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,cAAI,MAAM;AACR,iBAAK,WAAW;AAChB,iBAAK,YAAY;AACjB,iBAAK,cAAa,oBAAI,KAAI,GAAG,YAAa;AAAA,UAC3C;AAIDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACtB,CAAW;AAAA,QACX,OAAe;AAEL,uBAAa,UAAU,OAAO,QAAQ;AACtC,gBAAM;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,IAAI;AACnBA,oBAAA,MAAA,MAAA,OAAA,yBAAY,oBAAoB,EAAE,EAAE;AAGpC,WAAK,mBAAmB,EAAE;AAG1B,YAAM,QAAQ,KAAK,SAAS,UAAU,UAAQ,KAAK,QAAQ,MAAM,KAAK,OAAO,EAAE;AAE/E,UAAI,UAAU,IAAI;AAEhB,aAAK,SAAS,OAAO,OAAO,CAAC;AAE7BA,4BAAY,MAAA,OAAA,yBAAA,0BAA0B,KAAK,SAAS,MAAM,EAAE;AAG5D,aAAK,oBAAqB;AAG1B,aAAK;AAGL,aAAK,eAAgB;AAGrB,aAAK,uBAAuB,UAAU,EAAE;AAExCA,sBAAY,MAAA,MAAA,OAAA,yBAAA,0BAA0B;AAAA,MAC9C,OAAa;AACLA,sBAAA,MAAA,MAAA,QAAA,yBAAa,0BAA0B,EAAE,EAAE;AAAA,MAC5C;AAAA,IACF;AAAA;AAAA,IAGD,sBAAsB;AAElB,WAAK,SAAS,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAG5D,WAAK,SAAS,QAAQ,CAAC,MAAM,UAAU;AACrC,aAAK,QAAQ,QAAQ;AACrB,aAAK,cAAa,oBAAI,KAAM,GAAC,YAAW;AAAA,MAClD,CAAS;AAEDA,oBAAAA,MAAY,MAAA,OAAA,yBAAA,yBAAyB,KAAK,SAAS,MAAM,KAAK;AAAA,IACjE;AAAA;AAAA,IAGD,MAAM,aAAa,IAAI;AACrB,UAAI;AACF,cAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,aAAa,EAAE;AAE/C,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,cAAI,MAAM;AACR,iBAAK,cAAc;AACnB,iBAAK,gBAAe,oBAAI,KAAI,GAAG,YAAa;AAC5C,iBAAK,cAAa,oBAAI,KAAI,GAAG,YAAa;AAE1C,gBAAI,OAAO,QAAQ,OAAO,KAAK,SAAS;AACtC,mBAAK,UAAU,OAAO,KAAK;AAAA,YACzC,OAAmB;AAEL,mBAAK,WAAW,KAAK,WAAW,KAAK;AAAA,YACtC;AACD,iBAAK,eAAgB;AAGrB,iBAAK,uBAAuB,YAAY,EAAE;AAAA,UAC3C;AAAA,QACX,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,QAC1C;AAAA,MACF,SAAQ,OAAO;AAEd,YAAI,MAAM,YACR,MAAM,QAAQ,SAAS,OAAO,KAC9B,MAAM,QAAQ,SAAS,KAAK,KAC5B,MAAM,QAAQ,SAAS,WAAW,KAClC,MAAM,QAAQ,SAAS,YAAY,IAClC;AAEDJ,wBAAA,MAAA,MAAA,OAAA,yBAAY,0CAA0C;AACtD,gBAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AACtE,cAAI,UAAU,IAAI;AAChB,iBAAK,SAAS,OAAO,OAAO,CAAC;AAE7B,iBAAK,oBAAmB;AACxB,iBAAK,eAAc;AAAA,UACpB;AACD;AAAA,QACD;AAEDA,sBAAAA,MAAA,MAAA,SAAA,yBAAc,WAAW,KAAK;AAG9B,cAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,YAAI,MAAM;AACR,eAAK,cAAc;AACnB,eAAK,gBAAe,oBAAI,KAAI,GAAG,YAAa;AAC5C,eAAK,cAAa,oBAAI,KAAI,GAAG,YAAa;AAC1C,eAAK,YAAY;AACjB,eAAK,eAAgB;AAGrB,eAAK,uBAAuB,YAAY,EAAE;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,YAAY,IAAI;AACpB,UAAI;AACF,cAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,YAAY,EAAE;AAE9C,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,cAAI,QAAQ,KAAK,aAAa;AAC5B,iBAAK,cAAc;AACnB,iBAAK,eAAe;AACpB,iBAAK,cAAa,oBAAI,KAAI,GAAG,YAAa;AAE1C,gBAAI,OAAO,QAAQ,OAAO,KAAK,SAAS;AACtC,mBAAK,UAAU,OAAO,KAAK;AAAA,YACzC,OAAmB;AAEL,mBAAK,WAAW,KAAK,WAAW,KAAK;AAAA,YACtC;AACD,iBAAK,eAAgB;AAGrB,iBAAK,uBAAuB,WAAW,EAAE;AAEzC,mBAAO;AAAA,UACR;AAAA,QACX,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,QAC1C;AAAA,MACF,SAAQ,OAAO;AAEd,YAAI,MAAM,YACR,MAAM,QAAQ,SAAS,OAAO,KAC9B,MAAM,QAAQ,SAAS,KAAK,KAC5B,MAAM,QAAQ,SAAS,WAAW,KAClC,MAAM,QAAQ,SAAS,YAAY,IAClC;AAEDJ,wBAAA,MAAA,MAAA,OAAA,yBAAY,0CAA0C;AACtD,gBAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AACtE,cAAI,UAAU,IAAI;AAChB,iBAAK,SAAS,OAAO,OAAO,CAAC;AAE7B,iBAAK,oBAAmB;AACxB,iBAAK,eAAc;AAAA,UACpB;AACD,iBAAO;AAAA,QACR;AAEDA,sBAAAA,MAAA,MAAA,SAAA,yBAAc,WAAW,KAAK;AAG9B,cAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,YAAI,QAAQ,KAAK,aAAa;AAC5B,eAAK,cAAc;AACnB,eAAK,eAAe;AACpB,eAAK,cAAa,oBAAI,KAAI,GAAG,YAAa;AAC1C,eAAK,YAAY;AACnB,eAAK,eAAgB;AACnB,iBAAO;AAAA,QACR;AAAA,MACF;AACD,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,MAAM,gBAAgB,YAAY,UAAU,OAAO;AACjD,UAAI,CAAC,cAAc,WAAW,WAAW;AAAG;AAE5C,UAAI;AAEF,cAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,WAAW,YAAY,QAAQ,WAAW;AAChD,cAAM,SAAS,MAAM,WAAW,gBAAgB,YAAY,UAAU,OAAO;AAE7E,YAAI,OAAO,YAAY,GAAG;AAExB,eAAK,kBAAkB,YAAY,OAAO;AAAA,QAGpD,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,QAC1C;AAAA,MACF,SAAQ,OAAO;AACdJ,sBAAAA,MAAA,MAAA,SAAA,yBAAc,WAAW,KAAK;AAG9B,aAAK,kBAAkB,YAAY,OAAO;AAAA,MAC3C;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,YAAY,SAAS;AAErC,iBAAW,QAAQ,CAAC,IAAI,UAAU;AAChC,cAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,YAAI,MAAM;AACR,cAAI,YAAY,OAAO;AACrB,iBAAK,QAAQ,QAAQ;AAAA,UACjC,OAAiB;AACL,iBAAK,aAAa,QAAQ;AAAA,UAC3B;AACD,eAAK,cAAa,oBAAI,KAAI,GAAG,YAAa;AAC1C,eAAK,YAAY;AAAA,QAClB;AAAA,MACT,CAAO;AAGD,WAAK,SAAS,KAAK,CAAC,GAAG,MAAM;AAC3B,YAAI,EAAE,gBAAgB,EAAE,aAAa;AACnC,iBAAO,EAAE,cAAc,IAAI;AAAA,QAC5B;AACD,gBAAQ,EAAE,SAAS,MAAM,EAAE,SAAS;AAAA,MAC5C,CAAO;AAED,WAAK,eAAgB;AACrBA,oBAAAA,MAAA,MAAA,OAAA,yBAAY,YAAY;AAAA,IACzB;AAAA;AAAA,IAGD,eAAe,SAAS;AACtB,UAAI,CAAC,WAAW,QAAQ,WAAW;AAAG;AAGtC,YAAM,cAAc,oBAAI,IAAK;AAC7B,WAAK,SAAS,QAAQ,UAAQ;AAC5B,cAAM,MAAM,KAAK,OAAO,KAAK;AAC7B,oBAAY,IAAI,KAAK,IAAI;AAAA,MACjC,CAAO;AAED,UAAI,KAAK,mBAAmB,OAAO;AAEjC,cAAM,kBAAkB,CAAE;AAC1B,cAAM,kBAAkB,KAAK,SAAS,OAAO,UAAQ,KAAK,WAAW;AAErE,gBAAQ,QAAQ,UAAQ;AACtB,cAAI,YAAY,IAAI,KAAK,OAAO,KAAK,EAAE,GAAG;AACxC,4BAAgB,KAAK,YAAY,IAAI,KAAK,OAAO,KAAK,EAAE,CAAC;AAAA,UAC1D;AAAA,QACX,CAAS;AAED,cAAM,kBAAkB,KAAK,SAAS;AAAA,UAAO,UAC3C,CAAC,KAAK,eAAe,CAAC,gBAAgB,KAAK,QAAM,EAAE,OAAO,EAAE,SAAS,KAAK,OAAO,KAAK,GAAG;AAAA,QAC1F;AAED,aAAK,WAAW,CAAC,GAAG,iBAAiB,GAAG,iBAAiB,GAAG,eAAe;AAAA,MACnF,OAAa;AAEL,cAAM,cAAc,KAAK,SAAS;AAAA,UAAO,UACvC,KAAK,YAAY,KAAK,SAAS,SAAS,KAAK,cAAc,KAAK,CAAC,KAAK;AAAA,QACvE;AAED,cAAM,cAAc,KAAK,SAAS;AAAA,UAAO,UACvC,CAAC,KAAK,YAAY,CAAC,KAAK,SAAS,SAAS,KAAK,cAAc,KAAK,KAAK;AAAA,QACxE;AAED,cAAM,uBAAuB,CAAE;AAC/B,gBAAQ,QAAQ,UAAQ;AACtB,gBAAM,MAAM,KAAK,OAAO,KAAK;AAC7B,cAAI,YAAY,IAAI,GAAG,GAAG;AACxB,iCAAqB,KAAK,YAAY,IAAI,GAAG,CAAC;AAAA,UAC/C;AAAA,QACX,CAAS;AAED,cAAM,uBAAuB,YAAY;AAAA,UAAO,UAC9C,CAAC,qBAAqB,KAAK,QAAM,EAAE,OAAO,EAAE,SAAS,KAAK,OAAO,KAAK,GAAG;AAAA,QAC1E;AAED,aAAK,WAAW,CAAC,GAAG,sBAAsB,GAAG,sBAAsB,GAAG,WAAW;AAAA,MAClF;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACtB,YAAM,gBAAgB,KAAK,SAAS,OAAO,UAAQ,KAAK,SAAS;AACjE,UAAI,cAAc;AAClB,UAAI,cAAc;AAElB,UAAI,cAAc,WAAW,GAAG;AAC9BA,sBAAAA,4CAAY,qCAAqC;AACjD,eAAO,EAAE,aAAa,GAAG,aAAa,EAAC;AAAA,MACxC;AAEDA,0BAAY,MAAA,OAAA,yBAAA,uBAAuB,cAAc,MAAM,oBAAoB;AAE3E,iBAAW,QAAQ,eAAe;AAChC,YAAI;AACF,gBAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AAEtD,cAAI,KAAK,UAAU;AAEjB,kBAAM,SAAS,MAAM,WAAW,WAAW,KAAK,GAAG;AACnD,gBAAI,OAAO,YAAY,GAAG;AAExB,mBAAK,WAAW,KAAK,SAAS,OAAO,OAAK,EAAE,QAAQ,KAAK,OAAO,EAAE,OAAO,KAAK,EAAE;AAChF;AACAJ,kCAAA,MAAA,OAAA,yBAAY,oCAAoC,KAAK,KAAK,EAAE;AAAA,YAC1E,OAAmB;AACL,oBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,YAC1C;AAAA,UACF,WAAU,KAAK,IAAI,WAAW,OAAO,GAAG;AAEvC,kBAAM,SAAS,MAAM,WAAW,WAAW,IAAI;AAC/C,gBAAI,OAAO,YAAY,GAAG;AAExB,oBAAM,QAAQ,KAAK;AACnB,mBAAK,MAAM,OAAO,KAAK;AACvB,mBAAK,KAAK,OAAO,KAAK;AACtB,qBAAO,KAAK;AACZ;AACAA,4BAAA,MAAA,MAAA,OAAA,yBAAY,gCAAgC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,GAAG,GAAG;AAAA,YAChG,WAAuB,OAAO,YAAY,KAAK;AAEjCA,wEAAY,+CAA+C,KAAK,KAAK,oBAAoB;AACzF,oBAAM,YAAY,OAAO;AACzB,qBAAO,OAAO,MAAM,SAAS;AAC7B,qBAAO,KAAK;AACZ;AAAA,YACd,OAAmB;AACL,oBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,YAC1C;AAAA,UACb,OAAiB;AAEL,kBAAM,SAAS,MAAM,WAAW,WAAW,KAAK,KAAK,IAAI;AACzD,gBAAI,OAAO,YAAY,GAAG;AACxB,qBAAO,KAAK;AACZ;AACAA,kCAAA,MAAA,OAAA,yBAAY,oCAAoC,KAAK,KAAK,EAAE;AAAA,YAC1E,WAAuB,OAAO,YAAY,KAAK;AAEjCA,wEAAY,yCAAyC,KAAK,KAAK,6BAA6B;AAG5F,kBAAI,OAAO,MAAM;AACf,uBAAO,OAAO,MAAM,OAAO,IAAI;AAC/B,uBAAO,KAAK;AACZ;AACAA,oCAAY,MAAA,OAAA,yBAAA,0DAA0D,KAAK,KAAK,EAAE;AAAA,cAClG,OAAqB;AAELA,oCAAA,MAAA,OAAA,yBAAY,8CAA8C,KAAK,KAAK,oBAAoB;AACxF;AAAA,cACD;AAAA,YACf,OAAmB;AACL,oBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,YAC1C;AAAA,UACF;AAGD,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAAA,QAEtD,SAAQ,OAAO;AAEd,cAAI,MAAM,YACR,MAAM,QAAQ,SAAS,OAAO,KAC9B,MAAM,QAAQ,SAAS,KAAK,KAC5B,MAAM,QAAQ,SAAS,WAAW,KAClC,MAAM,QAAQ,SAAS,YAAY,IAClC;AAEDA,gCAAA,MAAA,OAAA,yBAAY,8BAA8B,KAAK,KAAK,qBAAqB;AAGzE,mBAAO,KAAK;AAGZ,kBAAM,aAAa,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,KAAK,GAAG;AAClE,gBAAI,eAAe,IAAI;AACrBA,kCAAY,MAAA,OAAA,yBAAA,2BAA2B,KAAK,KAAK,EAAE;AACnD,mBAAK,SAAS,OAAO,YAAY,CAAC;AAAA,YACnC;AAGD;AAAA,UACZ,OAAiB;AAEPA,0BAAA,MAAA,MAAA,SAAA,yBAAc,WAAW,KAAK,OAAO,KAAK;AACxC;AAGF,gBAAI,aAAa,eAAe,KAAK,GAAG;AACtC,mBAAK,WAAW;AAChBA,4BAAAA,4CAAY,WAAW;AACvB;AAAA,YACC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGD,WAAK,eAAgB;AAGrB,UAAI,cAAc,GAAG;AACnBA,kEAAY,qBAAqB,WAAW,KAAK;AAAA,MAClD;AACD,UAAI,cAAc,GAAG;AACnBA,kEAAY,qBAAqB,WAAW,KAAK;AAAA,MAClD;AAED,aAAO,EAAE,aAAa,YAAa;AAAA,IACpC;AAAA;AAAA,IAGD,MAAM,uBAAuB,WAAW,WAAW;AACjDA,0BAAA,MAAA,OAAA,yBAAY,uCAAuC,UAAU,KAAK,EAAE;AAEpE,YAAM,YAAY,IAAI,KAAK,UAAU,cAAc,UAAU,UAAU;AACvE,YAAM,YAAY,IAAI,KAAK,UAAU,cAAc,UAAU,UAAU;AAEvE,UAAI,YAAY,WAAW;AAEzB,YAAI;AACF,gBAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,gBAAM,SAAS,MAAM,WAAW,gBAAgB,UAAU,KAAK;AAAA,YAC7D,GAAG;AAAA,YACH,cAAc;AAAA,YACd,qBAAqB;AAAA,UACjC,CAAW;AAED,cAAI,OAAO,YAAY,GAAG;AACxB,mBAAO,UAAU;AACjBJ,gCAAY,MAAA,OAAA,yBAAA,iDAAiD,UAAU,KAAK,EAAE;AAC9E,mBAAO;AAAA,UACR;AAAA,QACF,SAAQ,OAAO;AACdA,wBAAc,MAAA,MAAA,SAAA,yBAAA,WAAW,KAAK;AAAA,QAC/B;AAAA,MACT,OAAa;AAELA,4BAAY,MAAA,OAAA,yBAAA,iDAAiD,UAAU,KAAK,EAAE;AAC9E,eAAO,OAAO,WAAW,SAAS;AAClC,eAAO,UAAU;AACjB,eAAO;AAAA,MACR;AAED,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,MAAM,yBAAyB;AAC7B,UAAI,CAAC,KAAK,cAAc;AACtBA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,qDAAqD;AACjE,eAAO,MAAM,KAAK;MACnB;AAED,UAAI;AACF,cAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,sBAAsB;AAAA,UACpD,OAAO,KAAK;AAAA,UACZ,gBAAgB;AAAA,QAC1B,CAAS;AAED,YAAI,OAAO,YAAY,GAAG;AACxB,gBAAM,EAAE,WAAW,SAAS,UAAS,IAAK,OAAO;AACjD,cAAI,iBAAiB;AAGrB,cAAI,aAAa,UAAU,SAAS,GAAG;AACrC,sBAAU,QAAQ,eAAa;AAC7B,oBAAM,gBAAgB,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,UAAU,GAAG;AAC1E,kBAAI,kBAAkB,IAAI;AACxB,qBAAK,SAAS,KAAK;AAAA,kBACjB,GAAG;AAAA,kBACH,IAAI,UAAU;AAAA,gBAChC,CAAiB;AACD;AACAJ,0EAAY,0CAA0C,UAAU,KAAK,EAAE;AAAA,cACxE;AAAA,YACf,CAAa;AAAA,UACF;AAGD,cAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,oBAAQ,QAAQ,eAAa;AAC3B,oBAAM,gBAAgB,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,UAAU,GAAG;AAC1E,kBAAI,kBAAkB,IAAI;AACxB,sBAAM,YAAY,KAAK,SAAS,aAAa;AAC7C,sBAAM,YAAY,IAAI,KAAK,UAAU,cAAc,UAAU,UAAU;AACvE,sBAAM,YAAY,IAAI,KAAK,UAAU,cAAc,UAAU,UAAU;AAGvE,oBAAI,YAAY,aAAa,CAAC,UAAU,WAAW;AACjD,uBAAK,SAAS,aAAa,IAAI;AAAA,oBAC7B,GAAG;AAAA,oBACH,IAAI,UAAU;AAAA,kBAClC;AACkB;AACAA,sCAAA,MAAA,OAAA,yBAAY,wCAAwC,UAAU,KAAK,EAAE;AAAA,gBACvF,OAAuB;AACLA,sCAAA,MAAA,OAAA,yBAAY,wCAAwC,UAAU,KAAK,0CAA0C;AAAA,gBAC9G;AAAA,cACF;AAAA,YACf,CAAa;AAAA,UACF;AAGD,cAAI,aAAa,UAAU,SAAS,GAAG;AACrC,sBAAU,QAAQ,eAAa;AAC7B,oBAAM,gBAAgB,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,SAAS;AACtE,kBAAI,kBAAkB,IAAI;AACxB,sBAAM,cAAc,KAAK,SAAS,aAAa;AAC/C,qBAAK,SAAS,OAAO,eAAe,CAAC;AACrC;AACAA,oCAAY,MAAA,OAAA,yBAAA,qCAAqC,YAAY,KAAK,EAAE;AAAA,cACrE;AAAA,YACf,CAAa;AAAA,UACF;AAED,cAAI,iBAAiB,GAAG;AACtB,iBAAK,gBAAe,oBAAI,KAAM,GAAC,YAAW;AAC1C,iBAAK,eAAc;AACnBA,gCAAA,MAAA,OAAA,yBAAY,2CAA2C,cAAc,kBAAkB;AAAA,UACnG,OAAiB;AACLA,0BAAAA,MAAY,MAAA,OAAA,yBAAA,uDAAuD;AAAA,UACpE;AAED,iBAAO,EAAE,eAAc;AAAA,QACjC,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,QAC1C;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,yBAAc,wCAAwC,KAAK;AAE3D,YAAI,CAAC,aAAa,eAAe,KAAK,GAAG;AACvC,iBAAO,MAAM,KAAK;QACnB;AACD,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC5DA,oBAAAA,MAAI,eAAe,oBAAoB,KAAK,YAAY;AAAA,IACzD;AAAA;AAAA,IAGD,MAAM,YAAY;AAChBA,oBAAAA,MAAY,MAAA,OAAA,yBAAA,qCAAqC;AAGjD,WAAK,WAAW;AAChB,WAAK,iBAAiB;AACtB,WAAK,eAAe;AACpB,WAAK,YAAY;AAGjBA,0BAAI,kBAAkB,UAAU;AAChCA,0BAAI,kBAAkB,kBAAkB;AAGxC,YAAM,KAAK;AAEXA,oBAAAA,MAAY,MAAA,OAAA,yBAAA,4CAA4C;AAAA,IACzD;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAY,MAAA,OAAA,yBAAA,4CAA4C;AAGxD,WAAK,WAAW;AAGhB,WAAK,iBAAiB;AAEtB,WAAK,eAAe;AACpB,WAAK,YAAY;AAGjBA,0BAAI,kBAAkB,UAAU;AAChCA,0BAAI,kBAAkB,kBAAkB;AAExCA,oBAAAA,4CAAY,wEAAwE;AAAA,IACrF;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAY,MAAA,OAAA,yBAAA,4BAA4B;AACxCA,0BAAY,MAAA,OAAA,yBAAA,iBAAiB,KAAK,SAAS,MAAM;AAEjD,YAAM,SAAS,CAAA;AACf,WAAK,SAAS,QAAQ,CAAC,MAAM,UAAU;AACrC,YAAI,CAAC,KAAK,MAAM,CAAC,KAAK,KAAK;AACzB,iBAAO,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,2BAA2B;AAAA,QAC7E,WAAmB,CAAC,KAAK,IAAI;AACnB,iBAAO,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,qBAAqB;AAAA,QACvE,WAAmB,CAAC,KAAK,KAAK;AACpB,iBAAO,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,sBAAsB;AAAA,QAC/D;AAAA,MACT,CAAO;AAED,UAAI,OAAO,SAAS,GAAG;AACrBA,sBAAa,MAAA,MAAA,QAAA,yBAAA,WAAW,MAAM;AAAA,MACtC,OAAa;AACLA,sBAAAA,4CAAY,YAAY;AAAA,MACzB;AAEDA,oBAAAA,MAAY,MAAA,OAAA,yBAAA,2BAA2B;AAAA,IACxC;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAY,MAAA,OAAA,yBAAA,sCAAsC;AAClD,UAAI,aAAa;AAEjB,WAAK,WAAW,KAAK,SAAS,IAAI,UAAQ;AACxC,cAAM,aAAa,CAAC,KAAK,MAAM,CAAC,KAAK;AAErC,YAAI,YAAY;AACd;AACA,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,IAAI,KAAK,MAAM,KAAK;AAAA,YACpB,KAAK,KAAK,OAAO,KAAK;AAAA,UAClC;AAAA,QACS;AAED,eAAO;AAAA,MACf,CAAO;AAED,UAAI,aAAa,GAAG;AAClB,aAAK,eAAc;AACnBA,4BAAA,MAAA,OAAA,yBAAY,qBAAqB,UAAU,wBAAwB;AACnEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,OAAO,UAAU;AAAA,UACxB,MAAM;AAAA,QAChB,CAAS;AAAA,MACT,OAAa;AACLA,sBAAAA,MAAY,MAAA,OAAA,yBAAA,gCAAgC;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,MAAM,6BAA6B,cAAc;AAC/C,UAAI;AACFA,4BAAA,MAAA,OAAA,yBAAY,8BAA8B,aAAa,QAAQ,KAAK;AAGpE,cAAM,aAAa,KAAK,eAAe,KAAK,UAAU,YAAY;AAGlE,aAAK,WAAW;AAGhB,aAAK,kBAAiB;AAGtB,aAAK,eAAc;AAEnBA,sBAAAA,MAAY,MAAA,OAAA,yBAAA,0BAA0B;AAAA,MAEvC,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,yBAAA,6BAA6B,KAAK;AAAA,MACjD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAOD,cAAc,MAAM;AAClB,YAAM,gBAAgB,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,KAAK,GAAG;AACrE,UAAI,kBAAkB,IAAI;AACxB,aAAK,SAAS,KAAK;AAAA,UACjB,GAAG;AAAA,UACH,IAAI,KAAK;AAAA;AAAA,QACnB,CAAS;AACD,aAAK,eAAc;AACnBA,kEAAY,2BAA2B,KAAK,GAAG;AAAA,MAChD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,iBAAiB,MAAM;AACrB,YAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,KAAK,GAAG;AAC7D,UAAI,UAAU,IAAI;AAChB,aAAK,SAAS,KAAK,IAAI;AAAA,UACrB,GAAG;AAAA,UACH,IAAI,KAAK;AAAA;AAAA,QACnB;AACQ,aAAK,eAAc;AACnBA,kEAAY,4BAA4B,KAAK,GAAG;AAAA,MACjD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,mBAAmB,QAAQ;AACzB,YAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,MAAM;AAC3D,UAAI,UAAU,IAAI;AAChB,aAAK,SAAS,OAAO,OAAO,CAAC;AAC7B,aAAK,eAAc;AACnBA,sBAAY,MAAA,MAAA,OAAA,yBAAA,4BAA4B,MAAM;AAAA,MAC/C;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,gBAAgB;AACpB,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,6BAA6B;AAEzC,cAAM,YAAYG,WAAAA;AAClB,YAAI,CAAC,UAAU,SAAS;AACtBH,wBAAAA,6CAAa,wBAAwB;AACrC;AAAA,QACD;AAED,cAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,YAAY;AAAA,UAC1C,MAAM;AAAA,UACN,UAAU;AAAA,UACV,kBAAkB;AAAA,QAC5B,CAAS;AAED,YAAI,OAAO,YAAY,GAAG;AAExB,eAAK,WAAW,OAAO,KAAK,IAAI,WAAS;AAAA,YACvC,GAAG;AAAA,YACH,IAAI,KAAK;AAAA;AAAA,UACV,EAAC;AAEF,eAAK,eAAc;AACnBJ,8BAAA,MAAA,OAAA,yBAAY,0BAA0B,OAAO,KAAK,QAAQ,KAAK;AAAA,QACzE,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,UAAU,MAAM;AAAA,QACxC;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,yBAAc,uBAAuB,KAAK;AAC1C,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAOD,gBAAgB,MAAM;AACpB,YAAM,gBAAgB,KAAK,SAAS,UAAU,OAAK,EAAE,OAAO,KAAK,MAAM,EAAE,QAAQ,KAAK,GAAG;AACzF,UAAI,kBAAkB,IAAI;AACxB,aAAK,SAAS,KAAK;AAAA,UACjB,GAAG;AAAA,UACH,IAAI,KAAK,OAAO,KAAK;AAAA,QAC/B,CAAS;AACD,aAAK,eAAgB;AACrBA,kEAAY,wBAAwB,KAAK,KAAK,EAAE;AAAA,MACjD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,mBAAmB,MAAM;AACvB,YAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,OAAO,KAAK,MAAM,EAAE,QAAQ,KAAK,GAAG;AACjF,UAAI,UAAU,IAAI;AAChB,aAAK,SAAS,KAAK,IAAI;AAAA,UACrB,GAAG;AAAA,UACH,IAAI,KAAK,OAAO,KAAK;AAAA,QACtB;AACD,aAAK,eAAgB;AACrBA,kEAAY,wBAAwB,KAAK,KAAK,EAAE;AAAA,MACjD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe,QAAQ;AACrB,YAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,OAAO,UAAU,EAAE,QAAQ,MAAM;AAC9E,UAAI,UAAU,IAAI;AAChB,cAAM,cAAc,KAAK,SAAS,OAAO,OAAO,CAAC,EAAE,CAAC;AACpD,aAAK,eAAgB;AACrBA,4BAAY,MAAA,OAAA,yBAAA,wBAAwB,YAAY,KAAK,EAAE;AAAA,MACxD;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,YAAY;AAChBA,oBAAAA,MAAA,MAAA,OAAA,yBAAY,oCAAoC;AAEhD,UAAI,CAAC,KAAK,UAAU;AAClBA,sBAAAA,MAAA,MAAA,QAAA,yBAAa,sDAAsD;AACnE,eAAO,EAAE,YAAY,OAAO,cAAc,GAAG,QAAQ;MACtD;AAED,UAAI;AAEF,cAAM,gBAAgB,KAAK,SAAS,OAAO,UAAQ,KAAK,SAAS;AACjE,YAAI,cAAc,SAAS,GAAG;AAC5BA,8BAAA,MAAA,OAAA,yBAAY,yBAAyB,cAAc,MAAM,oBAAoB;AAC7E,gBAAM,KAAK;QACZ;AAGD,cAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,gBAAgB,MAAM,WAAW;AAEvC,YAAI,cAAc,YAAY,GAAG;AAC/B,gBAAM,IAAI,MAAM,cAAc,UAAU,YAAY;AAAA,QACrD;AAED,cAAM,eAAe,cAAc,QAAQ;AAC3CJ,sBAAA,MAAA,MAAA,OAAA,yBAAY,uCAAuC,YAAY;AAG/D,cAAM,eAAe,KAAK;AAC1BA,sBAAY,MAAA,MAAA,OAAA,yBAAA,8BAA8B,YAAY;AAGtD,cAAM,YAAY,KAAK,0BAA0B,cAAc,YAAY;AAE3E,YAAI,CAAC,WAAW;AACdA,wBAAAA,MAAY,MAAA,OAAA,yBAAA,gDAAgD;AAC5D,iBAAO,EAAE,YAAY,OAAO,cAAc,GAAG,QAAQ;QACtD;AAGDA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,uEAAuE;AACnF,cAAM,aAAa,MAAM,KAAK,4BAA4B,YAAY;AAGtE,aAAK,0BAAyB;AAE9BA,sBAAY,MAAA,MAAA,OAAA,yBAAA,qCAAqC,UAAU;AAC3D,eAAO;AAAA,MAER,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,yBAAA,kCAAkC,KAAK;AACrD,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,4BAA4B;AAC1B,aAAO;AAAA,QACL,OAAO,KAAK,SAAS;AAAA,QACrB,cAAc,KAAK,sBAAuB;AAAA,QAC1C,UAAU,KAAK,yBAAyB,KAAK,QAAQ;AAAA,MAC7D;AAAA,IACK;AAAA;AAAA,IAGD,yBAAyB,QAAQ;AAC/B,YAAM,UAAU,OACb,IAAI,OAAK,GAAG,EAAE,GAAG,IAAI,EAAE,KAAK,IAAI,EAAE,cAAc,EAAE,UAAU,IAAI,EAAE,WAAW,EAAE,EAC/E,KAAM,EACN,KAAK,GAAG;AAGX,UAAI,OAAO;AACX,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAM,OAAO,QAAQ,WAAW,CAAC;AACjC,gBAAS,QAAQ,KAAK,OAAQ;AAC9B,eAAO,OAAO;AAAA,MACf;AACD,aAAO,KAAK,SAAS,EAAE;AAAA,IACxB;AAAA;AAAA,IAGD,0BAA0B,cAAc,cAAc;AAEpD,UAAI,aAAa,UAAU,aAAa,OAAO;AAC7CA,sBAAAA,4CAAY,0CAA0C,aAAa,OAAO,MAAM,aAAa,KAAK;AAClG,eAAO;AAAA,MACR;AAGD,UAAI,aAAa,aAAa,aAAa,UAAU;AACnDA,sBAAAA,MAAY,MAAA,OAAA,yBAAA,6CAA6C,aAAa,UAAU,MAAM,aAAa,QAAQ;AAC3G,eAAO;AAAA,MACR;AAGD,UAAI,aAAa,gBAAgB,aAAa,cAAc;AAC1D,cAAM,YAAY,IAAI,KAAK,aAAa,YAAY,EAAE;AACtD,cAAM,YAAY,IAAI,KAAK,aAAa,YAAY,EAAE;AAEtD,YAAI,YAAY,WAAW;AACzBA,wBAAAA,MAAY,MAAA,OAAA,yBAAA,oCAAoC,aAAa,cAAc,MAAM,aAAa,YAAY;AAC1G,iBAAO;AAAA,QACR;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,MAAM,4BAA4B,cAAc;AAE9C,YAAM,aAAaI,cAAAA,GAAS,aAAa,aAAa;AACtD,YAAM,SAAS,MAAM,WAAW,YAAY;AAAA,QAC1C,MAAM;AAAA,QACN,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,MACxB,CAAO;AAED,UAAI,OAAO,YAAY,GAAG;AACxB,cAAM,IAAI,MAAM,OAAO,UAAU,YAAY;AAAA,MAC9C;AAED,YAAM,cAAc,OAAO,QAAQ;AACnC,YAAM,cAAc,KAAK,SAAS;AAGlC,YAAM,aAAa,KAAK,eAAe,KAAK,UAAU,WAAW;AAGjE,YAAM,KAAK,kBAAkB,UAAU;AAEvC,YAAM,aAAa,KAAK,SAAS;AACjC,YAAM,eAAe,KAAK,IAAI,aAAa,WAAW;AAEtD,WAAK,gBAAe,oBAAI,KAAM,GAAC,YAAW;AAC1C,WAAK,eAAc;AAEnB,aAAO;AAAA,QACL,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MAChB;AAAA,IACK;AAAA;AAAA,IAGD,MAAM,kBAAkB,SAAS;AAC/B,UAAI;AAEF,cAAM,IAAI,QAAQ,CAAC,YAAY;AAC7B,cAAI,KAAK,WAAW;AAClB,iBAAK,UAAU,MAAM;AACnB,mBAAK,WAAW,QAAQ,IAAI,WAAS;AAAA,gBACnC,GAAG;AAAA,gBACH,IAAI,KAAK;AAAA;AAAA,cACV,EAAC;AACF;YACd,CAAa;AAAA,UACb,OAAiB;AACL,uBAAW,MAAM;AACf,mBAAK,WAAW,QAAQ,IAAI,WAAS;AAAA,gBACnC,GAAG;AAAA,gBACH,IAAI,KAAK;AAAA;AAAA,cACV,EAAC;AACF;YACD,GAAE,CAAC;AAAA,UACL;AAAA,QACX,CAAS;AAAA,MACF,SAAQ,OAAO;AACdJ,sBAAc,MAAA,MAAA,SAAA,yBAAA,wEAAwE,KAAK;AAC3F,aAAK,WAAW,QAAQ,IAAI,WAAS;AAAA,UACnC,GAAG;AAAA,UACH,IAAI,KAAK;AAAA;AAAA,QACV,EAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOD,uBAAuB,QAAQ,QAAQ;AACrCA,0BAAY,MAAA,OAAA,yBAAA,wBAAwB,MAAM,MAAM,MAAM,EAAE;AAGxD,WAAK;AAGLA,oBAAG,MAAC,MAAM,qBAAqB;AAAA,QAC7B,WAAW,KAAK,IAAK;AAAA,QACrB;AAAA,QACA;AAAA,QACA,eAAe,KAAK;AAAA,MAC5B,CAAO;AAGD,WAAK,aAAa,KAAK,UAAU,MAAM;AACrCA,4BAAA,MAAA,OAAA,yBAAY,wBAAwB,MAAM,MAAM,MAAM,EAAE;AAAA,MAChE,CAAO;AAEDA,0BAAA,MAAA,OAAA,yBAAY,+BAA+B,KAAK,iBAAiB,EAAE;AAAA,IACpE;AAAA;AAAA,IAGD,4BAA4B;AAC1B,UAAI,eAAe;AAEnB,WAAK,SAAS,QAAQ,UAAQ;AAE5B,YAAI,KAAK,cAAc,CAAC,KAAK,OAAO,CAAC,KAAK,QAAQ;AAChDA,8BAAY,MAAA,OAAA,yBAAA,0BAA0B,KAAK,SAAS,MAAM,EAAE;AAC5D,iBAAO,KAAK;AACZ;AAAA,QACD;AAAA,MACT,CAAO;AAED,UAAI,eAAe,GAAG;AACpBA,4BAAY,MAAA,OAAA,yBAAA,mBAAmB,YAAY,WAAW;AACtD,aAAK,eAAc;AAAA,MACpB;AAED,aAAO;AAAA,IACR;AAAA,EACF;AACH,CAAC;;"}