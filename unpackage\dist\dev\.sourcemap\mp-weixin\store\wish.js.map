{"version": 3, "file": "wish.js", "sources": ["store/wish.js"], "sourcesContent": ["/**\n * 心愿数据管理 Store - 简化版本\n */\n\nimport { defineStore } from 'pinia'\n\n// 错误代码常量\nconst ERROR_CODES = {\n  NETWORK_ERROR: 'NETWORK_ERROR',\n  AUTH_ERROR: 'AUTH_ERROR',\n  VALIDATION_ERROR: 'VALIDATION_ERROR',\n  PERMISSION_ERROR: 'PERMISSION_ERROR',\n  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',\n  SERVER_ERROR: 'SERVER_ERROR'\n}\n\n// 错误处理工具类\nconst ErrorHandler = {\n  getUserFriendlyMessage(error) {\n    if (typeof error === 'string') return error\n    return error.message || error.errMsg || '未知错误'\n  },\n  \n  isNetworkError(error) {\n    if (error.errCode === ERROR_CODES.NETWORK_ERROR) return true\n    if (error.code === 'NETWORK_ERROR') return true\n    return false\n  },\n  \n  isVersionConflictError(error) {\n    return error && error.message && (\n      error.message.includes('数据版本冲突') ||\n      error.message.includes('逻辑时钟冲突') ||\n      error.message.includes('version conflict') ||\n      error.message.includes('conflict')\n    )\n  },\n\n  showError(error, title = '操作失败', context = 'unknown') {\n    const message = this.getUserFriendlyMessage(error)\n    uni.showToast({\n      title: message,\n      icon: 'none',\n      duration: 3000\n    })\n    console.error(`${title}:`, error)\n  }\n}\n\nexport const useWishStore = defineStore('wish', {\n  state: () => ({\n    wishList: [],\n    currentGroupId: 'all',\n    isLoading: false,\n    lastSyncTime: null,\n    isOnline: true\n  }),\n\n  getters: {\n    activeWishes: (state) => {\n      return state.wishList.filter(wish => !wish._deleted && !wish.isCompleted)\n    }\n  },\n\n  actions: {\n    async initWishList() {\n      console.log('[wishStore] Initializing wish list...')\n      this.isLoading = true\n\n      try {\n        // 从本地存储加载数据\n        const storedWishes = uni.getStorageSync('wishList')\n        if (storedWishes) {\n          const parsed = JSON.parse(storedWishes)\n          this.wishList = parsed.map(wish => ({\n            ...wish,\n            id: wish.id || wish._id\n          }))\n        }\n\n        console.log('[wishStore] Wish list initialization completed')\n      } catch (error) {\n        console.error('[wishStore] Failed to initialize wish list:', error)\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    // 🚀 添加基本的同步方法来测试云函数修改\n    async syncFromCloud() {\n      console.log('[wishStore] 开始从云端同步数据...')\n\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.getWishesByUser()\n\n        if (result.errCode === 0) {\n          this.wishList = result.data.map(wish => ({\n            ...wish,\n            id: wish._id\n          }))\n\n          // 保存到本地存储\n          uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          this.lastSyncTime = new Date().toISOString()\n\n          console.log('[wishStore] 同步成功，获取到', result.data.length, '个心愿')\n        } else {\n          console.error('[wishStore] 同步失败:', result.errMsg)\n        }\n      } catch (error) {\n        console.error('[wishStore] 同步出错:', error)\n        ErrorHandler.showError(error, '同步失败')\n      }\n    },\n\n    // 🚀 添加删除方法来测试多设备冲突处理\n    async deleteWish(id) {\n      console.log('[wishStore] 删除心愿:', id)\n\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.deleteWish(id)\n\n        if (result.errCode === 0) {\n          // 从本地列表中移除\n          const index = this.wishList.findIndex(w => w._id === id || w.id === id)\n          if (index !== -1) {\n            this.wishList.splice(index, 1)\n            uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          }\n\n          uni.showToast({\n            title: '删除成功',\n            icon: 'success'\n          })\n\n          console.log('[wishStore] 删除成功')\n        } else {\n          console.error('[wishStore] 删除失败:', result.errMsg)\n          ErrorHandler.showError({ message: result.errMsg }, '删除失败')\n        }\n      } catch (error) {\n        console.error('[wishStore] 删除出错:', error)\n        ErrorHandler.showError(error, '删除失败')\n      }\n    }\n  }\n})\n"], "names": ["uni", "defineStore", "uniCloud"], "mappings": ";;;AAOA,MAAM,cAAc;AAAA,EAClB,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,cAAc;AAChB;AAGA,MAAM,eAAe;AAAA,EACnB,uBAAuB,OAAO;AAC5B,QAAI,OAAO,UAAU;AAAU,aAAO;AACtC,WAAO,MAAM,WAAW,MAAM,UAAU;AAAA,EACzC;AAAA,EAED,eAAe,OAAO;AACpB,QAAI,MAAM,YAAY,YAAY;AAAe,aAAO;AACxD,QAAI,MAAM,SAAS;AAAiB,aAAO;AAC3C,WAAO;AAAA,EACR;AAAA,EAED,uBAAuB,OAAO;AAC5B,WAAO,SAAS,MAAM,YACpB,MAAM,QAAQ,SAAS,QAAQ,KAC/B,MAAM,QAAQ,SAAS,QAAQ,KAC/B,MAAM,QAAQ,SAAS,kBAAkB,KACzC,MAAM,QAAQ,SAAS,UAAU;AAAA,EAEpC;AAAA,EAED,UAAU,OAAO,QAAQ,QAAQ,UAAU,WAAW;AACpD,UAAM,UAAU,KAAK,uBAAuB,KAAK;AACjDA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IAChB,CAAK;AACDA,wBAAA,MAAA,SAAA,uBAAc,GAAG,KAAK,KAAK,KAAK;AAAA,EACjC;AACH;AAEY,MAAC,eAAeC,cAAW,YAAC,QAAQ;AAAA,EAC9C,OAAO,OAAO;AAAA,IACZ,UAAU,CAAE;AAAA,IACZ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,EACd;AAAA,EAEE,SAAS;AAAA,IACP,cAAc,CAAC,UAAU;AACvB,aAAO,MAAM,SAAS,OAAO,UAAQ,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW;AAAA,IACzE;AAAA,EACF;AAAA,EAED,SAAS;AAAA,IACP,MAAM,eAAe;AACnBD,oBAAAA,MAAY,MAAA,OAAA,uBAAA,uCAAuC;AACnD,WAAK,YAAY;AAEjB,UAAI;AAEF,cAAM,eAAeA,cAAAA,MAAI,eAAe,UAAU;AAClD,YAAI,cAAc;AAChB,gBAAM,SAAS,KAAK,MAAM,YAAY;AACtC,eAAK,WAAW,OAAO,IAAI,WAAS;AAAA,YAClC,GAAG;AAAA,YACH,IAAI,KAAK,MAAM,KAAK;AAAA,UAChC,EAAY;AAAA,QACH;AAEDA,sBAAAA,MAAY,MAAA,OAAA,uBAAA,gDAAgD;AAAA,MAC7D,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,uBAAc,+CAA+C,KAAK;AAAA,MAC1E,UAAgB;AACR,aAAK,YAAY;AAAA,MAClB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpBA,oBAAAA,MAAY,MAAA,OAAA,uBAAA,0BAA0B;AAEtC,UAAI;AACF,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,gBAAiB;AAEjD,YAAI,OAAO,YAAY,GAAG;AACxB,eAAK,WAAW,OAAO,KAAK,IAAI,WAAS;AAAA,YACvC,GAAG;AAAA,YACH,IAAI,KAAK;AAAA,UACrB,EAAY;AAGFF,wBAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC5D,eAAK,gBAAe,oBAAI,KAAI,GAAG,YAAa;AAE5CA,8BAAY,MAAA,OAAA,wBAAA,wBAAwB,OAAO,KAAK,QAAQ,KAAK;AAAA,QACvE,OAAe;AACLA,wBAAA,MAAA,MAAA,SAAA,wBAAc,qBAAqB,OAAO,MAAM;AAAA,QACjD;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,wBAAA,qBAAqB,KAAK;AACxC,qBAAa,UAAU,OAAO,MAAM;AAAA,MACrC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,WAAW,IAAI;AACnBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,qBAAqB,EAAE;AAEnC,UAAI;AACF,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,WAAW,EAAE;AAE7C,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AACtE,cAAI,UAAU,IAAI;AAChB,iBAAK,SAAS,OAAO,OAAO,CAAC;AAC7BF,0BAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,UAC7D;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAEDA,wBAAAA,MAAA,MAAA,OAAA,wBAAY,kBAAkB;AAAA,QACxC,OAAe;AACLA,wBAAA,MAAA,MAAA,SAAA,wBAAc,qBAAqB,OAAO,MAAM;AAChD,uBAAa,UAAU,EAAE,SAAS,OAAO,OAAQ,GAAE,MAAM;AAAA,QAC1D;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,wBAAA,qBAAqB,KAAK;AACxC,qBAAa,UAAU,OAAO,MAAM;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACH,CAAC;;"}