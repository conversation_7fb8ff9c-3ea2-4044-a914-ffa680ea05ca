{"version": 3, "file": "wish.js", "sources": ["store/wish.js"], "sourcesContent": ["/**\n * 心愿数据管理 Store - 简化版本\n */\n\nimport { defineStore } from 'pinia'\n\n// 错误代码常量\nconst ERROR_CODES = {\n  NETWORK_ERROR: 'NETWORK_ERROR',\n  AUTH_ERROR: 'AUTH_ERROR',\n  VALIDATION_ERROR: 'VALIDATION_ERROR',\n  PERMISSION_ERROR: 'PERMISSION_ERROR',\n  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',\n  SERVER_ERROR: 'SERVER_ERROR'\n}\n\n// 错误处理工具类\nconst ErrorHandler = {\n  getUserFriendlyMessage(error) {\n    if (typeof error === 'string') return error\n    return error.message || error.errMsg || '未知错误'\n  },\n  \n  isNetworkError(error) {\n    if (error.errCode === ERROR_CODES.NETWORK_ERROR) return true\n    if (error.code === 'NETWORK_ERROR') return true\n    return false\n  },\n  \n  isVersionConflictError(error) {\n    return error && error.message && (\n      error.message.includes('数据版本冲突') ||\n      error.message.includes('逻辑时钟冲突') ||\n      error.message.includes('version conflict') ||\n      error.message.includes('conflict')\n    )\n  },\n\n  showError(error, title = '操作失败', context = 'unknown') {\n    const message = this.getUserFriendlyMessage(error)\n    uni.showToast({\n      title: message,\n      icon: 'none',\n      duration: 3000\n    })\n    console.error(`${title}:`, error)\n  }\n}\n\nexport const useWishStore = defineStore('wish', {\n  state: () => ({\n    wishList: [],\n    currentGroupId: 'all',\n    isLoading: false,\n    lastSyncTime: null,\n    isOnline: true,\n    listUpdateCounter: 0  // 🚀 添加列表更新计数器\n  }),\n\n  getters: {\n    activeWishes: (state) => {\n      return state.wishList.filter(wish => !wish._deleted && !wish.isCompleted)\n    },\n\n    // 🚀 添加当前分组的心愿列表\n    currentGroupWishes: (state) => {\n      const activeWishes = state.wishList.filter(wish => !wish._deleted && !wish.isCompleted)\n\n      if (state.currentGroupId === 'all') {\n        return activeWishes\n      } else if (state.currentGroupId === 'completed') {\n        return state.wishList.filter(wish => !wish._deleted && wish.isCompleted)\n      } else {\n        // 特定分组\n        return activeWishes.filter(wish =>\n          wish.groupIds && wish.groupIds.includes(state.currentGroupId)\n        )\n      }\n    },\n\n    // 🚀 添加其他必要的 getters\n    getWishById: (state) => (id) => {\n      return state.wishList.find(wish =>\n        (wish._id === id || wish.id === id) && !wish._deleted\n      ) || null\n    }\n  },\n\n  actions: {\n    async initWishList() {\n      console.log('[wishStore] Initializing wish list...')\n      this.isLoading = true\n\n      try {\n        // 从本地存储加载数据\n        const storedWishes = uni.getStorageSync('wishList')\n        if (storedWishes) {\n          const parsed = JSON.parse(storedWishes)\n          this.wishList = parsed.map(wish => ({\n            ...wish,\n            id: wish.id || wish._id\n          }))\n        }\n\n        console.log('[wishStore] Wish list initialization completed')\n      } catch (error) {\n        console.error('[wishStore] Failed to initialize wish list:', error)\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    // 🚀 添加基本的同步方法来测试云函数修改\n    async syncFromCloud() {\n      console.log('[wishStore] 开始从云端同步数据...')\n\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.getWishesByUser()\n\n        if (result.errCode === 0) {\n          this.wishList = result.data.map(wish => ({\n            ...wish,\n            id: wish._id\n          }))\n\n          // 保存到本地存储\n          uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          this.lastSyncTime = new Date().toISOString()\n\n          console.log('[wishStore] 同步成功，获取到', result.data.length, '个心愿')\n        } else {\n          console.error('[wishStore] 同步失败:', result.errMsg)\n        }\n      } catch (error) {\n        console.error('[wishStore] 同步出错:', error)\n        ErrorHandler.showError(error, '同步失败')\n      }\n    },\n\n    // 🚀 添加删除方法来测试多设备冲突处理\n    async deleteWish(id) {\n      console.log('[wishStore] 删除心愿:', id)\n\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.deleteWish(id)\n\n        if (result.errCode === 0) {\n          // 从本地列表中移除\n          const index = this.wishList.findIndex(w => w._id === id || w.id === id)\n          if (index !== -1) {\n            this.wishList.splice(index, 1)\n            uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          }\n\n          uni.showToast({\n            title: '删除成功',\n            icon: 'success'\n          })\n\n          console.log('[wishStore] 删除成功')\n        } else {\n          console.error('[wishStore] 删除失败:', result.errMsg)\n          ErrorHandler.showError({ message: result.errMsg }, '删除失败')\n        }\n      } catch (error) {\n        console.error('[wishStore] 删除出错:', error)\n        ErrorHandler.showError(error, '删除失败')\n      }\n    },\n\n    // 🚀 添加其他必要的方法\n    setCurrentGroup(groupId) {\n      this.currentGroupId = groupId\n    },\n\n    // 强制同步数据（用于下拉刷新）\n    async forceSyncData() {\n      console.log('[wishStore] 强制同步数据...')\n      await this.syncFromCloud()\n    },\n\n    // 手动同步（兼容原有接口）\n    async manualSync(silent = false) {\n      if (!silent) {\n        uni.showLoading({ title: '同步中...' })\n      }\n\n      try {\n        await this.syncFromCloud()\n\n        if (!silent) {\n          uni.hideLoading()\n          uni.showToast({\n            title: '同步完成',\n            icon: 'success'\n          })\n        }\n      } catch (error) {\n        if (!silent) {\n          uni.hideLoading()\n        }\n        throw error\n      }\n    },\n\n    // 完成心愿\n    async completeWish(id) {\n      console.log('[wishStore] 完成心愿:', id)\n\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.completeWish(id)\n\n        if (result.errCode === 0) {\n          // 更新本地数据\n          const wish = this.wishList.find(w => w._id === id || w.id === id)\n          if (wish) {\n            wish.isCompleted = true\n            wish.completeDate = new Date().toISOString()\n            uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          }\n\n          uni.showToast({\n            title: '心愿已完成',\n            icon: 'success'\n          })\n        } else {\n          ErrorHandler.showError({ message: result.errMsg }, '完成失败')\n        }\n      } catch (error) {\n        console.error('[wishStore] 完成心愿出错:', error)\n        ErrorHandler.showError(error, '完成失败')\n      }\n    },\n\n    // 恢复心愿\n    async restoreWish(id) {\n      console.log('[wishStore] 恢复心愿:', id)\n\n      try {\n        const wishCenter = uniCloud.importObject('wish-center')\n        const result = await wishCenter.restoreWish(id)\n\n        if (result.errCode === 0) {\n          // 更新本地数据\n          const wish = this.wishList.find(w => w._id === id || w.id === id)\n          if (wish) {\n            wish.isCompleted = false\n            wish.completeDate = null\n            uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n          }\n\n          uni.showToast({\n            title: '心愿已恢复',\n            icon: 'success'\n          })\n        } else {\n          ErrorHandler.showError({ message: result.errMsg }, '恢复失败')\n        }\n      } catch (error) {\n        console.error('[wishStore] 恢复心愿出错:', error)\n        ErrorHandler.showError(error, '恢复失败')\n      }\n    },\n\n    // 🚀 清理本地数据（登录后重新初始化时使用）\n    clearLocalData() {\n      console.log('[wishStore] 清理本地数据...')\n\n      // 清空当前数据\n      this.wishList = []\n      this.currentGroupId = 'all'\n      this.lastSyncTime = null\n      this.isLoading = false\n\n      // 清理本地存储\n      uni.removeStorageSync('wishList')\n      uni.removeStorageSync('wishLastSyncTime')\n\n      console.log('[wishStore] 本地数据清理完成')\n    },\n\n    // 🚀 强制初始化（登录后使用）\n    async forceInit() {\n      console.log('[wishStore] 强制初始化...')\n\n      // 清空当前数据\n      this.clearLocalData()\n\n      // 强制从云端同步\n      await this.syncFromCloud()\n\n      console.log('[wishStore] 强制初始化完成')\n    },\n\n    // 🚀 用户登出时清理数据\n    clearUserData() {\n      console.log('[wishStore] 清理用户数据...')\n\n      // 清空用户的心愿数据\n      this.wishList = []\n\n      // 重置为默认分组（全部）\n      this.currentGroupId = 'all'\n\n      this.lastSyncTime = null\n      this.isLoading = false\n\n      // 清除本地存储中的用户数据\n      uni.removeStorageSync('wishList')\n      uni.removeStorageSync('wishLastSyncTime')\n\n      console.log('[wishStore] 用户数据清理完成')\n    },\n\n    // 🚀 刷新心愿列表（从本地存储重新加载）\n    refreshWishList() {\n      console.log('[wishStore] 刷新心愿列表...')\n\n      try {\n        const storedWishes = uni.getStorageSync('wishList')\n        if (storedWishes) {\n          const parsed = JSON.parse(storedWishes)\n          this.wishList = parsed.map(wish => ({\n            ...wish,\n            id: wish.id || wish._id\n          }))\n          console.log('[wishStore] 心愿列表已刷新，共', this.wishList.length, '个心愿')\n        }\n      } catch (error) {\n        console.error('[wishStore] 刷新心愿列表失败:', error)\n      }\n    },\n\n    // 🚀 更新心愿排序\n    updateWishOrder(orderedIds) {\n      console.log('[wishStore] 更新心愿排序:', orderedIds)\n\n      // 简单实现：根据新的ID顺序重新排列心愿\n      const reorderedWishes = []\n      orderedIds.forEach(id => {\n        const wish = this.wishList.find(w => w._id === id || w.id === id)\n        if (wish) {\n          reorderedWishes.push(wish)\n        }\n      })\n\n      // 添加不在排序列表中的心愿\n      this.wishList.forEach(wish => {\n        if (!orderedIds.includes(wish._id) && !orderedIds.includes(wish.id)) {\n          reorderedWishes.push(wish)\n        }\n      })\n\n      this.wishList = reorderedWishes\n      uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n\n      console.log('[wishStore] 心愿排序已更新')\n    },\n\n    // 🚀 设置当前心愿列表\n    setCurrentList(newWishList) {\n      console.log('[wishStore] 设置当前心愿列表:', newWishList.length, '个心愿')\n\n      this.wishList = newWishList.map(wish => ({\n        ...wish,\n        id: wish.id || wish._id\n      }))\n\n      uni.setStorageSync('wishList', JSON.stringify(this.wishList))\n    }\n  }\n})\n"], "names": ["uni", "defineStore", "uniCloud"], "mappings": ";;;AAOA,MAAM,cAAc;AAAA,EAClB,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,cAAc;AAChB;AAGA,MAAM,eAAe;AAAA,EACnB,uBAAuB,OAAO;AAC5B,QAAI,OAAO,UAAU;AAAU,aAAO;AACtC,WAAO,MAAM,WAAW,MAAM,UAAU;AAAA,EACzC;AAAA,EAED,eAAe,OAAO;AACpB,QAAI,MAAM,YAAY,YAAY;AAAe,aAAO;AACxD,QAAI,MAAM,SAAS;AAAiB,aAAO;AAC3C,WAAO;AAAA,EACR;AAAA,EAED,uBAAuB,OAAO;AAC5B,WAAO,SAAS,MAAM,YACpB,MAAM,QAAQ,SAAS,QAAQ,KAC/B,MAAM,QAAQ,SAAS,QAAQ,KAC/B,MAAM,QAAQ,SAAS,kBAAkB,KACzC,MAAM,QAAQ,SAAS,UAAU;AAAA,EAEpC;AAAA,EAED,UAAU,OAAO,QAAQ,QAAQ,UAAU,WAAW;AACpD,UAAM,UAAU,KAAK,uBAAuB,KAAK;AACjDA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IAChB,CAAK;AACDA,wBAAA,MAAA,SAAA,uBAAc,GAAG,KAAK,KAAK,KAAK;AAAA,EACjC;AACH;AAEY,MAAC,eAAeC,cAAW,YAAC,QAAQ;AAAA,EAC9C,OAAO,OAAO;AAAA,IACZ,UAAU,CAAE;AAAA,IACZ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,mBAAmB;AAAA;AAAA,EACvB;AAAA,EAEE,SAAS;AAAA,IACP,cAAc,CAAC,UAAU;AACvB,aAAO,MAAM,SAAS,OAAO,UAAQ,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW;AAAA,IACzE;AAAA;AAAA,IAGD,oBAAoB,CAAC,UAAU;AAC7B,YAAM,eAAe,MAAM,SAAS,OAAO,UAAQ,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW;AAEtF,UAAI,MAAM,mBAAmB,OAAO;AAClC,eAAO;AAAA,MACf,WAAiB,MAAM,mBAAmB,aAAa;AAC/C,eAAO,MAAM,SAAS,OAAO,UAAQ,CAAC,KAAK,YAAY,KAAK,WAAW;AAAA,MAC/E,OAAa;AAEL,eAAO,aAAa;AAAA,UAAO,UACzB,KAAK,YAAY,KAAK,SAAS,SAAS,MAAM,cAAc;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,CAAC,UAAU,CAAC,OAAO;AAC9B,aAAO,MAAM,SAAS;AAAA,QAAK,WACxB,KAAK,QAAQ,MAAM,KAAK,OAAO,OAAO,CAAC,KAAK;AAAA,MACrD,KAAW;AAAA,IACN;AAAA,EACF;AAAA,EAED,SAAS;AAAA,IACP,MAAM,eAAe;AACnBD,oBAAAA,MAAY,MAAA,OAAA,uBAAA,uCAAuC;AACnD,WAAK,YAAY;AAEjB,UAAI;AAEF,cAAM,eAAeA,cAAAA,MAAI,eAAe,UAAU;AAClD,YAAI,cAAc;AAChB,gBAAM,SAAS,KAAK,MAAM,YAAY;AACtC,eAAK,WAAW,OAAO,IAAI,WAAS;AAAA,YAClC,GAAG;AAAA,YACH,IAAI,KAAK,MAAM,KAAK;AAAA,UAChC,EAAY;AAAA,QACH;AAEDA,sBAAAA,MAAY,MAAA,OAAA,wBAAA,gDAAgD;AAAA,MAC7D,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,+CAA+C,KAAK;AAAA,MAC1E,UAAgB;AACR,aAAK,YAAY;AAAA,MAClB;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpBA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,0BAA0B;AAEtC,UAAI;AACF,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,gBAAiB;AAEjD,YAAI,OAAO,YAAY,GAAG;AACxB,eAAK,WAAW,OAAO,KAAK,IAAI,WAAS;AAAA,YACvC,GAAG;AAAA,YACH,IAAI,KAAK;AAAA,UACrB,EAAY;AAGFF,wBAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC5D,eAAK,gBAAe,oBAAI,KAAI,GAAG,YAAa;AAE5CA,8BAAY,MAAA,OAAA,wBAAA,wBAAwB,OAAO,KAAK,QAAQ,KAAK;AAAA,QACvE,OAAe;AACLA,wBAAA,MAAA,MAAA,SAAA,wBAAc,qBAAqB,OAAO,MAAM;AAAA,QACjD;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,wBAAA,qBAAqB,KAAK;AACxC,qBAAa,UAAU,OAAO,MAAM;AAAA,MACrC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,WAAW,IAAI;AACnBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,qBAAqB,EAAE;AAEnC,UAAI;AACF,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,WAAW,EAAE;AAE7C,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,QAAQ,KAAK,SAAS,UAAU,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AACtE,cAAI,UAAU,IAAI;AAChB,iBAAK,SAAS,OAAO,OAAO,CAAC;AAC7BF,0BAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,UAC7D;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAEDA,wBAAAA,MAAA,MAAA,OAAA,wBAAY,kBAAkB;AAAA,QACxC,OAAe;AACLA,wBAAA,MAAA,MAAA,SAAA,wBAAc,qBAAqB,OAAO,MAAM;AAChD,uBAAa,UAAU,EAAE,SAAS,OAAO,OAAQ,GAAE,MAAM;AAAA,QAC1D;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,wBAAA,qBAAqB,KAAK;AACxC,qBAAa,UAAU,OAAO,MAAM;AAAA,MACrC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACvB,WAAK,iBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB;AACnC,YAAM,KAAK,cAAe;AAAA,IAC3B;AAAA;AAAA,IAGD,MAAM,WAAW,SAAS,OAAO;AAC/B,UAAI,CAAC,QAAQ;AACXA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAQ,CAAE;AAAA,MACpC;AAED,UAAI;AACF,cAAM,KAAK,cAAe;AAE1B,YAAI,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAa;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACd,YAAI,CAAC,QAAQ;AACXA,wBAAAA,MAAI,YAAa;AAAA,QAClB;AACD,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,aAAa,IAAI;AACrBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,qBAAqB,EAAE;AAEnC,UAAI;AACF,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,aAAa,EAAE;AAE/C,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,cAAI,MAAM;AACR,iBAAK,cAAc;AACnB,iBAAK,gBAAe,oBAAI,KAAI,GAAG,YAAa;AAC5CF,0BAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,UAC7D;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAAA,QACX,OAAe;AACL,uBAAa,UAAU,EAAE,SAAS,OAAO,OAAQ,GAAE,MAAM;AAAA,QAC1D;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,uBAAuB,KAAK;AAC1C,qBAAa,UAAU,OAAO,MAAM;AAAA,MACrC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,YAAY,IAAI;AACpBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,qBAAqB,EAAE;AAEnC,UAAI;AACF,cAAM,aAAaE,cAAAA,GAAS,aAAa,aAAa;AACtD,cAAM,SAAS,MAAM,WAAW,YAAY,EAAE;AAE9C,YAAI,OAAO,YAAY,GAAG;AAExB,gBAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,cAAI,MAAM;AACR,iBAAK,cAAc;AACnB,iBAAK,eAAe;AACpBF,0BAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,UAC7D;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAAA,QACX,OAAe;AACL,uBAAa,UAAU,EAAE,SAAS,OAAO,OAAQ,GAAE,MAAM;AAAA,QAC1D;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,uBAAuB,KAAK;AAC1C,qBAAa,UAAU,OAAO,MAAM;AAAA,MACrC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB;AAGnC,WAAK,WAAW,CAAE;AAClB,WAAK,iBAAiB;AACtB,WAAK,eAAe;AACpB,WAAK,YAAY;AAGjBA,oBAAG,MAAC,kBAAkB,UAAU;AAChCA,oBAAG,MAAC,kBAAkB,kBAAkB;AAExCA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,sBAAsB;AAAA,IACnC;AAAA;AAAA,IAGD,MAAM,YAAY;AAChBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,sBAAsB;AAGlC,WAAK,eAAgB;AAGrB,YAAM,KAAK,cAAe;AAE1BA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,qBAAqB;AAAA,IAClC;AAAA;AAAA,IAGD,gBAAgB;AACdA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB;AAGnC,WAAK,WAAW,CAAE;AAGlB,WAAK,iBAAiB;AAEtB,WAAK,eAAe;AACpB,WAAK,YAAY;AAGjBA,oBAAG,MAAC,kBAAkB,UAAU;AAChCA,oBAAG,MAAC,kBAAkB,kBAAkB;AAExCA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,sBAAsB;AAAA,IACnC;AAAA;AAAA,IAGD,kBAAkB;AAChBA,oBAAAA,MAAA,MAAA,OAAA,wBAAY,uBAAuB;AAEnC,UAAI;AACF,cAAM,eAAeA,cAAAA,MAAI,eAAe,UAAU;AAClD,YAAI,cAAc;AAChB,gBAAM,SAAS,KAAK,MAAM,YAAY;AACtC,eAAK,WAAW,OAAO,IAAI,WAAS;AAAA,YAClC,GAAG;AAAA,YACH,IAAI,KAAK,MAAM,KAAK;AAAA,UAChC,EAAY;AACFA,8BAAA,MAAA,OAAA,wBAAY,yBAAyB,KAAK,SAAS,QAAQ,KAAK;AAAA,QACjE;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wBAAc,yBAAyB,KAAK;AAAA,MAC7C;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,YAAY;AAC1BA,oBAAAA,2CAAY,uBAAuB,UAAU;AAG7C,YAAM,kBAAkB,CAAE;AAC1B,iBAAW,QAAQ,QAAM;AACvB,cAAM,OAAO,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,MAAM,EAAE,OAAO,EAAE;AAChE,YAAI,MAAM;AACR,0BAAgB,KAAK,IAAI;AAAA,QAC1B;AAAA,MACT,CAAO;AAGD,WAAK,SAAS,QAAQ,UAAQ;AAC5B,YAAI,CAAC,WAAW,SAAS,KAAK,GAAG,KAAK,CAAC,WAAW,SAAS,KAAK,EAAE,GAAG;AACnE,0BAAgB,KAAK,IAAI;AAAA,QAC1B;AAAA,MACT,CAAO;AAED,WAAK,WAAW;AAChBA,oBAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAE5DA,oBAAAA,MAAY,MAAA,OAAA,wBAAA,qBAAqB;AAAA,IAClC;AAAA;AAAA,IAGD,eAAe,aAAa;AAC1BA,oBAAY,MAAA,MAAA,OAAA,wBAAA,yBAAyB,YAAY,QAAQ,KAAK;AAE9D,WAAK,WAAW,YAAY,IAAI,WAAS;AAAA,QACvC,GAAG;AAAA,QACH,IAAI,KAAK,MAAM,KAAK;AAAA,MAC5B,EAAQ;AAEFA,oBAAG,MAAC,eAAe,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,IAC7D;AAAA,EACF;AACH,CAAC;;"}