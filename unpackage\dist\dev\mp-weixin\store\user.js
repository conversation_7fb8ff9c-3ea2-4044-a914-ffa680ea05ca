"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const utils_loadingManager = require("../utils/loadingManager.js");
const utils_syncManager = require("../utils/syncManager.js");
let uniIdCo = null;
function getUniIdCo() {
  if (!uniIdCo) {
    try {
      if (typeof common_vendor.nr !== "undefined" && common_vendor.nr.importObject) {
        uniIdCo = common_vendor.nr.importObject("uni-id-co", {
          customUI: true
        });
      } else {
        common_vendor.index.__f__("error", "at store/user.js:19", "[store/user.js] uniCloud not available or not initialized");
        return null;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/user.js:23", "[store/user.js] Failed to initialize uni-id-co:", error);
      return null;
    }
  }
  return uniIdCo;
}
const useUserStore = common_vendor.defineStore("user", {
  state: () => ({
    userInfo: (() => {
      try {
        const stored = common_vendor.index.getStorageSync("userInfo");
        return stored ? typeof stored === "string" ? JSON.parse(stored) : stored : {};
      } catch (e) {
        common_vendor.index.__f__("error", "at store/user.js:37", "[userStore] Error parsing userInfo from storage:", e);
        return {};
      }
    })(),
    isLogin: false,
    token: common_vendor.index.getStorageSync("uni_id_token") || common_vendor.index.getStorageSync("token") || "",
    // 优先从uni_id_token读取
    tokenExpired: 0,
    // 是否跳过登录检查，设置为false时要求用户登录才能执行关键操作
    skipLoginCheck: false,
    // 新增：用于存储登录成功后需要执行的动作
    postLoginAction: null,
    // 可以是一个函数引用或一个描述对象
    // 🚀 新增：同步状态管理
    _isSyncInProgress: false,
    // 防止重复同步
    // 🚀 新增：智能验证缓存机制
    lastTokenVerifyTime: 0,
    // 上次token验证时间
    tokenVerifyInterval: 5 * 60 * 1e3,
    // 验证间隔：5分钟
    isTokenVerifying: false,
    // 是否正在验证token，避免并发验证
    appLastActiveTime: Date.now(),
    // 应用最后活跃时间
    quickAuthCache: {
      // 快速验证缓存
      isValid: false,
      timestamp: 0,
      ttl: 30 * 1e3
      // 30秒内有效
    },
    // 🔒 防重复导航机制
    lastNavigationTime: 0,
    // 上次导航时间
    navigationCooldown: 1e3
    // 导航冷却时间：1秒
  }),
  getters: {
    hasLogin: (state) => state.isLogin,
    getUserInfo: (state) => state.userInfo,
    // isLoggedIn 主要依赖 state.isLogin，skipLoginCheck 可作为特殊用途保留
    isLoggedIn: (state) => state.isLogin || state.skipLoginCheck,
    // Getter to safely get nickname
    nickname: (state) => state.userInfo && state.userInfo.nickname ? state.userInfo.nickname : "未登录",
    // Getter to safely get avatar
    avatarUrl: (state) => {
      if (state.userInfo) {
        if (state.userInfo.avatarUrl) {
          return state.userInfo.avatarUrl;
        }
        if (state.userInfo.avatar_file && state.userInfo.avatar_file.url) {
          return state.userInfo.avatar_file.url;
        }
        if (state.userInfo.avatar) {
          return state.userInfo.avatar;
        }
      }
      return "/static/default_avatar.png";
    },
    userId: (state) => state.userInfo && state.userInfo.uid ? state.userInfo.uid : null
  },
  actions: {
    // 登录成功时的处理逻辑
    async loginSuccess(data, showSuccessToast = false) {
      this.isLogin = true;
      this.userInfo = data.userInfo || {};
      this.token = data.token || "";
      this.tokenExpired = data.tokenExpired || 0;
      this.lastTokenVerifyTime = Date.now();
      this.quickAuthCache = {
        isValid: true,
        timestamp: Date.now(),
        ttl: 30 * 1e3
      };
      if (typeof this.userInfo === "string") {
        try {
          this.userInfo = JSON.parse(this.userInfo);
        } catch (e) {
          common_vendor.index.__f__("error", "at store/user.js:115", "[userStore] Error parsing userInfo from string:", e);
          this.userInfo = {};
        }
      }
      common_vendor.index.setStorageSync("userInfo", JSON.stringify(this.userInfo));
      if (this.token) {
        common_vendor.index.setStorageSync("uni_id_token", this.token);
        common_vendor.index.setStorageSync("token", this.token);
      } else {
        common_vendor.index.removeStorageSync("uni_id_token");
        common_vendor.index.removeStorageSync("token");
      }
      await this.initDependentStores(showSuccessToast);
      if (typeof this.postLoginAction === "function") {
        try {
          this.postLoginAction();
        } catch (e) {
          common_vendor.index.__f__("error", "at store/user.js:140", "[userStore] Error executing postLoginAction:", e);
        }
        this.clearPostLoginAction();
      } else if (this.postLoginAction && typeof this.postLoginAction.handlerName === "string") {
        this.clearPostLoginAction();
      }
    },
    // 退出登录
    logout() {
      this.userInfo = {};
      this.isLogin = false;
      this.token = "";
      this.tokenExpired = 0;
      this.lastTokenVerifyTime = 0;
      this.quickAuthCache = {
        isValid: false,
        timestamp: 0,
        ttl: 30 * 1e3
      };
      this.isTokenVerifying = false;
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("uni_id_token");
      common_vendor.index.removeStorageSync("uni_id_token_expired");
      common_vendor.index.removeStorageSync("uid");
      common_vendor.index.removeStorageSync("wishList");
      common_vendor.index.removeStorageSync("wishLastSyncTime");
      common_vendor.index.removeStorageSync("groups");
      common_vendor.index.removeStorageSync("groupsLastSyncTime");
      common_vendor.index.removeStorageSync("messages");
      common_vendor.index.removeStorageSync("messagesLastSyncTime");
      try {
        const { useWishStore } = require("./wish.js");
        const { useGroupStore } = require("./group.js");
        const wishStore = useWishStore();
        const groupStore = useGroupStore();
        if (wishStore) {
          wishStore.clearLocalData();
        }
        if (groupStore && typeof groupStore.clearLocalData === "function") {
          groupStore.clearLocalData();
        }
      } catch (error) {
        common_vendor.index.__f__("warn", "at store/user.js:200", "[userStore] Error clearing other stores:", error);
      }
      try {
        utils_syncManager.syncManager.destroy();
      } catch (syncError) {
        common_vendor.index.__f__("warn", "at store/user.js:208", "[userStore] Error destroying sync manager:", syncError);
      }
    },
    // 🚀 智能验证方法：本地快速检查
    quickLocalAuthCheck() {
      let now;
      try {
        if (!this.quickAuthCache) {
          this.quickAuthCache = {
            isValid: false,
            timestamp: 0,
            ttl: 30 * 1e3
          };
        }
        now = Date.now();
        if (this.quickAuthCache.isValid && now - this.quickAuthCache.timestamp < this.quickAuthCache.ttl) {
          return { needsVerification: false, isAuthenticated: true };
        }
        if (!this._performLoginCheck()) {
          return { needsVerification: false, isAuthenticated: false };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/user.js:237", "[userStore] Error in quickLocalAuthCheck initial checks:", error);
        return { needsVerification: false, isAuthenticated: false };
      }
      try {
        const tokenParts = this.token.split(".");
        if (tokenParts.length === 3) {
          const base64Str = tokenParts[1];
          const paddedBase64 = base64Str + "===".slice((base64Str.length + 3) % 4);
          let decodedStr = "";
          if (typeof atob !== "undefined") {
            decodedStr = atob(paddedBase64);
          } else {
            try {
              if (common_vendor.index.base64ToArrayBuffer) {
                const buffer = common_vendor.index.base64ToArrayBuffer(paddedBase64);
                decodedStr = String.fromCharCode.apply(null, new Uint8Array(buffer));
              } else {
              }
            } catch (decodeError) {
              common_vendor.index.__f__("warn", "at store/user.js:265", "[userStore] Base64 decode failed, skipping local token check:", decodeError);
            }
          }
          if (decodedStr) {
            const payload = JSON.parse(decodedStr);
            if (payload.exp && payload.exp * 1e3 < Date.now()) {
              return { needsVerification: false, isAuthenticated: false };
            }
          }
        }
      } catch (e) {
        common_vendor.index.__f__("warn", "at store/user.js:277", "[userStore] Error parsing token locally:", e);
      }
      try {
        if (!now) {
          now = Date.now();
        }
        const needsServerVerification = now - this.lastTokenVerifyTime > this.tokenVerifyInterval;
        if (!needsServerVerification) {
          this.quickAuthCache = { isValid: true, timestamp: now, ttl: 30 * 1e3 };
          return { needsVerification: false, isAuthenticated: true };
        }
        return { needsVerification: true, isAuthenticated: true };
      } catch (error) {
        common_vendor.index.__f__("error", "at store/user.js:294", "[userStore] Error in quickLocalAuthCheck final checks:", error);
        return { needsVerification: false, isAuthenticated: false };
      }
    },
    // 🚀 智能验证方法：按安全级别分层验证
    async smartAuthCheck(securityLevel = "basic") {
      this.appLastActiveTime = Date.now();
      const quickCheck = this.quickLocalAuthCheck();
      if (!quickCheck.needsVerification) {
        return quickCheck.isAuthenticated;
      }
      if (this.isTokenVerifying) {
        let retries = 0;
        while (this.isTokenVerifying && retries < 20) {
          await new Promise((resolve) => setTimeout(resolve, 100));
          retries++;
        }
        const recheckResult = this.quickLocalAuthCheck();
        return !recheckResult.needsVerification && recheckResult.isAuthenticated;
      }
      switch (securityLevel) {
        case "low":
          if (Date.now() - this.lastTokenVerifyTime < this.tokenVerifyInterval * 3) {
            this.quickAuthCache = { isValid: true, timestamp: Date.now(), ttl: 30 * 1e3 };
            return true;
          }
          break;
      }
      return await this.performServerTokenVerification();
    },
    // 🚀 执行服务端token验证
    async performServerTokenVerification() {
      this.isTokenVerifying = true;
      try {
        const localToken = this.token;
        if (!localToken) {
          this.logout();
          return false;
        }
        const uniIdCo2 = getUniIdCo();
        if (!uniIdCo2) {
          common_vendor.index.__f__("error", "at store/user.js:360", "[userStore] uni-id-co not available for token check.");
          this.logout();
          return false;
        }
        const res = await uniIdCo2.getAccountInfo();
        if (res.errCode === 0) {
          this.lastTokenVerifyTime = Date.now();
          this.quickAuthCache = {
            isValid: true,
            timestamp: Date.now(),
            ttl: 30 * 1e3
          };
          common_vendor.index.__f__("log", "at store/user.js:375", "[userStore] Server token verification successful");
          return true;
        } else if (res.errCode === "uni-id-token-expired" || res.errCode === "uni-id-check-token-failed") {
          common_vendor.index.__f__("warn", "at store/user.js:378", "[userStore] Token expired or invalid on server:", res.errMsg);
          this.logout();
          return false;
        } else {
          common_vendor.index.__f__("error", "at store/user.js:382", "[userStore] Server token verification error:", res.errMsg);
          this.logout();
          return false;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/user.js:387", "[userStore] Exception during server token verification:", error);
        this.logout();
        return false;
      } finally {
        this.isTokenVerifying = false;
      }
    },
    // 🚀 优化后的检查登录并重定向方法
    async checkLoginAndRedirect(securityLevel = "basic") {
      const isAuthenticated = await this.smartAuthCheck(securityLevel);
      if (!isAuthenticated && !this.skipLoginCheck) {
        this.safeNavigateToLogin();
        return false;
      }
      return isAuthenticated;
    },
    // 🔧 检测是否在开发者工具环境
    isDevTool() {
      return true;
    },
    // 🔒 安全导航方法：防止重复导航（针对开发者工具优化）
    safeNavigateToLogin() {
      const now = Date.now();
      const cooldown = this.isDevTool() ? this.navigationCooldown * 2 : this.navigationCooldown;
      if (now - this.lastNavigationTime < cooldown) {
        return false;
      }
      this.lastNavigationTime = now;
      const performNavigation = () => {
        common_vendor.index.navigateTo({
          url: "/pages/login/login",
          fail: (error) => {
            common_vendor.index.__f__("error", "at store/user.js:431", "[userStore] Navigation to login failed:", error);
            if (this.isDevTool() && error.errMsg && error.errMsg.includes("timeout")) {
              setTimeout(() => {
                common_vendor.index.navigateTo({
                  url: "/pages/login/login",
                  fail: (retryError) => {
                    common_vendor.index.__f__("error", "at store/user.js:439", "[userStore] Retry login navigation also failed:", retryError);
                    this.lastNavigationTime = 0;
                    common_vendor.index.showToast({
                      title: "跳转登录页失败，请重试",
                      icon: "none",
                      duration: 2e3
                    });
                  }
                });
              }, 1e3);
            } else {
              this.lastNavigationTime = 0;
            }
          }
        });
      };
      if (this.isDevTool()) {
        setTimeout(performNavigation, 200);
      } else {
        performNavigation();
      }
      return true;
    },
    // 🚀 按需验证：根据操作类型选择验证级别
    async ensureAuthenticated(options = {}) {
      const {
        operation = "read",
        // 'read', 'write', 'sensitive'
        showToast = true,
        redirectOnFail = true
      } = options;
      let securityLevel = "basic";
      switch (operation) {
        case "read":
          securityLevel = "low";
          break;
        case "write":
          securityLevel = "basic";
          break;
        case "sensitive":
          securityLevel = "high";
          break;
      }
      const isAuthenticated = await this.smartAuthCheck(securityLevel);
      if (!isAuthenticated) {
        if (showToast) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
        }
        if (redirectOnFail) {
          this.safeNavigateToLogin();
        }
      }
      return isAuthenticated;
    },
    // 用于 uni.getUserProfile 获取信息后更新 store 和（可选）后端
    async updateProfileDetails(profileFromWx) {
      if (!this.userInfo || !this.userInfo.uid) {
        common_vendor.index.__f__("error", "at store/user.js:510", "Cannot update profile, user or UID not found in store.");
        return;
      }
      const newProfileData = {
        nickname: profileFromWx.nickName,
        avatarUrl: profileFromWx.avatarUrl,
        gender: profileFromWx.gender,
        // 0: 未知, 1: 男性, 2: 女性
        country: profileFromWx.country,
        province: profileFromWx.province,
        city: profileFromWx.city
      };
      this.userInfo = {
        ...this.userInfo,
        ...newProfileData
      };
      common_vendor.index.setStorageSync("userInfo", JSON.stringify(this.userInfo));
      try {
        const uniIdCo2 = getUniIdCo();
        if (!uniIdCo2) {
          common_vendor.index.__f__("error", "at store/user.js:533", "Cannot update profile: uni-id-co not available");
          return;
        }
        const updateResult = await uniIdCo2.updateUser({
          uid: this.userInfo.uid,
          // 确保传递了uid
          nickname: newProfileData.nickname,
          avatar: newProfileData.avatarUrl
          // uni-id users表通常用 avatar 字段存头像URL
          // 根据需要可以传递 gender 等其他信息
        });
        if (updateResult.errCode !== 0) {
          common_vendor.index.__f__("error", "at store/user.js:546", "Failed to update profile on uni-id server:", updateResult.errMsg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/user.js:549", "Error calling uni-id to update user profile:", error);
      }
    },
    // 从本地存储加载并验证用户状态
    // 🚀 优化后的从本地存储加载用户数据（仅在应用启动时使用）
    async loadUserFromStorage() {
      const localToken = common_vendor.index.getStorageSync("uni_id_token") || common_vendor.index.getStorageSync("token");
      if (!localToken) {
        this.logout();
        return Promise.resolve(false);
      }
      this.token = localToken;
      let storedUserInfo = {};
      try {
        const stored = common_vendor.index.getStorageSync("userInfo");
        storedUserInfo = stored ? typeof stored === "string" ? JSON.parse(stored) : stored : {};
      } catch (e) {
        common_vendor.index.__f__("error", "at store/user.js:572", "[userStore] Error parsing stored userInfo:", e);
        storedUserInfo = {};
      }
      if (storedUserInfo && Object.keys(storedUserInfo).length > 0) {
        this.isLogin = true;
        this.userInfo = storedUserInfo;
        setTimeout(() => {
          this.smartAuthCheck("basic").catch((error) => {
            common_vendor.index.__f__("error", "at store/user.js:584", "[userStore] Background token verification failed:", error);
          });
        }, 1e3);
        return Promise.resolve(true);
      } else {
        return await this.performServerTokenVerification();
      }
    },
    // 通用登录状态检查方法（避免重复逻辑）
    _performLoginCheck() {
      if (!this.token || !this.isLogin) {
        return false;
      }
      if (!this.userInfo || Object.keys(this.userInfo).length === 0) {
        return false;
      }
      return true;
    },
    // 检查登录状态 (可被 loadUserFromStorage 替代或基于其结果)
    checkLoginStatus() {
      this.loadUserFromStorage();
      return this._performLoginCheck();
    },
    setSkipLoginCheck(skip) {
      this.skipLoginCheck = skip;
    },
    updateUserInfo(updatedInfo) {
      this.userInfo = { ...this.userInfo, ...updatedInfo };
      common_vendor.index.setStorageSync("userInfo", JSON.stringify(this.userInfo));
      return true;
    },
    clearStorage() {
      try {
        common_vendor.index.removeStorageSync("userInfo");
        common_vendor.index.removeStorageSync("token");
        common_vendor.index.removeStorageSync("uni_id_token");
        common_vendor.index.removeStorageSync("uni_id_token_expired");
        common_vendor.index.removeStorageSync("uid");
        this.userInfo = null;
        this.isLogin = false;
        this.token = "";
        this.tokenExpired = 0;
        return true;
      } catch (e) {
        common_vendor.index.__f__("error", "at store/user.js:648", "清除存储数据失败:", e);
        return false;
      }
    },
    // 设置登录后要执行的动作
    setPostLoginAction(action) {
      this.postLoginAction = action;
    },
    // 清除登录后动作
    clearPostLoginAction() {
      this.postLoginAction = null;
    },
    // 检查用户是否有特定角色 (如果未来引入RBAC)
    hasRole(role) {
      if (!this.isLogin || !this.userInfo || !this.userInfo.roles) {
        return false;
      }
      return this.userInfo.roles.includes(role);
    },
    // 强制清除所有认证数据的方法
    forceLogout() {
      this.logout();
      common_vendor.index.removeStorageSync("uni_id_token_expired");
      common_vendor.index.removeStorageSync("uni_id_user");
      common_vendor.index.removeStorageSync("uni_id_uid");
    },
    // 修复损坏的userInfo数据
    fixUserInfoData() {
      try {
        const stored = common_vendor.index.getStorageSync("userInfo");
        if (stored && typeof stored === "object" && !Array.isArray(stored) && stored.hasOwnProperty("0")) {
          common_vendor.index.removeStorageSync("userInfo");
          this.userInfo = {};
          this.logout();
          return true;
        }
        return false;
      } catch (e) {
        common_vendor.index.__f__("error", "at store/user.js:696", "[userStore] Error during userInfo fix:", e);
        common_vendor.index.removeStorageSync("userInfo");
        this.userInfo = {};
        this.logout();
        return true;
      }
    },
    // 登录成功后，重新初始化需要认证的stores
    async initDependentStores(showSuccessToast = false) {
      try {
        const { useGroupStore } = require("./group.js");
        const { useWishStore } = require("./wish.js");
        const groupStore = useGroupStore();
        const wishStore = useWishStore();
        await groupStore.initGroups();
        await wishStore.initWishList();
        try {
          await utils_syncManager.syncManager.init();
        } catch (syncError) {
          common_vendor.index.__f__("error", "at store/user.js:726", "[userStore] ❌ Sync manager initialization failed:", syncError);
        }
        if (showSuccessToast) {
          common_vendor.index.showToast({
            title: "登录成功！",
            icon: "success",
            duration: 1500
          });
        }
        setTimeout(() => {
          common_vendor.index.$emit("user-login-success", {
            userId: this.userId,
            timestamp: Date.now()
          });
        }, 500);
      } catch (error) {
        common_vendor.index.__f__("error", "at store/user.js:748", "[userStore] Error reinitializing dependent stores:", error);
        common_vendor.index.showToast({
          title: "登录成功",
          icon: "success",
          duration: 1500
        });
      }
    },
    // 手动同步所有数据（用于下拉刷新）- 智能增量同步
    async manualSyncAllData(silent = false) {
      if (!this.isLogin) {
        if (!silent) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
        }
        return false;
      }
      try {
        if (silent) {
          const syncResult = await this.performIntelligentSync(silent);
          return syncResult;
        } else {
          const syncResult = await utils_loadingManager.loadingManager.wrap(
            () => this.performIntelligentSync(silent),
            {
              title: "正在检查数据更新...",
              id: "user_intelligent_sync",
              timeout: 15e3,
              silent: false,
              showSuccess: false,
              // 智能同步方法内部处理成功提示
              showError: false,
              // 智能同步方法内部处理错误
              errorTitle: "同步失败，请重试"
            }
          );
          return syncResult;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/user.js:795", "[userStore] Manual sync failed:", error);
        return false;
      }
    },
    // 执行智能增量同步 - 🚀 增强防重复机制
    async performIntelligentSync(silent = false) {
      if (this._isSyncInProgress) {
        common_vendor.index.__f__("log", "at store/user.js:805", "[userStore] 智能同步已在进行中，跳过重复请求");
        return false;
      }
      try {
        this._isSyncInProgress = true;
        const { useGroupStore } = require("./group.js");
        const { useWishStore } = require("./wish.js");
        const groupStore = useGroupStore();
        const wishStore = useWishStore();
        let hasUpdates = false;
        const syncResults = {
          groups: { checked: false, updated: false, count: 0 },
          wishes: { checked: false, updated: false, count: 0 }
        };
        try {
          const groupSyncResult = await groupStore.smartSync();
          syncResults.groups.checked = true;
          syncResults.groups.updated = groupSyncResult.hasUpdates;
          syncResults.groups.count = groupSyncResult.updatedCount || 0;
          if (groupSyncResult.hasUpdates) {
            hasUpdates = true;
          }
        } catch (groupError) {
          if (this._isMultiDeviceConflictError(groupError)) {
          } else {
            common_vendor.index.__f__("error", "at store/user.js:841", "[userStore] Group sync failed:", groupError);
            throw groupError;
          }
        }
        try {
          const wishSyncResult = await wishStore.smartSync();
          syncResults.wishes.checked = true;
          syncResults.wishes.updated = wishSyncResult.hasUpdates;
          syncResults.wishes.count = wishSyncResult.updatedCount || 0;
          if (wishSyncResult.hasUpdates) {
            hasUpdates = true;
          }
        } catch (wishError) {
          if (this._isMultiDeviceConflictError(wishError)) {
          } else {
            common_vendor.index.__f__("error", "at store/user.js:862", "[userStore] Wish sync failed:", wishError);
            throw wishError;
          }
        }
        if (!silent) {
          if (hasUpdates) {
            let message = "数据已更新";
            const updates = [];
            if (syncResults.groups.updated)
              updates.push(`分组${syncResults.groups.count}个`);
            if (syncResults.wishes.updated)
              updates.push(`心愿${syncResults.wishes.count}个`);
            if (updates.length > 0) {
              message = `更新了${updates.join("、")}`;
            }
            common_vendor.index.__f__("log", "at store/user.js:881", "[userStore] 同步完成:", message);
          } else {
            common_vendor.index.__f__("log", "at store/user.js:884", "[userStore] 数据已是最新");
          }
        }
        return hasUpdates;
      } catch (error) {
        if (this._isMultiDeviceConflictError(error)) {
          common_vendor.index.__f__("log", "at store/user.js:894", "[userStore] 多设备冲突已静默处理");
          return false;
        }
        if (error.message && (error.message.includes("getSyncSummary") || error.message.includes("获取云端") || error.message.includes("摘要失败"))) {
          common_vendor.index.__f__("error", "at store/user.js:904", "[userStore] Sync summary API error:", error);
          return false;
        }
        common_vendor.index.__f__("error", "at store/user.js:909", "[userStore] Intelligent sync failed:", error);
        return false;
      } finally {
        this._isSyncInProgress = false;
      }
    },
    // 检查是否是多设备冲突错误
    _isMultiDeviceConflictError(error) {
      if (!error || !error.message)
        return false;
      const conflictMessages = [
        "心愿不存在或无权限",
        "分组不存在或无权限",
        "not found",
        "permission denied",
        "无权限操作"
      ];
      return conflictMessages.some(
        (msg) => error.message.toLowerCase().includes(msg.toLowerCase())
      );
    }
  }
});
exports.useUserStore = useUserStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/user.js.map
