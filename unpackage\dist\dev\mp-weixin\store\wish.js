"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const utils_loadingManager = require("../utils/loadingManager.js");
const store_user = require("./user.js");
const ERROR_CODES = {
  SUCCESS: 0,
  NETWORK_ERROR: 1001,
  AUTH_ERROR: 1002,
  VALIDATION_ERROR: 1003,
  PERMISSION_ERROR: 1004,
  NOT_FOUND_ERROR: 1005,
  SERVER_ERROR: 1006,
  UNKNOWN_ERROR: 9999
};
const ConflictManager = {
  // 静默处理版本冲突，不显示任何弹窗
  handleVersionConflict(error, context = "unknown") {
    common_vendor.index.__f__("log", "at store/wish.js:41", `[ConflictManager] 静默处理版本冲突: ${context}`, error.message);
    return true;
  }
};
const ErrorHandler = {
  // 获取用户友好的错误信息
  getUserFriendlyMessage(error) {
    if (typeof error === "string")
      return error;
    if (error.errCode) {
      switch (error.errCode) {
        case ERROR_CODES.NETWORK_ERROR:
          return "网络连接失败，请检查网络设置";
        case ERROR_CODES.AUTH_ERROR:
          return "登录已过期，请重新登录";
        case ERROR_CODES.VALIDATION_ERROR:
          return error.errMsg || "数据验证失败";
        case ERROR_CODES.PERMISSION_ERROR:
          return "没有权限执行此操作";
        case ERROR_CODES.NOT_FOUND_ERROR:
          return "数据不存在或已被删除";
        case ERROR_CODES.SERVER_ERROR:
          return "服务器错误，请稍后重试";
        default:
          return error.errMsg || "操作失败";
      }
    }
    if (error.message && (error.message.includes("网络") || error.message.includes("network") || error.message.includes("timeout") || error.code === "NETWORK_ERROR")) {
      return "网络连接失败，数据已保存到本地";
    }
    return error.message || error.errMsg || "未知错误";
  },
  // 判断是否为网络错误
  isNetworkError(error) {
    if (error.errCode === ERROR_CODES.NETWORK_ERROR)
      return true;
    if (error.code === "NETWORK_ERROR")
      return true;
    if (error.message && (error.message.includes("网络") || error.message.includes("network") || error.message.includes("timeout")))
      return true;
    return false;
  },
  // 🚀 检查是否是版本冲突错误
  isVersionConflictError(error) {
    return error && error.message && (error.message.includes("数据版本冲突") || error.message.includes("逻辑时钟冲突") || error.message.includes("version conflict") || error.message.includes("conflict"));
  },
  // 显示错误提示 - 🔧 版本冲突完全静默处理
  showError(error, title = "操作失败", context = "unknown") {
    if (this.isVersionConflictError(error)) {
      ConflictManager.handleVersionConflict(error, context);
      return;
    }
    const message = this.getUserFriendlyMessage(error);
    common_vendor.index.showToast({
      title: message,
      icon: "none",
      duration: 3e3
    });
    common_vendor.index.__f__("error", "at store/wish.js:124", `${title}:`, error);
  }
};
const useWishStore = common_vendor.defineStore("wish", {
  state: () => ({
    wishList: [],
    currentGroupId: "all",
    // 默认显示全部分组
    isLoading: false,
    lastSyncTime: null,
    isOnline: true,
    // 网络状态
    // 添加一个用于强制触发响应式更新的标记
    listUpdateCounter: 0,
    // 新增同步状态管理
    syncStatus: {
      issyncing: false,
      lastSyncResult: null,
      pendingCount: 0,
      errorCount: 0,
      lastError: null
    },
    // 🚀 增强同步状态管理
    _syncOperations: /* @__PURE__ */ new Set(),
    // 正在进行的同步操作
    _lastSyncTime: 0,
    // 上次同步时间
    _recentlyDeleted: /* @__PURE__ */ new Map()
    // 最近删除的心愿ID记录
  }),
  getters: {
    // 获取当前分组的心愿列表
    currentGroupWishes: (state) => {
      state.listUpdateCounter;
      const activeWishes = state.wishList.filter((wish) => !wish.isCompleted && !wish._deleted);
      if (state.currentGroupId === "all") {
        return activeWishes;
      } else if (state.currentGroupId === "friend-visible") {
        return activeWishes.filter((wish) => wish.permission === "friends");
      } else if (state.currentGroupId === "private") {
        return activeWishes.filter((wish) => wish.permission === "private");
      } else if (state.currentGroupId === "public") {
        return activeWishes.filter((wish) => wish.permission === "public");
      } else if (state.currentGroupId === "gift") {
        return activeWishes.filter(
          (wish) => wish.groupIds && wish.groupIds.includes("gift")
        );
      } else {
        return activeWishes.filter(
          (wish) => wish.groupIds && wish.groupIds.includes(state.currentGroupId)
        );
      }
    },
    // 获取单个心愿
    getWishById: (state) => (id) => {
      return state.wishList.find(
        (wish) => (wish._id === id || wish.id === id) && !wish._deleted
      ) || null;
    },
    // 新增同步状态相关getters
    // 获取待同步的心愿数量
    pendingSyncCount: (state) => {
      return state.wishList.filter((wish) => wish._needSync).length;
    },
    // 检查是否有同步错误
    hasSyncErrors: (state) => {
      return state.syncStatus.errorCount > 0 || state.syncStatus.lastError !== null;
    },
    // 获取同步状态描述
    syncStatusText: (state) => {
      if (state.syncStatus.issyncing) {
        return "正在同步...";
      }
      if (state.syncStatus.errorCount > 0) {
        return `同步失败 (${state.syncStatus.errorCount}个错误)`;
      }
      if (state.syncStatus.pendingCount > 0) {
        return `待同步 (${state.syncStatus.pendingCount}项)`;
      }
      if (state.syncStatus.lastSyncResult === "success") {
        return "同步完成";
      }
      return "未同步";
    },
    // 获取详细同步信息
    syncInfo: (state) => {
      return {
        ...state.syncStatus,
        pendingCount: state.wishList.filter((wish) => wish._needSync).length,
        lastSyncTime: state.lastSyncTime,
        isOnline: state.isOnline
      };
    }
  },
  actions: {
    // 初始化心愿数据
    async initWishList() {
      common_vendor.index.__f__("log", "at store/wish.js:237", "[wishStore] Initializing wish list...");
      const storedWishes = common_vendor.index.getStorageSync("wishList");
      if (storedWishes) {
        const parsed = JSON.parse(storedWishes);
        this.wishList = parsed.map((wish) => ({
          ...wish,
          id: wish.id || wish._id
          // 如果没有 id 字段，使用 _id
        }));
        common_vendor.index.__f__("log", "at store/wish.js:248", "[wishStore] Loaded wishes from storage:", this.wishList.length);
      }
      this.initNetworkMonitor();
      await this.syncFromCloud();
      common_vendor.index.__f__("log", "at store/wish.js:257", "[wishStore] Wish list initialization completed");
    },
    // 初始化网络监听
    initNetworkMonitor() {
      this.checkNetworkStatus();
      common_vendor.index.onNetworkStatusChange((res) => {
        const wasOnline = this.isOnline;
        this.isOnline = res.isConnected;
        common_vendor.index.__f__("log", "at store/wish.js:270", `网络状态变化: ${wasOnline ? "在线" : "离线"} -> ${this.isOnline ? "在线" : "离线"}`);
        if (!wasOnline && this.isOnline) {
          common_vendor.index.__f__("log", "at store/wish.js:275", "网络恢复，但不启用自动同步");
        }
        if (wasOnline && !this.isOnline) {
          common_vendor.index.showToast({
            title: "网络连接已断开，将保存到本地",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    // 检查网络状态
    async checkNetworkStatus() {
      try {
        const networkType = await common_vendor.index.getNetworkType();
        this.isOnline = networkType.networkType !== "none";
        common_vendor.index.__f__("log", "at store/wish.js:294", "当前网络状态:", this.isOnline ? "在线" : "离线");
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:296", "检查网络状态失败:", error);
        this.isOnline = false;
      }
    },
    // 手动同步逻辑（原autoSync方法重命名）
    async _performSync() {
      if (this.syncStatus.issyncing) {
        common_vendor.index.__f__("log", "at store/wish.js:305", "[wishStore] Sync already in progress, skipping...");
        return;
      }
      try {
        this._updateSyncStatus({ issyncing: true });
        const pendingWishes = this.wishList.filter((wish) => wish._needSync);
        this._updateSyncStatus({ pendingCount: pendingWishes.length });
        if (pendingWishes.length > 0) {
          common_vendor.index.__f__("log", "at store/wish.js:318", `[wishStore] 发现${pendingWishes.length}个待同步心愿，开始上传同步...`);
          const result = await this.syncPendingData();
          if (result.syncedCount > 0) {
            await this.performIncrementalSync();
          }
        } else {
          common_vendor.index.__f__("log", "at store/wish.js:327", "[wishStore] 执行增量同步检查云端更新...");
          await this.performIncrementalSync();
        }
        this._updateSyncStatus({
          lastSyncResult: "success",
          errorCount: 0,
          lastError: null
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:339", "[wishStore] 手动同步失败:", error);
        this._updateSyncStatus({
          lastSyncResult: "failed",
          errorCount: this.syncStatus.errorCount + 1,
          lastError: error.message || "同步失败"
        });
        if (!ErrorHandler.isNetworkError(error)) {
          common_vendor.index.__f__("warn", "at store/wish.js:350", "[wishStore] 手动同步遇到错误");
        }
      } finally {
        this._updateSyncStatus({ issyncing: false });
      }
    },
    // 更新同步状态
    _updateSyncStatus(updates) {
      this.syncStatus = {
        ...this.syncStatus,
        ...updates
      };
      common_vendor.index.__f__("log", "at store/wish.js:363", "[wishStore] Sync status updated:", this.syncStatus);
    },
    // 手动触发同步（供用户主动调用）
    async manualSync(silent = false) {
      this._updateSyncStatus({
        errorCount: 0,
        lastError: null
      });
      try {
        await utils_loadingManager.loadingManager.wrap(
          () => this._performSync(),
          {
            title: "正在同步数据...",
            id: "wish_manual_sync",
            timeout: 15e3,
            silent,
            showSuccess: !silent && this.syncStatus.lastSyncResult === "success",
            successTitle: "同步完成",
            showError: !silent,
            errorTitle: "同步失败"
          }
        );
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:390", "[wishStore] Manual sync failed:", error);
        if (!silent) {
          throw error;
        }
      }
    },
    // 重置同步状态
    resetSyncStatus() {
      this._updateSyncStatus({
        issyncing: false,
        lastSyncResult: null,
        pendingCount: 0,
        errorCount: 0,
        lastError: null
      });
    },
    // 🚀 新增：检查同步操作是否可以执行
    _canStartSync(operation) {
      const now = Date.now();
      if (this._syncOperations.has(operation)) {
        common_vendor.index.__f__("log", "at store/wish.js:414", `[wishStore] 同步操作 ${operation} 已在进行中`);
        return false;
      }
      if (now - this._lastSyncTime < 1e3) {
        common_vendor.index.__f__("log", "at store/wish.js:420", `[wishStore] 同步操作过于频繁，跳过`);
        return false;
      }
      return true;
    },
    // 🚀 新增：开始同步操作
    _startSyncOperation(operation) {
      this._syncOperations.add(operation);
      this._lastSyncTime = Date.now();
      common_vendor.index.__f__("log", "at store/wish.js:431", `[wishStore] 开始同步操作: ${operation}`);
    },
    // 🚀 新增：结束同步操作
    _endSyncOperation(operation) {
      this._syncOperations.delete(operation);
      common_vendor.index.__f__("log", "at store/wish.js:437", `[wishStore] 结束同步操作: ${operation}`);
    },
    // 🔧 新增：记录最近删除的心愿ID
    _recordDeletedWish(wishId) {
      if (!this._recentlyDeleted) {
        this._recentlyDeleted = /* @__PURE__ */ new Map();
      }
      this._recentlyDeleted.set(wishId, Date.now());
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1e3;
      for (const [id, timestamp] of this._recentlyDeleted.entries()) {
        if (timestamp < fiveMinutesAgo) {
          this._recentlyDeleted.delete(id);
        }
      }
    },
    // 🔧 新增：获取最近删除的心愿ID列表
    _getRecentlyDeletedWishIds() {
      if (!this._recentlyDeleted) {
        return [];
      }
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1e3;
      const recentIds = [];
      for (const [id, timestamp] of this._recentlyDeleted.entries()) {
        if (timestamp >= fiveMinutesAgo) {
          recentIds.push(id);
        }
      }
      return recentIds;
    },
    // 从云端同步数据 - 🚀 增强防重复机制
    async syncFromCloud() {
      const operation = "syncFromCloud";
      if (!this._canStartSync(operation)) {
        return;
      }
      const userStore = store_user.useUserStore();
      if (!userStore.isLogin) {
        common_vendor.index.__f__("log", "at store/wish.js:485", "[store/wish.js syncFromCloud] User not logged in, skipping cloud sync.");
        if (this.wishList.length === 0)
          ;
        return;
      }
      try {
        this._startSyncOperation(operation);
        this.isLoading = true;
        common_vendor.index.__f__("log", "at store/wish.js:496", "[wishStore] Starting intelligent sync...");
        const syncNeded = await this._checkSyncNeeded();
        if (!syncNeded) {
          common_vendor.index.__f__("log", "at store/wish.js:502", "[wishStore] Local data is up to date, no sync needed");
          return;
        }
        await this._performIntelligentSync();
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:510", "[wishStore] 智能同步失败:", error);
        if (this.wishList.length === 0) {
          common_vendor.index.__f__("log", "at store/wish.js:513", "[wishStore] Initializing example data due to sync failure...");
          this._initExampleData();
        }
      } finally {
        this.isLoading = false;
        this._endSyncOperation(operation);
      }
    },
    // 检查是否需要同步（轻量级调用）
    async _checkSyncNeeded() {
      {
        common_vendor.index.__f__("log", "at store/wish.js:528", "[wishStore] Lightweight sync check disabled, performing full sync");
        return true;
      }
    },
    // 获取本地数据的最后修改时间
    _getLocalLastModified() {
      if (this.wishList.length === 0)
        return null;
      const latestWish = this.wishList.reduce((latest, current) => {
        const currentTime = new Date(current.updateDate || current.createDate);
        const latestTime = new Date(latest.updateDate || latest.createDate);
        return currentTime > latestTime ? current : latest;
      });
      return latestWish.updateDate || latestWish.createDate;
    },
    // 执行智能同步（合并数据而不是覆盖）
    async _performIntelligentSync() {
      common_vendor.index.__f__("log", "at store/wish.js:593", "[wishStore] Performing intelligent data merge...");
      const wishCenter = common_vendor.nr.importObject("wish-center");
      const result = await wishCenter.getWishList({
        page: 1,
        pageSize: 1e3,
        includeCompleted: true,
        includeDeleted: true
        // 包含已删除的数据用于冲突解决
      });
      if (result.errCode === 0) {
        const cloudWishes = result.data || [];
        common_vendor.index.__f__("log", "at store/wish.js:605", "[wishStore] Cloud data received:", cloudWishes.length, "items");
        const mergedData = this._mergeWishData(this.wishList, cloudWishes);
        this.wishList = mergedData.map((wish) => ({
          ...wish,
          id: wish._id
          // 确保有 id 字段供前端组件使用
        }));
        this._clearWishesCache();
        this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
        this._saveToStorage();
        common_vendor.index.__f__("log", "at store/wish.js:620", "[wishStore] Intelligent sync completed, merged data:", this.wishList.length, "items");
      } else {
        throw new Error(result.errMsg || "获取云端数据失败");
      }
    },
    // 智能合并本地和云端数据
    _mergeWishData(localWishes, cloudWishes) {
      common_vendor.index.__f__("log", "at store/wish.js:628", "[wishStore] Merging local and cloud data...");
      const mergedMap = /* @__PURE__ */ new Map();
      const recentlyDeletedIds = this._getRecentlyDeletedWishIds();
      cloudWishes.forEach((cloudWish) => {
        if (recentlyDeletedIds.includes(cloudWish._id)) {
          common_vendor.index.__f__("log", "at store/wish.js:639", `[wishStore] 跳过最近删除的心愿: ${cloudWish.title}`);
          return;
        }
        mergedMap.set(cloudWish._id, {
          ...cloudWish,
          _source: "cloud"
        });
      });
      localWishes.forEach((localWish) => {
        const id = localWish._id || localWish.id;
        const existingWish = mergedMap.get(id);
        if (!existingWish) {
          mergedMap.set(id, {
            ...localWish,
            _source: "local",
            _needSync: true
            // 标记需要同步到云端
          });
        } else {
          const localTimeStr = localWish.updateDate || localWish.createDate;
          const cloudTimeStr = existingWish.updateDate || existingWish.createDate;
          const localTime = localTimeStr ? new Date(localTimeStr) : /* @__PURE__ */ new Date(0);
          const cloudTime = cloudTimeStr ? new Date(cloudTimeStr) : /* @__PURE__ */ new Date(0);
          const localTimeValid = !isNaN(localTime.getTime());
          const cloudTimeValid = !isNaN(cloudTime.getTime());
          if (!localTimeValid && !cloudTimeValid) {
            common_vendor.index.__f__("log", "at store/wish.js:676", "[wishStore] 两个时间戳都无效，使用云端数据:", id);
          } else if (!localTimeValid) {
            common_vendor.index.__f__("log", "at store/wish.js:680", "[wishStore] 本地时间戳无效，使用云端数据:", id);
          } else if (!cloudTimeValid) {
            common_vendor.index.__f__("log", "at store/wish.js:684", "[wishStore] 云端时间戳无效，使用本地数据:", id);
            mergedMap.set(id, {
              ...localWish,
              _source: "local-newer",
              _needSync: true
            });
          } else if (localTime > cloudTime) {
            mergedMap.set(id, {
              ...localWish,
              _source: "local-newer",
              _needSync: true
              // 需要同步到云端
            });
          } else if (localTime < cloudTime)
            ;
          else {
            if (this._hasContentDifference(localWish, existingWish)) {
              common_vendor.index.__f__("log", "at store/wish.js:704", "[wishStore] 时间戳相同但内容不同，使用云端数据:", id);
            }
          }
        }
      });
      const mergedArray = Array.from(mergedMap.values()).filter((wish) => !wish._deleted).sort((a, b) => (a.order || 0) - (b.order || 0));
      common_vendor.index.__f__("log", "at store/wish.js:715", "[wishStore] Data merge completed:", {
        localCount: localWishes.length,
        cloudCount: cloudWishes.length,
        mergedCount: mergedArray.length
      });
      return mergedArray;
    },
    // 检查两个心愿对象是否有内容差异
    _hasContentDifference(wish1, wish2) {
      const keys = ["title", "description", "image", "video", "audio", "groupIds", "permission"];
      return keys.some((key) => {
        const val1 = wish1[key];
        const val2 = wish2[key];
        if (Array.isArray(val1) && Array.isArray(val2)) {
          return JSON.stringify(val1.sort()) !== JSON.stringify(val2.sort());
        }
        return val1 !== val2;
      });
    },
    // 初始化示例数据
    _initExampleData() {
      this.wishList = [
        {
          _id: "1",
          id: "1",
          // 添加 id 字段以保持兼容性
          title: "买一台新笔记本电脑",
          description: "想要一台性能好的笔记本，用于工作和娱乐",
          image: [],
          video: [],
          audio: [],
          createDate: (/* @__PURE__ */ new Date()).toISOString(),
          updateDate: (/* @__PURE__ */ new Date()).toISOString(),
          startDate: null,
          completeDate: null,
          isCompleted: false,
          permission: "friends",
          groupIds: ["all", "gift", "friend-visible"],
          order: 1,
          groupOrder: 1
        },
        {
          _id: "2",
          id: "2",
          // 添加 id 字段以保持兼容性
          title: "去日本旅行",
          description: "计划明年去日本旅行，看樱花",
          image: [],
          video: [],
          audio: [],
          createDate: (/* @__PURE__ */ new Date()).toISOString(),
          updateDate: (/* @__PURE__ */ new Date()).toISOString(),
          startDate: null,
          completeDate: null,
          isCompleted: false,
          permission: "private",
          groupIds: ["all"],
          order: 2,
          groupOrder: 1
        },
        {
          _id: "3",
          id: "3",
          // 添加 id 字段以保持兼容性
          title: "学习一门新语言",
          description: "学习法语，已经完成了初级课程",
          image: [],
          video: [],
          audio: [],
          createDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1e3).toISOString(),
          updateDate: (/* @__PURE__ */ new Date()).toISOString(),
          startDate: new Date(Date.now() - 25 * 24 * 60 * 60 * 1e3).toISOString(),
          completeDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1e3).toISOString(),
          isCompleted: true,
          permission: "friends",
          groupIds: ["all", "friend-visible"],
          order: 3,
          groupOrder: 1
        }
      ];
      this._saveToStorage();
    },
    // 从本地存储刷新心愿列表数据
    refreshWishList() {
      const storedWishes = common_vendor.index.getStorageSync("wishList");
      if (storedWishes) {
        const parsed = JSON.parse(storedWishes);
        this.wishList = parsed.map((wish) => ({
          ...wish,
          id: wish.id || wish._id
          // 如果没有 id 字段，使用 _id
        }));
      }
    },
    // 设置当前分组
    setCurrentGroup(groupId) {
      this.currentGroupId = groupId;
      this._clearWishesCache();
    },
    // 清除心愿列表缓存（内部方法） - 已废弃，保留以防兼容性问题
    _clearWishesCache() {
    },
    // 添加心愿
    async addWish(wishData) {
      try {
        const timestamp = Date.now();
        const enhancedWishData2 = {
          ...wishData,
          timestamp,
          deviceId: common_vendor.index.getSystemInfoSync().deviceId || "unknown"
        };
        if (!this.isOnline) {
          common_vendor.index.__f__("log", "at store/wish.js:840", "当前离线，心愿将保存到本地");
          return this._addWishOffline(enhancedWishData2);
        }
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.createWish(enhancedWishData2);
        if (result.errCode === 0) {
          const wishWithId = {
            ...result.data,
            id: result.data._id
            // 确保有 id 字段供前端组件使用
          };
          this.wishList.push(wishWithId);
          this._clearWishesCache();
          this._saveToStorage();
          this._triggerReactiveUpdate("add", wishWithId._id);
          return wishWithId;
        } else {
          throw new Error(result.errMsg || "创建心愿失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:866", "添加心愿失败:", error);
        if (ErrorHandler.isNetworkError(error)) {
          this.isOnline = false;
          return this._addWishOffline(enhancedWishData);
        }
        if (ErrorHandler.isVersionConflictError(error)) {
          ConflictManager.handleVersionConflict(error, "create_wish");
          return this._addWishOffline(enhancedWishData);
        }
        ErrorHandler.showError(error, "创建心愿失败", "create_wish");
        throw error;
      }
    },
    // 🚀 离线添加心愿 - 支持逻辑时钟
    _addWishOffline(wishData) {
      const wishId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const wish = {
        _id: wishId,
        id: wishId,
        // 同时添加 id 字段以保持兼容性
        title: wishData.title || "",
        description: wishData.description || "",
        image: Array.isArray(wishData.image) ? wishData.image : wishData.image ? [wishData.image] : [],
        video: Array.isArray(wishData.video) ? wishData.video : wishData.video ? [wishData.video] : [],
        audio: Array.isArray(wishData.audio) ? wishData.audio : wishData.audio ? [wishData.audio] : [],
        createDate: (/* @__PURE__ */ new Date()).toISOString(),
        updateDate: (/* @__PURE__ */ new Date()).toISOString(),
        startDate: wishData.startDate || null,
        completeDate: null,
        isCompleted: false,
        permission: wishData.permission || "private",
        groupIds: wishData.groupIds || ["all"],
        order: this.wishList.length + 1,
        groupOrder: this._getNextGroupOrder(wishData.groupIds || ["all"]),
        _needSync: true,
        // 标记需要同步到云端
        // 时间戳支持
        timestamp: wishData.timestamp || Date.now(),
        lastModifiedDevice: wishData.deviceId || common_vendor.index.getSystemInfoSync().deviceId || "unknown",
        version: 1
      };
      if (!wish.groupIds.includes("all")) {
        wish.groupIds.push("all");
      }
      if (wish.permission === "friends" && !wish.groupIds.includes("friend-visible")) {
        wish.groupIds.push("friend-visible");
      }
      this.wishList.push(wish);
      this._clearWishesCache();
      this._saveToStorage();
      this._triggerReactiveUpdate("add", wish._id);
      common_vendor.index.showToast({
        title: this.isOnline ? "已保存到本地，稍后将同步到云端" : "当前离线，已保存到本地",
        icon: "none",
        duration: 2500
      });
      return wish;
    },
    // 获取分组内下一个排序号
    _getNextGroupOrder(groupIds) {
      if (!groupIds || groupIds.length === 0)
        return 1;
      const maxOrder = this.wishList.filter((wish) => {
        return groupIds.some((groupId) => wish.groupIds && wish.groupIds.includes(groupId));
      }).reduce((max, wish) => Math.max(max, wish.groupOrder || 0), 0);
      return maxOrder + 1;
    },
    // 更新心愿
    async updateWish(updatedWish) {
      common_vendor.index.__f__("log", "at store/wish.js:959", "[wishStore] updateWish called with:", updatedWish);
      try {
        if (!updatedWish || !updatedWish._id && !updatedWish.id) {
          throw new Error("更新数据缺少必要的ID字段");
        }
        if (!this.isOnline) {
          common_vendor.index.__f__("log", "at store/wish.js:969", "当前离线，心愿将保存到本地");
          this._updateLocalWish(updatedWish);
          const index = this.wishList.findIndex((w) => w._id === updatedWish._id || w.id === updatedWish.id);
          if (index !== -1) {
            this.wishList[index]._needSync = true;
          }
          this._triggerReactiveUpdate("update", updatedWish._id || updatedWish.id);
          common_vendor.index.showToast({
            title: "当前离线，已保存到本地",
            icon: "none",
            duration: 2500
          });
          return;
        }
        const updateData = {
          ...updatedWish,
          updateDate: (/* @__PURE__ */ new Date()).toISOString(),
          // 设置当前时间戳
          version: (updatedWish.version || 1) + 1,
          // 递增版本号
          _forceUpdate: false
          // 不强制更新，允许冲突检测
        };
        const wishCenter = common_vendor.nr.importObject("wish-center");
        common_vendor.index.__f__("log", "at store/wish.js:999", "[wishStore] Calling cloud updateWish with ID:", updatedWish._id || updatedWish.id);
        const result = await wishCenter.updateWish(updatedWish._id || updatedWish.id, updateData);
        if (result.errCode === 0) {
          common_vendor.index.__f__("log", "at store/wish.js:1004", "[wishStore] Cloud update successful, updating local data");
          if (result.data) {
            this._updateLocalWish(result.data);
          } else {
            updatedWish.version = (updatedWish.version || 1) + 1;
            this._updateLocalWish(updatedWish);
          }
          this._triggerReactiveUpdate("update", updatedWish._id || updatedWish.id);
        } else {
          throw new Error(result.errMsg || "更新心愿失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:1021", "更新心愿失败:", error);
        if (ErrorHandler.isNetworkError(error)) {
          common_vendor.index.__f__("log", "at store/wish.js:1025", "[wishStore] Network error, saving locally");
          this.isOnline = false;
          this._updateLocalWish(updatedWish);
          const index = this.wishList.findIndex((w) => w._id === updatedWish._id || w.id === updatedWish.id);
          if (index !== -1) {
            this.wishList[index]._needSync = true;
          }
          this._triggerReactiveUpdate("update", updatedWish._id || updatedWish.id);
          common_vendor.index.showToast({
            title: "网络连接失败，已保存到本地",
            icon: "none",
            duration: 2500
          });
        } else if (error.message && (error.message.includes("心愿不存在") || error.message.includes("无权限") || error.message.includes("not found") || error.message.includes("permission"))) {
          common_vendor.index.__f__("log", "at store/wish.js:1050", `[wishStore] 多设备更新冲突: 心愿可能已被其他设备删除，移除本地记录`);
          const index = this.wishList.findIndex((w) => w._id === updatedWish._id || w.id === updatedWish.id);
          if (index !== -1) {
            this.wishList.splice(index, 1);
            this._reorderAfterDelete();
            this._saveToStorage();
          }
          common_vendor.index.showToast({
            title: "心愿已不存在",
            icon: "none",
            duration: 1500
          });
        } else if (ErrorHandler.isVersionConflictError(error)) {
          ConflictManager.handleVersionConflict(error, "update_wish");
          const index = this.wishList.findIndex((w) => w._id === updatedWish._id || w.id === updatedWish.id);
          if (index !== -1) {
            this.wishList[index]._needSync = true;
          }
        } else {
          ErrorHandler.showError(error, "更新心愿失败", "update_wish");
          throw error;
        }
      }
    },
    // 更新本地心愿数据
    _updateLocalWish(updatedWish) {
      common_vendor.index.__f__("log", "at store/wish.js:1082", "[wishStore] _updateLocalWish called with:", updatedWish);
      const index = this.wishList.findIndex((w) => w._id === updatedWish._id || w.id === updatedWish.id);
      common_vendor.index.__f__("log", "at store/wish.js:1085", "[wishStore] Found wish at index:", index);
      if (index !== -1) {
        const originalWish = this.wishList[index];
        common_vendor.index.__f__("log", "at store/wish.js:1089", "[wishStore] Original wish:", originalWish);
        const mergedWish = {
          ...originalWish,
          ...updatedWish,
          updateDate: (/* @__PURE__ */ new Date()).toISOString(),
          // 确保ID字段兼容性
          id: updatedWish.id || updatedWish._id || originalWish.id || originalWish._id,
          _id: updatedWish._id || updatedWish.id || originalWish._id || originalWish.id,
          // 确保必要字段有默认值
          permission: updatedWish.permission || originalWish.permission || "private",
          groupIds: updatedWish.groupIds || originalWish.groupIds || ["all"],
          tags: updatedWish.tags || originalWish.tags || [],
          title: updatedWish.title || originalWish.title || "",
          description: updatedWish.description || originalWish.description || ""
        };
        common_vendor.index.__f__("log", "at store/wish.js:1107", "[wishStore] Merged wish:", mergedWish);
        ["image", "video", "audio"].forEach((field) => {
          if (mergedWish[field] && !Array.isArray(mergedWish[field])) {
            mergedWish[field] = [mergedWish[field]];
          } else if (!mergedWish[field]) {
            mergedWish[field] = [];
          }
        });
        if (updatedWish.hasOwnProperty("permission")) {
          common_vendor.index.__f__("log", "at store/wish.js:1120", "[wishStore] Permission change detected:", updatedWish.permission);
          if (!Array.isArray(mergedWish.groupIds)) {
            common_vendor.index.__f__("log", "at store/wish.js:1124", "[wishStore] groupIds is not array, setting default");
            mergedWish.groupIds = ["all"];
          }
          if (mergedWish.permission === "friends") {
            if (!mergedWish.groupIds.includes("friend-visible")) {
              mergedWish.groupIds.push("friend-visible");
            }
          } else {
            mergedWish.groupIds = mergedWish.groupIds.filter((id) => id !== "friend-visible");
            if (mergedWish.groupIds.length === 0) {
              mergedWish.groupIds.push("all");
            }
          }
        }
        common_vendor.index.__f__("log", "at store/wish.js:1140", "[wishStore] Final merged wish:", mergedWish);
        this.wishList[index] = mergedWish;
        this._saveToStorage();
      } else {
        common_vendor.index.__f__("error", "at store/wish.js:1144", "[wishStore] Could not find wish to update with ID:", updatedWish._id || updatedWish.id);
      }
    },
    // 删除心愿
    async deleteWish(id) {
      try {
        if (!this.isOnline) {
          common_vendor.index.__f__("log", "at store/wish.js:1153", "当前离线，心愿删除将保存到本地");
          const wish = this.wishList.find((w) => w._id === id || w.id === id);
          if (wish) {
            wish._deleted = true;
            wish._needSync = true;
            wish.updateDate = (/* @__PURE__ */ new Date()).toISOString();
          }
          common_vendor.index.showToast({
            title: "当前离线，删除操作已保存",
            icon: "none",
            duration: 2500
          });
          return;
        }
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.deleteWish(id);
        if (result.errCode === 0) {
          this._deleteLocalWish(id);
          this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
          this._saveToStorage();
        } else {
          throw new Error(result.errMsg || "删除心愿失败");
        }
      } catch (error) {
        if (error.message && (error.message.includes("心愿不存在") || error.message.includes("无权限") || error.message.includes("not found") || error.message.includes("permission"))) {
          common_vendor.index.__f__("log", "at store/wish.js:1197", `[wishStore] 多设备删除冲突: 心愿可能已被其他设备删除，直接清理本地数据`);
          this._deleteLocalWish(id);
          common_vendor.index.showToast({
            title: "心愿已删除",
            icon: "success",
            duration: 1500
          });
          return;
        }
        common_vendor.index.__f__("error", "at store/wish.js:1208", "删除心愿失败:", error);
        if (ErrorHandler.isNetworkError(error)) {
          this.isOnline = false;
          const wish = this.wishList.find((w) => w._id === id || w.id === id);
          if (wish) {
            wish._deleted = true;
            wish._needSync = true;
            wish.updateDate = (/* @__PURE__ */ new Date()).toISOString();
          }
          common_vendor.index.showToast({
            title: "网络连接失败，删除操作已保存",
            icon: "none",
            duration: 2500
          });
        } else {
          ErrorHandler.showError(error, "删除心愿失败");
          throw error;
        }
      }
    },
    // 删除本地心愿数据
    _deleteLocalWish(id) {
      common_vendor.index.__f__("log", "at store/wish.js:1237", `[wishStore] 删除心愿 ${id}`);
      this._recordDeletedWish(id);
      const index = this.wishList.findIndex((wish) => wish._id === id || wish.id === id);
      if (index !== -1) {
        this.wishList.splice(index, 1);
        common_vendor.index.__f__("log", "at store/wish.js:1249", `[wishStore] 删除后剩余心愿数量: ${this.wishList.length}`);
        this._reorderAfterDelete();
        this.listUpdateCounter++;
        this._saveToStorage();
        this._triggerReactiveUpdate("delete", id);
        common_vendor.index.__f__("log", "at store/wish.js:1263", `[wishStore] 心愿删除完成，序号已更新`);
      } else {
        common_vendor.index.__f__("warn", "at store/wish.js:1265", `[wishStore] 未找到要删除的心愿: ${id}`);
      }
    },
    // 删除后重新计算序号的统一方法
    _reorderAfterDelete() {
      this.wishList.sort((a, b) => (a.order || 0) - (b.order || 0));
      this.wishList.forEach((wish, index) => {
        wish.order = index + 1;
        wish.updateDate = (/* @__PURE__ */ new Date()).toISOString();
      });
      common_vendor.index.__f__("log", "at store/wish.js:1280", `[wishStore] 序号重新计算完成，共${this.wishList.length}个心愿`);
    },
    // 完成心愿
    async completeWish(id) {
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.completeWish(id);
        if (result.errCode === 0) {
          const wish = this.wishList.find((w) => w._id === id || w.id === id);
          if (wish) {
            wish.isCompleted = true;
            wish.completeDate = (/* @__PURE__ */ new Date()).toISOString();
            wish.updateDate = (/* @__PURE__ */ new Date()).toISOString();
            if (result.data && result.data.version) {
              wish.version = result.data.version;
            } else {
              wish.version = (wish.version || 1) + 1;
            }
            this._saveToStorage();
            this._triggerReactiveUpdate("complete", id);
          }
        } else {
          throw new Error(result.errMsg || "完成心愿失败");
        }
      } catch (error) {
        if (error.message && (error.message.includes("心愿不存在") || error.message.includes("无权限") || error.message.includes("not found") || error.message.includes("permission"))) {
          common_vendor.index.__f__("log", "at store/wish.js:1320", `[wishStore] 多设备完成冲突: 心愿可能已被其他设备删除，移除本地记录`);
          const index = this.wishList.findIndex((w) => w._id === id || w.id === id);
          if (index !== -1) {
            this.wishList.splice(index, 1);
            this._reorderAfterDelete();
            this._saveToStorage();
          }
          return;
        }
        common_vendor.index.__f__("error", "at store/wish.js:1331", "完成心愿失败:", error);
        const wish = this.wishList.find((w) => w._id === id || w.id === id);
        if (wish) {
          wish.isCompleted = true;
          wish.completeDate = (/* @__PURE__ */ new Date()).toISOString();
          wish.updateDate = (/* @__PURE__ */ new Date()).toISOString();
          wish._needSync = true;
          this._saveToStorage();
          this._triggerReactiveUpdate("complete", id);
        }
      }
    },
    // 恢复心愿
    async restoreWish(id) {
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.restoreWish(id);
        if (result.errCode === 0) {
          const wish = this.wishList.find((w) => w._id === id || w.id === id);
          if (wish && wish.isCompleted) {
            wish.isCompleted = false;
            wish.completeDate = null;
            wish.updateDate = (/* @__PURE__ */ new Date()).toISOString();
            if (result.data && result.data.version) {
              wish.version = result.data.version;
            } else {
              wish.version = (wish.version || 1) + 1;
            }
            this._saveToStorage();
            this._triggerReactiveUpdate("restore", id);
            return true;
          }
        } else {
          throw new Error(result.errMsg || "恢复心愿失败");
        }
      } catch (error) {
        if (error.message && (error.message.includes("心愿不存在") || error.message.includes("无权限") || error.message.includes("not found") || error.message.includes("permission"))) {
          common_vendor.index.__f__("log", "at store/wish.js:1387", `[wishStore] 多设备恢复冲突: 心愿可能已被其他设备删除，移除本地记录`);
          const index = this.wishList.findIndex((w) => w._id === id || w.id === id);
          if (index !== -1) {
            this.wishList.splice(index, 1);
            this._reorderAfterDelete();
            this._saveToStorage();
          }
          return false;
        }
        common_vendor.index.__f__("error", "at store/wish.js:1398", "恢复心愿失败:", error);
        const wish = this.wishList.find((w) => w._id === id || w.id === id);
        if (wish && wish.isCompleted) {
          wish.isCompleted = false;
          wish.completeDate = null;
          wish.updateDate = (/* @__PURE__ */ new Date()).toISOString();
          wish._needSync = true;
          this._saveToStorage();
          return true;
        }
      }
      return false;
    },
    // 更新心愿排序（支持分组内排序）
    async updateWishOrder(orderedIds, groupId = "all") {
      if (!orderedIds || orderedIds.length === 0)
        return;
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const sortType = groupId === "all" ? "global" : "group";
        const result = await wishCenter.updateWishOrder(orderedIds, sortType, groupId);
        if (result.errCode === 0) {
          this._updateLocalOrder(orderedIds, groupId);
        } else {
          throw new Error(result.errMsg || "更新排序失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:1433", "更新排序失败:", error);
        this._updateLocalOrder(orderedIds, groupId);
      }
    },
    // 更新本地排序
    _updateLocalOrder(orderedIds, groupId) {
      orderedIds.forEach((id, index) => {
        const wish = this.wishList.find((w) => w._id === id || w.id === id);
        if (wish) {
          if (groupId === "all") {
            wish.order = index + 1;
          } else {
            wish.groupOrder = index + 1;
          }
          wish.updateDate = (/* @__PURE__ */ new Date()).toISOString();
          wish._needSync = true;
        }
      });
      this.wishList.sort((a, b) => {
        if (a.isCompleted !== b.isCompleted) {
          return a.isCompleted ? 1 : -1;
        }
        return (a.order || 0) - (b.order || 0);
      });
      this._saveToStorage();
      common_vendor.index.__f__("log", "at store/wish.js:1465", "心愿排序已更新并保存");
    },
    // 临时设置当前列表的顺序，用于拖拽排序过程中的实时显示
    setCurrentList(newList) {
      if (!newList || newList.length === 0)
        return;
      const idToWishMap = /* @__PURE__ */ new Map();
      this.wishList.forEach((wish) => {
        const key = wish._id || wish.id;
        idToWishMap.set(key, wish);
      });
      if (this.currentGroupId === "all") {
        const reorderedWishes = [];
        const completedWishes = this.wishList.filter((wish) => wish.isCompleted);
        newList.forEach((wish) => {
          if (idToWishMap.has(wish._id || wish.id)) {
            reorderedWishes.push(idToWishMap.get(wish._id || wish.id));
          }
        });
        const unorderedWishes = this.wishList.filter(
          (wish) => !wish.isCompleted && !reorderedWishes.some((w) => (w._id || w.id) === (wish._id || wish.id))
        );
        this.wishList = [...reorderedWishes, ...unorderedWishes, ...completedWishes];
      } else {
        const groupWishes = this.wishList.filter(
          (wish) => wish.groupIds && wish.groupIds.includes(this.currentGroupId) && !wish.isCompleted
        );
        const otherWishes = this.wishList.filter(
          (wish) => !wish.groupIds || !wish.groupIds.includes(this.currentGroupId) || wish.isCompleted
        );
        const reorderedGroupWishes = [];
        newList.forEach((wish) => {
          const key = wish._id || wish.id;
          if (idToWishMap.has(key)) {
            reorderedGroupWishes.push(idToWishMap.get(key));
          }
        });
        const unorderedGroupWishes = groupWishes.filter(
          (wish) => !reorderedGroupWishes.some((w) => (w._id || w.id) === (wish._id || wish.id))
        );
        this.wishList = [...reorderedGroupWishes, ...unorderedGroupWishes, ...otherWishes];
      }
    },
    // 同步需要上传的本地数据到云端
    async syncPendingData() {
      const pendingWishes = this.wishList.filter((wish) => wish._needSync);
      let syncedCount = 0;
      let failedCount = 0;
      if (pendingWishes.length === 0) {
        common_vendor.index.__f__("log", "at store/wish.js:1528", "[wishStore] No pending data to sync");
        return { syncedCount: 0, failedCount: 0 };
      }
      common_vendor.index.__f__("log", "at store/wish.js:1532", `[wishStore] Syncing ${pendingWishes.length} pending wishes...`);
      for (const wish of pendingWishes) {
        try {
          const wishCenter = common_vendor.nr.importObject("wish-center");
          if (wish._deleted) {
            const result = await wishCenter.deleteWish(wish._id);
            if (result.errCode === 0) {
              this.wishList = this.wishList.filter((w) => w._id !== wish._id && w.id !== wish.id);
              syncedCount++;
              common_vendor.index.__f__("log", "at store/wish.js:1545", `[wishStore] Deleted wish synced: ${wish.title}`);
            } else {
              throw new Error(result.errMsg || "删除心愿失败");
            }
          } else if (wish._id.startsWith("temp_")) {
            const result = await wishCenter.createWish(wish);
            if (result.errCode === 0) {
              const oldId = wish._id;
              wish._id = result.data._id;
              wish.id = result.data._id;
              delete wish._needSync;
              syncedCount++;
              common_vendor.index.__f__("log", "at store/wish.js:1559", `[wishStore] New wish synced: ${wish.title} (${oldId} -> ${wish._id})`);
            } else if (result.errCode === 409) {
              common_vendor.index.__f__("log", "at store/wish.js:1562", `[wishStore] Conflict detected for new wish: ${wish.title}, using cloud data`);
              const cloudWish = result.data;
              Object.assign(wish, cloudWish);
              delete wish._needSync;
              syncedCount++;
            } else {
              throw new Error(result.errMsg || "创建心愿失败");
            }
          } else {
            const result = await wishCenter.updateWish(wish._id, wish);
            if (result.errCode === 0) {
              delete wish._needSync;
              syncedCount++;
              common_vendor.index.__f__("log", "at store/wish.js:1576", `[wishStore] Updated wish synced: ${wish.title}`);
            } else if (result.errCode === 409) {
              common_vendor.index.__f__("log", "at store/wish.js:1579", `[wishStore] Update conflict for wish: ${wish.title}, using cloud data silently`);
              if (result.data) {
                Object.assign(wish, result.data);
                delete wish._needSync;
                syncedCount++;
                common_vendor.index.__f__("log", "at store/wish.js:1586", `[wishStore] Conflict resolved silently: cloud wins for ${wish.title}`);
              } else {
                common_vendor.index.__f__("log", "at store/wish.js:1589", `[wishStore] Conflict resolution failed for ${wish.title}, will retry later`);
                failedCount++;
              }
            } else {
              throw new Error(result.errMsg || "更新心愿失败");
            }
          }
          await new Promise((resolve) => setTimeout(resolve, 100));
        } catch (error) {
          if (error.message && (error.message.includes("心愿不存在") || error.message.includes("无权限") || error.message.includes("not found") || error.message.includes("permission"))) {
            common_vendor.index.__f__("log", "at store/wish.js:1609", `[wishStore] 多设备同步冲突处理: 心愿 "${wish.title}" 在云端不存在，可能已被其他设备删除`);
            delete wish._needSync;
            const localIndex = this.wishList.findIndex((w) => w._id === wish._id);
            if (localIndex !== -1) {
              common_vendor.index.__f__("log", "at store/wish.js:1617", `[wishStore] 清理本地已删除的心愿: ${wish.title}`);
              this.wishList.splice(localIndex, 1);
            }
            syncedCount++;
          } else {
            common_vendor.index.__f__("error", "at store/wish.js:1625", "同步心愿失败:", wish.title, error);
            failedCount++;
            if (ErrorHandler.isNetworkError(error)) {
              this.isOnline = false;
              common_vendor.index.__f__("log", "at store/wish.js:1631", "网络错误，停止同步");
              break;
            }
          }
        }
      }
      this._saveToStorage();
      if (syncedCount > 0) {
        common_vendor.index.__f__("log", "at store/wish.js:1643", `[wishStore] 同步成功: ${syncedCount}个心愿`);
      }
      if (failedCount > 0) {
        common_vendor.index.__f__("log", "at store/wish.js:1646", `[wishStore] 同步失败: ${failedCount}个心愿`);
      }
      return { syncedCount, failedCount };
    },
    // 解决更新冲突
    async _resolveUpdateConflict(localWish, cloudWish) {
      common_vendor.index.__f__("log", "at store/wish.js:1654", `[wishStore] Resolving conflict for: ${localWish.title}`);
      const localTime = new Date(localWish.updateDate || localWish.createDate);
      const cloudTime = new Date(cloudWish.updateDate || cloudWish.createDate);
      if (localTime > cloudTime) {
        try {
          const wishCenter = common_vendor.nr.importObject("wish-center");
          const result = await wishCenter.forceUpdateWish(localWish._id, {
            ...localWish,
            _forceUpdate: true,
            _conflictResolution: "local-wins"
          });
          if (result.errCode === 0) {
            delete localWish._needSync;
            common_vendor.index.__f__("log", "at store/wish.js:1671", `[wishStore] Conflict resolved: local wins for ${localWish.title}`);
            return true;
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at store/wish.js:1675", "强制更新失败:", error);
        }
      } else {
        common_vendor.index.__f__("log", "at store/wish.js:1679", `[wishStore] Conflict resolved: cloud wins for ${cloudWish.title}`);
        Object.assign(localWish, cloudWish);
        delete localWish._needSync;
        return true;
      }
      return false;
    },
    // 增量同步：只同步变更的数据
    async performIncrementalSync() {
      if (!this.lastSyncTime) {
        common_vendor.index.__f__("log", "at store/wish.js:1691", "[wishStore] No last sync time, performing full sync");
        return await this.syncFromCloud();
      }
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.getIncrementalChanges({
          since: this.lastSyncTime,
          includeDeleted: true
        });
        if (result.errCode === 0) {
          const { additions, updates, deletions } = result.data;
          let changesApplied = 0;
          if (additions && additions.length > 0) {
            additions.forEach((cloudWish) => {
              const existingIndex = this.wishList.findIndex((w) => w._id === cloudWish._id);
              if (existingIndex === -1) {
                this.wishList.push({
                  ...cloudWish,
                  id: cloudWish._id
                });
                changesApplied++;
                common_vendor.index.__f__("log", "at store/wish.js:1716", `[wishStore] Added new wish from cloud: ${cloudWish.title}`);
              }
            });
          }
          if (updates && updates.length > 0) {
            updates.forEach((cloudWish) => {
              const existingIndex = this.wishList.findIndex((w) => w._id === cloudWish._id);
              if (existingIndex !== -1) {
                const localWish = this.wishList[existingIndex];
                const localTime = new Date(localWish.updateDate || localWish.createDate);
                const cloudTime = new Date(cloudWish.updateDate || cloudWish.createDate);
                if (cloudTime > localTime && !localWish._needSync) {
                  this.wishList[existingIndex] = {
                    ...cloudWish,
                    id: cloudWish._id
                  };
                  changesApplied++;
                  common_vendor.index.__f__("log", "at store/wish.js:1737", `[wishStore] Updated wish from cloud: ${cloudWish.title}`);
                } else {
                  common_vendor.index.__f__("log", "at store/wish.js:1739", `[wishStore] Skipped cloud update for ${cloudWish.title} (local is newer or has pending changes)`);
                }
              }
            });
          }
          if (deletions && deletions.length > 0) {
            deletions.forEach((deletedId) => {
              const existingIndex = this.wishList.findIndex((w) => w._id === deletedId);
              if (existingIndex !== -1) {
                const removedWish = this.wishList[existingIndex];
                this.wishList.splice(existingIndex, 1);
                changesApplied++;
                common_vendor.index.__f__("log", "at store/wish.js:1753", `[wishStore] Removed deleted wish: ${removedWish.title}`);
              }
            });
          }
          if (changesApplied > 0) {
            this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
            this._saveToStorage();
            common_vendor.index.__f__("log", "at store/wish.js:1761", `[wishStore] Incremental sync completed: ${changesApplied} changes applied`);
          } else {
            common_vendor.index.__f__("log", "at store/wish.js:1763", "[wishStore] No changes to apply from incremental sync");
          }
          return { changesApplied };
        } else {
          throw new Error(result.errMsg || "增量同步失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:1771", "[wishStore] Incremental sync failed:", error);
        if (!ErrorHandler.isNetworkError(error)) {
          return await this.syncFromCloud();
        }
        throw error;
      }
    },
    // 保存到本地存储
    _saveToStorage() {
      common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
      common_vendor.index.setStorageSync("wishLastSyncTime", this.lastSyncTime);
    },
    // 强制重新初始化（用于登录后重新同步）
    async forceInit() {
      common_vendor.index.__f__("log", "at store/wish.js:1788", "[wishStore] Force initialization...");
      this.wishList = [];
      this.currentGroupId = "all";
      this.lastSyncTime = null;
      this.isLoading = false;
      common_vendor.index.removeStorageSync("wishList");
      common_vendor.index.removeStorageSync("wishLastSyncTime");
      await this.syncFromCloud();
      common_vendor.index.__f__("log", "at store/wish.js:1803", "[wishStore] Force initialization completed");
    },
    // 清理本地数据（用于重新登录时清除旧用户数据）
    clearLocalData() {
      common_vendor.index.__f__("log", "at store/wish.js:1808", "[wishStore] Clearing user-specific data...");
      this.wishList = [];
      this.currentGroupId = "all";
      this.lastSyncTime = null;
      this.isLoading = false;
      common_vendor.index.removeStorageSync("wishList");
      common_vendor.index.removeStorageSync("wishLastSyncTime");
      common_vendor.index.__f__("log", "at store/wish.js:1823", "[wishStore] User-specific data cleared, system functionality preserved");
    },
    // 调试方法：检查心愿数据的ID字段
    debugWishIds() {
      common_vendor.index.__f__("log", "at store/wish.js:1828", "=== Wish ID Debug Info ===");
      common_vendor.index.__f__("log", "at store/wish.js:1829", "Total wishes:", this.wishList.length);
      const issues = [];
      this.wishList.forEach((wish, index) => {
        if (!wish.id && !wish._id) {
          issues.push(`Wish ${index} (${wish.title}): Missing both ID fields`);
        } else if (!wish.id) {
          issues.push(`Wish ${index} (${wish.title}): Missing id field`);
        } else if (!wish._id) {
          issues.push(`Wish ${index} (${wish.title}): Missing _id field`);
        }
      });
      if (issues.length > 0) {
        common_vendor.index.__f__("warn", "at store/wish.js:1843", "ID字段问题:", issues);
      } else {
        common_vendor.index.__f__("log", "at store/wish.js:1845", "所有心愿ID字段正常");
      }
      common_vendor.index.__f__("log", "at store/wish.js:1848", "=========================");
    },
    // 修复心愿数据中的ID字段
    fixWishIds() {
      common_vendor.index.__f__("log", "at store/wish.js:1853", "[wishStore] Fixing wish ID fields...");
      let fixedCount = 0;
      this.wishList = this.wishList.map((wish) => {
        const hasIdIssue = !wish.id || !wish._id;
        if (hasIdIssue) {
          fixedCount++;
          return {
            ...wish,
            id: wish.id || wish._id,
            _id: wish._id || wish.id
          };
        }
        return wish;
      });
      if (fixedCount > 0) {
        this._saveToStorage();
        common_vendor.index.__f__("log", "at store/wish.js:1873", `[wishStore] Fixed ${fixedCount} wishes with ID issues`);
        common_vendor.index.showToast({
          title: `修复了 ${fixedCount} 个心愿的ID问题`,
          icon: "success"
        });
      } else {
        common_vendor.index.__f__("log", "at store/wish.js:1879", "[wishStore] No ID issues found");
        common_vendor.index.showToast({
          title: "所有心愿ID字段正常",
          icon: "success"
        });
      }
      return fixedCount;
    },
    /**
     * 🚀 处理实时数据更新
     * 当收到实时推送时调用此方法
     */
    async _updateLocalFromRealtimeData(realtimeData) {
      try {
        common_vendor.index.__f__("log", "at store/wish.js:1895", "[wishStore] 📝 收到实时心愿数据更新:", realtimeData.length, "个心愿");
        const mergedData = this._mergeWishData(this.wishList, realtimeData);
        this.wishList = mergedData;
        this._clearWishesCache();
        this._saveToStorage();
        common_vendor.index.__f__("log", "at store/wish.js:1909", "[wishStore] ✅ 实时心愿数据更新完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:1912", "[wishStore] 处理实时心愿数据更新失败:", error);
      }
    },
    // 🚀 新增：支持新同步架构的方法
    /**
     * 添加心愿到列表（供实时同步调用）
     */
    addWishToList(wish) {
      const existingIndex = this.wishList.findIndex((w) => w._id === wish._id);
      if (existingIndex === -1) {
        this.wishList.push({
          ...wish,
          id: wish._id
          // 确保有 id 字段
        });
        this._saveToStorage();
        common_vendor.index.__f__("log", "at store/wish.js:1929", "[wishStore] 🚀 添加心愿到列表:", wish._id);
      }
    },
    /**
     * 更新列表中的心愿（供实时同步调用）
     */
    updateWishInList(wish) {
      const index = this.wishList.findIndex((w) => w._id === wish._id);
      if (index !== -1) {
        this.wishList[index] = {
          ...wish,
          id: wish._id
          // 确保有 id 字段
        };
        this._saveToStorage();
        common_vendor.index.__f__("log", "at store/wish.js:1944", "[wishStore] 🚀 更新列表中的心愿:", wish._id);
      }
    },
    /**
     * 从列表中移除心愿（供实时同步调用）
     */
    removeWishFromList(wishId) {
      const index = this.wishList.findIndex((w) => w._id === wishId);
      if (index !== -1) {
        this.wishList.splice(index, 1);
        this._saveToStorage();
        common_vendor.index.__f__("log", "at store/wish.js:1956", "[wishStore] 🚀 从列表中移除心愿:", wishId);
      }
    },
    /**
     * 从云端同步数据（供新同步管理器调用）- 优化版本
     */
    async syncFromCloud() {
      try {
        common_vendor.index.__f__("log", "at store/wish.js:1965", "[wishStore] 🔄 从云端同步心愿数据...");
        const userStore = store_user.useUserStore();
        if (!userStore.isLogin) {
          common_vendor.index.__f__("warn", "at store/wish.js:1969", "[wishStore] 用户未登录，跳过同步");
          return;
        }
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.getWishList({
          page: 1,
          pageSize: 1e3,
          includeCompleted: true
        });
        if (result.errCode === 0) {
          this.wishList = result.data.map((wish) => ({
            ...wish,
            id: wish._id
            // 确保有 id 字段
          }));
          this._saveToStorage();
          common_vendor.index.__f__("log", "at store/wish.js:1988", "[wishStore] ✅ 云端同步完成，共", result.data.length, "个心愿");
        } else {
          throw new Error(result.errMsg || "同步失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:1993", "[wishStore] 云端同步失败:", error);
        throw error;
      }
    },
    // ==================== 新增同步优化方法 ====================
    /**
     * 从同步中添加心愿（避免触发推送）
     */
    addWishFromSync(wish) {
      const existingIndex = this.wishList.findIndex((w) => w.id === wish.id || w._id === wish._id);
      if (existingIndex === -1) {
        this.wishList.push({
          ...wish,
          id: wish._id || wish.id
        });
        this._saveToStorage();
        common_vendor.index.__f__("log", "at store/wish.js:2011", `[wishStore] 从同步添加心愿: ${wish.title}`);
      }
    },
    /**
     * 从同步中更新心愿（避免触发推送）
     */
    updateWishFromSync(wish) {
      const index = this.wishList.findIndex((w) => w.id === wish.id || w._id === wish._id);
      if (index !== -1) {
        this.wishList[index] = {
          ...wish,
          id: wish._id || wish.id
        };
        this._saveToStorage();
        common_vendor.index.__f__("log", "at store/wish.js:2026", `[wishStore] 从同步更新心愿: ${wish.title}`);
      }
    },
    /**
     * 从同步中删除心愿（避免触发推送）
     */
    removeWishById(wishId) {
      const index = this.wishList.findIndex((w) => w.id === wishId || w._id === wishId);
      if (index !== -1) {
        const removedWish = this.wishList.splice(index, 1)[0];
        this._saveToStorage();
        common_vendor.index.__f__("log", "at store/wish.js:2038", `[wishStore] 从同步删除心愿: ${removedWish.title}`);
      }
    },
    // 智能同步 - 只同步有差异的数据（用于下拉刷新）
    async smartSync() {
      common_vendor.index.__f__("log", "at store/wish.js:2044", "[wishStore] Starting smart sync...");
      if (!this.isOnline) {
        common_vendor.index.__f__("warn", "at store/wish.js:2047", "[wishStore] Network unavailable, skipping smart sync");
        return { hasUpdates: false, updatedCount: 0, reason: "offline" };
      }
      try {
        const pendingWishes = this.wishList.filter((wish) => wish._needSync);
        if (pendingWishes.length > 0) {
          common_vendor.index.__f__("log", "at store/wish.js:2055", `[wishStore] Uploading ${pendingWishes.length} pending wishes...`);
          await this.syncPendingData();
        }
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const summaryResult = await wishCenter.getSyncSummary();
        if (summaryResult.errCode !== 0) {
          throw new Error(summaryResult.errMsg || "获取云端心愿摘要失败");
        }
        const cloudSummary = summaryResult.data || {};
        common_vendor.index.__f__("log", "at store/wish.js:2068", "[wishStore] Cloud summary received:", cloudSummary);
        const localSummary = this._generateLocalWishSummary();
        common_vendor.index.__f__("log", "at store/wish.js:2072", "[wishStore] Local summary:", localSummary);
        const needsSync = this._compareWishDataSummaries(localSummary, cloudSummary);
        if (!needsSync) {
          common_vendor.index.__f__("log", "at store/wish.js:2078", "[wishStore] Data is up to date, no sync needed");
          return { hasUpdates: false, updatedCount: 0, reason: "up_to_date" };
        }
        common_vendor.index.__f__("log", "at store/wish.js:2083", "[wishStore] Data differences detected, performing incremental sync...");
        const syncResult = await this._performWishIncrementalSync(cloudSummary);
        this.cleanupInvalidSyncMarkers();
        common_vendor.index.__f__("log", "at store/wish.js:2089", "[wishStore] Smart sync completed:", syncResult);
        return syncResult;
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:2093", "[wishStore] Smart sync failed:", error);
        throw error;
      }
    },
    // 生成本地心愿数据摘要
    _generateLocalWishSummary() {
      return {
        count: this.wishList.length,
        lastModified: this._getLocalLastModified(),
        checksum: this._calculateWishesChecksum(this.wishList)
      };
    },
    // 计算心愿数据校验和
    _calculateWishesChecksum(wishes) {
      const dataStr = wishes.map((w) => `${w._id}:${w.title}:${w.updateDate || w.createDate}:${w.isCompleted}`).sort().join("|");
      let hash = 0;
      for (let i = 0; i < dataStr.length; i++) {
        const char = dataStr.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash;
      }
      return hash.toString(36);
    },
    // 对比心愿数据摘要
    _compareWishDataSummaries(localSummary, cloudSummary) {
      if (localSummary.count !== cloudSummary.count) {
        common_vendor.index.__f__("log", "at store/wish.js:2128", "[wishStore] Count difference detected:", localSummary.count, "vs", cloudSummary.count);
        return true;
      }
      if (localSummary.checksum !== cloudSummary.checksum) {
        common_vendor.index.__f__("log", "at store/wish.js:2134", "[wishStore] Checksum difference detected:", localSummary.checksum, "vs", cloudSummary.checksum);
        return true;
      }
      if (cloudSummary.lastModified && localSummary.lastModified) {
        const cloudTime = new Date(cloudSummary.lastModified).getTime();
        const localTime = new Date(localSummary.lastModified).getTime();
        if (cloudTime > localTime) {
          common_vendor.index.__f__("log", "at store/wish.js:2144", "[wishStore] Cloud data is newer:", cloudSummary.lastModified, "vs", localSummary.lastModified);
          return true;
        }
      }
      return false;
    },
    // 执行心愿增量同步
    async _performWishIncrementalSync(cloudSummary) {
      const wishCenter = common_vendor.nr.importObject("wish-center");
      const result = await wishCenter.getWishList({
        page: 1,
        pageSize: 1e3,
        includeCompleted: true,
        includeDeleted: true
      });
      if (result.errCode !== 0) {
        throw new Error(result.errMsg || "获取云端心愿数据失败");
      }
      const cloudWishes = result.data || [];
      const beforeCount = this.wishList.length;
      const mergedData = this._mergeWishData(this.wishList, cloudWishes);
      await this._safeUpdateWishes(mergedData);
      const afterCount = this.wishList.length;
      const updatedCount = Math.abs(afterCount - beforeCount);
      this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
      this._saveToStorage();
      return {
        hasUpdates: true,
        updatedCount,
        beforeCount,
        afterCount,
        reason: "incremental_sync"
      };
    },
    // 安全更新心愿数据
    async _safeUpdateWishes(newData) {
      try {
        await new Promise((resolve) => {
          if (this.$nextTick) {
            this.$nextTick(() => {
              this.wishList = newData.map((wish) => ({
                ...wish,
                id: wish._id
                // 确保有 id 字段供前端组件使用
              }));
              resolve();
            });
          } else {
            setTimeout(() => {
              this.wishList = newData.map((wish) => ({
                ...wish,
                id: wish._id
                // 确保有 id 字段供前端组件使用
              }));
              resolve();
            }, 0);
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:2215", "[wishStore] Error in safe update, falling back to direct assignment:", error);
        this.wishList = newData.map((wish) => ({
          ...wish,
          id: wish._id
          // 确保有 id 字段供前端组件使用
        }));
      }
    },
    /**
     * 统一的响应式更新触发器
     * @param {string} action - 操作类型 ('complete', 'delete', 'add', 'update')
     * @param {string} wishId - 心愿ID
     */
    _triggerReactiveUpdate(action, wishId) {
      common_vendor.index.__f__("log", "at store/wish.js:2229", `[wishStore] 触发响应式更新: ${action} - ${wishId}`);
      this.listUpdateCounter++;
      common_vendor.index.$emit("wish-list-updated", {
        timestamp: Date.now(),
        action,
        wishId,
        updateCounter: this.listUpdateCounter
      });
      this.$nextTick && this.$nextTick(() => {
        common_vendor.index.__f__("log", "at store/wish.js:2244", `[wishStore] 响应式更新完成: ${action} - ${wishId}`);
      });
      common_vendor.index.__f__("log", "at store/wish.js:2247", `[wishStore] 响应式更新已触发，当前计数器: ${this.listUpdateCounter}`);
    },
    // 清理无效的同步标记（多设备冲突后的清理）
    cleanupInvalidSyncMarkers() {
      let cleanedCount = 0;
      this.wishList.forEach((wish) => {
        if (wish._needSync && (!wish._id || !wish.title)) {
          common_vendor.index.__f__("log", "at store/wish.js:2257", `[wishStore] 清理无效的同步标记: ${wish.title || "未知心愿"}`);
          delete wish._needSync;
          cleanedCount++;
        }
      });
      if (cleanedCount > 0) {
        common_vendor.index.__f__("log", "at store/wish.js:2264", `[wishStore] 已清理 ${cleanedCount} 个无效的同步标记`);
        this._saveToStorage();
      }
      return cleanedCount;
    }
  }
});
exports.useWishStore = useWishStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/wish.js.map
