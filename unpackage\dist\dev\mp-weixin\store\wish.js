"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const ERROR_CODES = {
  NETWORK_ERROR: "NETWORK_ERROR",
  AUTH_ERROR: "AUTH_ERROR",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  PERMISSION_ERROR: "PERMISSION_ERROR",
  NOT_FOUND_ERROR: "NOT_FOUND_ERROR",
  SERVER_ERROR: "SERVER_ERROR"
};
const ErrorHandler = {
  getUserFriendlyMessage(error) {
    if (typeof error === "string")
      return error;
    return error.message || error.errMsg || "未知错误";
  },
  isNetworkError(error) {
    if (error.errCode === ERROR_CODES.NETWORK_ERROR)
      return true;
    if (error.code === "NETWORK_ERROR")
      return true;
    return false;
  },
  isVersionConflictError(error) {
    return error && error.message && (error.message.includes("数据版本冲突") || error.message.includes("逻辑时钟冲突") || error.message.includes("version conflict") || error.message.includes("conflict"));
  },
  showError(error, title = "操作失败", context = "unknown") {
    const message = this.getUserFriendlyMessage(error);
    common_vendor.index.showToast({
      title: message,
      icon: "none",
      duration: 3e3
    });
    common_vendor.index.__f__("error", "at store/wish.js:46", `${title}:`, error);
  }
};
const useWishStore = common_vendor.defineStore("wish", {
  state: () => ({
    wishList: [],
    currentGroupId: "all",
    isLoading: false,
    lastSyncTime: null,
    isOnline: true,
    listUpdateCounter: 0
    // 🚀 添加列表更新计数器
  }),
  getters: {
    activeWishes: (state) => {
      return state.wishList.filter((wish) => !wish._deleted && !wish.isCompleted);
    },
    // 🚀 添加当前分组的心愿列表
    currentGroupWishes: (state) => {
      const activeWishes = state.wishList.filter((wish) => !wish._deleted && !wish.isCompleted);
      if (state.currentGroupId === "all") {
        return activeWishes;
      } else if (state.currentGroupId === "completed") {
        return state.wishList.filter((wish) => !wish._deleted && wish.isCompleted);
      } else {
        return activeWishes.filter(
          (wish) => wish.groupIds && wish.groupIds.includes(state.currentGroupId)
        );
      }
    },
    // 🚀 添加其他必要的 getters
    getWishById: (state) => (id) => {
      return state.wishList.find(
        (wish) => (wish._id === id || wish.id === id) && !wish._deleted
      ) || null;
    }
  },
  actions: {
    async initWishList() {
      common_vendor.index.__f__("log", "at store/wish.js:91", "[wishStore] Initializing wish list...");
      this.isLoading = true;
      try {
        const storedWishes = common_vendor.index.getStorageSync("wishList");
        if (storedWishes) {
          const parsed = JSON.parse(storedWishes);
          this.wishList = parsed.map((wish) => ({
            ...wish,
            id: wish.id || wish._id
          }));
        }
        common_vendor.index.__f__("log", "at store/wish.js:105", "[wishStore] Wish list initialization completed");
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:107", "[wishStore] Failed to initialize wish list:", error);
      } finally {
        this.isLoading = false;
      }
    },
    // 🚀 添加基本的同步方法来测试云函数修改
    async syncFromCloud() {
      common_vendor.index.__f__("log", "at store/wish.js:115", "[wishStore] 开始从云端同步数据...");
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.getWishesByUser();
        if (result.errCode === 0) {
          this.wishList = result.data.map((wish) => ({
            ...wish,
            id: wish._id
          }));
          common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
          common_vendor.index.__f__("log", "at store/wish.js:131", "[wishStore] 同步成功，获取到", result.data.length, "个心愿");
        } else {
          common_vendor.index.__f__("error", "at store/wish.js:133", "[wishStore] 同步失败:", result.errMsg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:136", "[wishStore] 同步出错:", error);
        ErrorHandler.showError(error, "同步失败");
      }
    },
    // 🚀 添加删除方法来测试多设备冲突处理
    async deleteWish(id) {
      common_vendor.index.__f__("log", "at store/wish.js:143", "[wishStore] 删除心愿:", id);
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.deleteWish(id);
        if (result.errCode === 0) {
          const index = this.wishList.findIndex((w) => w._id === id || w.id === id);
          if (index !== -1) {
            this.wishList.splice(index, 1);
            common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          }
          common_vendor.index.showToast({
            title: "删除成功",
            icon: "success"
          });
          common_vendor.index.__f__("log", "at store/wish.js:162", "[wishStore] 删除成功");
        } else {
          common_vendor.index.__f__("error", "at store/wish.js:164", "[wishStore] 删除失败:", result.errMsg);
          ErrorHandler.showError({ message: result.errMsg }, "删除失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:168", "[wishStore] 删除出错:", error);
        ErrorHandler.showError(error, "删除失败");
      }
    },
    // 🚀 添加其他必要的方法
    setCurrentGroup(groupId) {
      this.currentGroupId = groupId;
    },
    // 强制同步数据（用于下拉刷新）
    async forceSyncData() {
      common_vendor.index.__f__("log", "at store/wish.js:180", "[wishStore] 强制同步数据...");
      await this.syncFromCloud();
    },
    // 手动同步（兼容原有接口）
    async manualSync(silent = false) {
      if (!silent) {
        common_vendor.index.showLoading({ title: "同步中..." });
      }
      try {
        await this.syncFromCloud();
        if (!silent) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "同步完成",
            icon: "success"
          });
        }
      } catch (error) {
        if (!silent) {
          common_vendor.index.hideLoading();
        }
        throw error;
      }
    },
    // 完成心愿
    async completeWish(id) {
      common_vendor.index.__f__("log", "at store/wish.js:210", "[wishStore] 完成心愿:", id);
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.completeWish(id);
        if (result.errCode === 0) {
          const wish = this.wishList.find((w) => w._id === id || w.id === id);
          if (wish) {
            wish.isCompleted = true;
            wish.completeDate = (/* @__PURE__ */ new Date()).toISOString();
            common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          }
          common_vendor.index.showToast({
            title: "心愿已完成",
            icon: "success"
          });
        } else {
          ErrorHandler.showError({ message: result.errMsg }, "完成失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:233", "[wishStore] 完成心愿出错:", error);
        ErrorHandler.showError(error, "完成失败");
      }
    },
    // 恢复心愿
    async restoreWish(id) {
      common_vendor.index.__f__("log", "at store/wish.js:240", "[wishStore] 恢复心愿:", id);
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.restoreWish(id);
        if (result.errCode === 0) {
          const wish = this.wishList.find((w) => w._id === id || w.id === id);
          if (wish) {
            wish.isCompleted = false;
            wish.completeDate = null;
            common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          }
          common_vendor.index.showToast({
            title: "心愿已恢复",
            icon: "success"
          });
        } else {
          ErrorHandler.showError({ message: result.errMsg }, "恢复失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:263", "[wishStore] 恢复心愿出错:", error);
        ErrorHandler.showError(error, "恢复失败");
      }
    },
    // 🚀 清理本地数据（登录后重新初始化时使用）
    clearLocalData() {
      common_vendor.index.__f__("log", "at store/wish.js:270", "[wishStore] 清理本地数据...");
      this.wishList = [];
      this.currentGroupId = "all";
      this.lastSyncTime = null;
      this.isLoading = false;
      common_vendor.index.removeStorageSync("wishList");
      common_vendor.index.removeStorageSync("wishLastSyncTime");
      common_vendor.index.__f__("log", "at store/wish.js:282", "[wishStore] 本地数据清理完成");
    },
    // 🚀 强制初始化（登录后使用）
    async forceInit() {
      common_vendor.index.__f__("log", "at store/wish.js:287", "[wishStore] 强制初始化...");
      this.clearLocalData();
      await this.syncFromCloud();
      common_vendor.index.__f__("log", "at store/wish.js:295", "[wishStore] 强制初始化完成");
    },
    // 🚀 用户登出时清理数据
    clearUserData() {
      common_vendor.index.__f__("log", "at store/wish.js:300", "[wishStore] 清理用户数据...");
      this.wishList = [];
      this.currentGroupId = "all";
      this.lastSyncTime = null;
      this.isLoading = false;
      common_vendor.index.removeStorageSync("wishList");
      common_vendor.index.removeStorageSync("wishLastSyncTime");
      common_vendor.index.__f__("log", "at store/wish.js:315", "[wishStore] 用户数据清理完成");
    },
    // 🚀 刷新心愿列表（从本地存储重新加载）
    refreshWishList() {
      common_vendor.index.__f__("log", "at store/wish.js:320", "[wishStore] 刷新心愿列表...");
      try {
        const storedWishes = common_vendor.index.getStorageSync("wishList");
        if (storedWishes) {
          const parsed = JSON.parse(storedWishes);
          this.wishList = parsed.map((wish) => ({
            ...wish,
            id: wish.id || wish._id
          }));
          common_vendor.index.__f__("log", "at store/wish.js:330", "[wishStore] 心愿列表已刷新，共", this.wishList.length, "个心愿");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:333", "[wishStore] 刷新心愿列表失败:", error);
      }
    },
    // 🚀 更新心愿排序
    updateWishOrder(orderedIds) {
      common_vendor.index.__f__("log", "at store/wish.js:339", "[wishStore] 更新心愿排序:", orderedIds);
      const reorderedWishes = [];
      orderedIds.forEach((id) => {
        const wish = this.wishList.find((w) => w._id === id || w.id === id);
        if (wish) {
          reorderedWishes.push(wish);
        }
      });
      this.wishList.forEach((wish) => {
        if (!orderedIds.includes(wish._id) && !orderedIds.includes(wish.id)) {
          reorderedWishes.push(wish);
        }
      });
      this.wishList = reorderedWishes;
      common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
      common_vendor.index.__f__("log", "at store/wish.js:360", "[wishStore] 心愿排序已更新");
    },
    // 🚀 设置当前心愿列表
    setCurrentList(newWishList) {
      common_vendor.index.__f__("log", "at store/wish.js:365", "[wishStore] 设置当前心愿列表:", newWishList.length, "个心愿");
      this.wishList = newWishList.map((wish) => ({
        ...wish,
        id: wish.id || wish._id
      }));
      common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
    }
  }
});
exports.useWishStore = useWishStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/wish.js.map
