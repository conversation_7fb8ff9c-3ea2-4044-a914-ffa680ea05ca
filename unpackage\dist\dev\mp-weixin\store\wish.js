"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const ERROR_CODES = {
  NETWORK_ERROR: "NETWORK_ERROR",
  AUTH_ERROR: "AUTH_ERROR",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  PERMISSION_ERROR: "PERMISSION_ERROR",
  NOT_FOUND_ERROR: "NOT_FOUND_ERROR",
  SERVER_ERROR: "SERVER_ERROR"
};
const ErrorHandler = {
  getUserFriendlyMessage(error) {
    if (typeof error === "string")
      return error;
    return error.message || error.errMsg || "未知错误";
  },
  isNetworkError(error) {
    if (error.errCode === ERROR_CODES.NETWORK_ERROR)
      return true;
    if (error.code === "NETWORK_ERROR")
      return true;
    return false;
  },
  isVersionConflictError(error) {
    return error && error.message && (error.message.includes("数据版本冲突") || error.message.includes("逻辑时钟冲突") || error.message.includes("version conflict") || error.message.includes("conflict"));
  },
  showError(error, title = "操作失败", context = "unknown") {
    const message = this.getUserFriendlyMessage(error);
    common_vendor.index.showToast({
      title: message,
      icon: "none",
      duration: 3e3
    });
    common_vendor.index.__f__("error", "at store/wish.js:46", `${title}:`, error);
  }
};
const useWishStore = common_vendor.defineStore("wish", {
  state: () => ({
    wishList: [],
    currentGroupId: "all",
    isLoading: false,
    lastSyncTime: null,
    isOnline: true
  }),
  getters: {
    activeWishes: (state) => {
      return state.wishList.filter((wish) => !wish._deleted && !wish.isCompleted);
    }
  },
  actions: {
    async initWishList() {
      common_vendor.index.__f__("log", "at store/wish.js:67", "[wishStore] Initializing wish list...");
      this.isLoading = true;
      try {
        const storedWishes = common_vendor.index.getStorageSync("wishList");
        if (storedWishes) {
          const parsed = JSON.parse(storedWishes);
          this.wishList = parsed.map((wish) => ({
            ...wish,
            id: wish.id || wish._id
          }));
        }
        common_vendor.index.__f__("log", "at store/wish.js:81", "[wishStore] Wish list initialization completed");
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:83", "[wishStore] Failed to initialize wish list:", error);
      } finally {
        this.isLoading = false;
      }
    },
    // 🚀 添加基本的同步方法来测试云函数修改
    async syncFromCloud() {
      common_vendor.index.__f__("log", "at store/wish.js:91", "[wishStore] 开始从云端同步数据...");
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.getWishesByUser();
        if (result.errCode === 0) {
          this.wishList = result.data.map((wish) => ({
            ...wish,
            id: wish._id
          }));
          common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
          common_vendor.index.__f__("log", "at store/wish.js:107", "[wishStore] 同步成功，获取到", result.data.length, "个心愿");
        } else {
          common_vendor.index.__f__("error", "at store/wish.js:109", "[wishStore] 同步失败:", result.errMsg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:112", "[wishStore] 同步出错:", error);
        ErrorHandler.showError(error, "同步失败");
      }
    },
    // 🚀 添加删除方法来测试多设备冲突处理
    async deleteWish(id) {
      common_vendor.index.__f__("log", "at store/wish.js:119", "[wishStore] 删除心愿:", id);
      try {
        const wishCenter = common_vendor.nr.importObject("wish-center");
        const result = await wishCenter.deleteWish(id);
        if (result.errCode === 0) {
          const index = this.wishList.findIndex((w) => w._id === id || w.id === id);
          if (index !== -1) {
            this.wishList.splice(index, 1);
            common_vendor.index.setStorageSync("wishList", JSON.stringify(this.wishList));
          }
          common_vendor.index.showToast({
            title: "删除成功",
            icon: "success"
          });
          common_vendor.index.__f__("log", "at store/wish.js:138", "[wishStore] 删除成功");
        } else {
          common_vendor.index.__f__("error", "at store/wish.js:140", "[wishStore] 删除失败:", result.errMsg);
          ErrorHandler.showError({ message: result.errMsg }, "删除失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/wish.js:144", "[wishStore] 删除出错:", error);
        ErrorHandler.showError(error, "删除失败");
      }
    }
  }
});
exports.useWishStore = useWishStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/wish.js.map
