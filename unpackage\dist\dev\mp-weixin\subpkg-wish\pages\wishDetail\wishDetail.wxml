<view class="wish-detail-container"><view class="wish-detail card"><view class="wish-header"><view class="wish-title-wrap"><view class="wish-title">{{a}}</view></view><view class="wish-status"><view wx:if="{{b}}" class="tag private-tag"><uni-icons wx:if="{{c}}" u-i="38e9e0de-0" bind:__l="__l" u-p="{{c}}"></uni-icons><text>私密</text></view><view wx:elif="{{d}}" class="tag friends-tag"><uni-icons wx:if="{{e}}" u-i="38e9e0de-1" bind:__l="__l" u-p="{{e}}"></uni-icons><text>朋友可见</text></view><view wx:elif="{{f}}" class="tag public-tag"><uni-icons wx:if="{{g}}" u-i="38e9e0de-2" bind:__l="__l" u-p="{{g}}"></uni-icons><text>公开</text></view><view wx:if="{{h}}" class="tag completed-tag"><uni-icons wx:if="{{i}}" u-i="38e9e0de-3" bind:__l="__l" u-p="{{i}}"></uni-icons><text>已完成</text></view></view></view><view class="wish-content"><view wx:if="{{j}}" class="wish-desc">{{k}}</view><image wx:if="{{l}}" class="wish-image" src="{{m}}" mode="widthFix" bindtap="{{n}}"></image><swiper wx:elif="{{o}}" class="image-swiper" indicator-dots="{{true}}" autoplay="{{false}}" duration="{{500}}" indicator-active-color="#8a2be2"><swiper-item wx:for="{{p}}" wx:for-item="img" wx:key="c"><image class="swiper-image" src="{{img.a}}" mode="widthFix" bindtap="{{img.b}}"></image></swiper-item></swiper><view class="wish-time-info"><view class="time-item"><text class="time-label">创建时间：</text><text class="time-value">{{q}}</text></view><view wx:if="{{r}}" class="time-item"><text class="time-label">开始时间：</text><text class="time-value">{{s}}</text></view><view wx:if="{{t}}" class="time-item"><text class="time-label">完成时间：</text><text class="time-value">{{v}}</text></view></view></view><view class="wish-actions"><view class="action-btn" bindtap="{{x}}"><uni-icons wx:if="{{w}}" u-i="38e9e0de-4" bind:__l="__l" u-p="{{w}}"></uni-icons><text>编辑</text></view><button open-type="share" class="action-btn share-btn" bindtap="{{z}}"><uni-icons wx:if="{{y}}" u-i="38e9e0de-5" bind:__l="__l" u-p="{{y}}"></uni-icons><text>分享</text></button><view wx:if="{{A}}" class="action-btn" bindtap="{{C}}"><uni-icons wx:if="{{B}}" u-i="38e9e0de-6" bind:__l="__l" u-p="{{B}}"></uni-icons><text>完成</text></view><view class="action-btn delete" bindtap="{{E}}"><uni-icons wx:if="{{D}}" u-i="38e9e0de-7" bind:__l="__l" u-p="{{D}}"></uni-icons><text>删除</text></view></view></view><view class="wish-groups card"><view class="section-title"><text>所属分组</text></view><view class="group-tags"><view wx:for="{{F}}" wx:for-item="groupId" wx:key="b" class="group-tag" bindtap="{{groupId.c}}">{{groupId.a}}</view></view></view><view class="wish-comments card" bindtap="{{S}}"><view class="section-title"><text>评论区</text><text class="comment-count">{{G}}条评论</text></view><view wx:if="{{H}}" class="empty-comment"><text>暂无评论，快来添加第一条评论吧</text></view><view wx:else class="comment-list"><view wx:for="{{I}}" wx:for-item="comment" wx:key="g" class="comment-item" bindlongpress="{{comment.h}}" bindtouchstart="{{comment.i}}" bindtouchend="{{comment.j}}" catchtap="{{comment.k}}"><image class="comment-avatar" src="{{comment.a}}" mode="aspectFill"></image><view class="comment-content"><view class="comment-user"><text class="comment-nickname">{{comment.b}}</text><text class="comment-time">{{comment.c}}</text></view><view class="comment-text">{{comment.d}}</view><image wx:if="{{comment.e}}" class="comment-image" src="{{comment.f}}" mode="widthFix"></image></view></view></view><view wx:if="{{J}}" class="floating-delete-btn" style="{{L}}" catchtap="{{M}}"><uni-icons wx:if="{{K}}" u-i="38e9e0de-8" bind:__l="__l" u-p="{{K}}"></uni-icons><text>删除</text></view><view class="comment-form"><input class="comment-input" placeholder="添加评论..." confirm-type="send" bindconfirm="{{N}}" value="{{O}}" bindinput="{{P}}"/><view class="comment-btn" bindtap="{{R}}"><uni-icons wx:if="{{Q}}" u-i="38e9e0de-9" bind:__l="__l" u-p="{{Q}}"></uni-icons></view></view></view><uni-popup wx:if="{{X}}" class="r" u-s="{{['d']}}" u-r="deletePopup" u-i="38e9e0de-10" bind:__l="__l" u-p="{{X}}"><uni-popup-dialog wx:if="{{V}}" bindconfirm="{{T}}" bindclose="{{U}}" u-i="38e9e0de-11,38e9e0de-10" bind:__l="__l" u-p="{{V}}"></uni-popup-dialog></uni-popup><uni-popup wx:if="{{ac}}" class="r" u-s="{{['d']}}" u-r="deleteCommentPopup" u-i="38e9e0de-12" bind:__l="__l" u-p="{{ac}}"><uni-popup-dialog wx:if="{{aa}}" bindconfirm="{{Y}}" bindclose="{{Z}}" u-i="38e9e0de-13,38e9e0de-12" bind:__l="__l" u-p="{{aa}}"></uni-popup-dialog></uni-popup></view>