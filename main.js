import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'
import syncManager from './utils/syncManager.js'
// 确保主包引用 comment store（消除编译警告）
import { useCommentStore } from './store/comment.js'

export function createApp() {
  const app = createSSRApp(App)
  const pinia = createPinia()

  app.use(pinia)

  // 🚀 添加全局错误处理，拦截版本冲突弹窗
  app.config.errorHandler = (err, instance, info) => {
    // 检查是否是版本冲突相关错误
    if (err && err.message && (
      err.message.includes('数据版本冲突') ||
      err.message.includes('版本冲突') ||
      err.message.includes('数据冲突') ||
      err.message.includes('version conflict') ||
      err.message.includes('conflict')
    )) {
      console.log('[Global Error Handler] 静默处理版本冲突错误:', err.message)
      return // 静默处理，不显示弹窗
    }

    // 其他错误正常处理
    console.error('[Global Error Handler] 应用错误:', err, info)
  }
  
  // 延迟初始化同步管理器，确保 uni 对象完全准备好
  setTimeout(async () => {
    if (typeof uni !== 'undefined') {
      // 🚀 添加 uni.showModal 拦截器，阻止版本冲突弹窗
      uni.addInterceptor('showModal', {
        invoke(args) {
          // 🔧 详细记录所有弹窗信息，帮助调试
          console.log('[Modal Interceptor] 检测到弹窗:', {
            title: args?.title,
            content: args?.content,
            timestamp: new Date().toISOString()
          })

          // 检查弹窗内容是否包含版本冲突相关信息
          if (args && (args.title || args.content)) {
            const title = args.title || ''
            const content = args.content || ''
            const text = (title + ' ' + content).toLowerCase()

            if (text.includes('数据版本冲突') ||
                text.includes('版本冲突') ||
                text.includes('数据冲突') ||
                text.includes('version conflict') ||
                text.includes('conflict') ||
                text.includes('心愿不存在或无权限操作') ||
                text.includes('心愿不存在') ||
                text.includes('无权限操作') ||
                text.includes('权限') ||
                text.includes('not found') ||
                text.includes('permission')) {
              console.log('[Modal Interceptor] 🚫 拦截多设备冲突弹窗:', { title, content })

              // 🔧 记录调用栈，帮助找到弹窗来源
              console.trace('[Modal Interceptor] 多设备冲突弹窗调用栈:')

              // 🔧 静默处理多设备同步冲突，直接刷新页面
              setTimeout(() => {
                console.log('[Modal Interceptor] 静默刷新页面以同步最新数据')
                // 触发页面数据刷新
                uni.$emit('silent-refresh-data', {
                  reason: 'multi_device_conflict',
                  timestamp: new Date().toISOString()
                })
              }, 100)

              return false // 阻止弹窗显示
            }
          }
          return args // 允许其他弹窗正常显示
        }
      })

      // 🚀 添加 uni.showToast 拦截器，阻止多设备冲突提示
      uni.addInterceptor('showToast', {
        invoke(args) {
          // 检查提示内容是否包含多设备冲突相关信息
          if (args && args.title) {
            const title = args.title.toLowerCase()

            if (title.includes('心愿不存在或无权限操作') ||
                title.includes('心愿不存在') ||
                title.includes('无权限操作') ||
                title.includes('权限') ||
                title.includes('not found') ||
                title.includes('permission')) {
              console.log('[Toast Interceptor] 🚫 拦截多设备冲突提示:', args.title)

              // 触发静默刷新
              setTimeout(() => {
                uni.$emit('silent-refresh-data', {
                  reason: 'toast_conflict',
                  timestamp: new Date().toISOString()
                })
              }, 100)

              return false // 阻止提示显示
            }
          }
          return args // 允许其他提示正常显示
        }
      })

      // 🚀 添加 uniCloud 错误拦截（如果存在的话）
      if (typeof uniCloud !== 'undefined') {
        // 拦截 uniCloud 的错误处理
        if (uniCloud.onError) {
          uniCloud.onError((error) => {
            if (error && (error.message || error.errMsg) && (
              (error.message && (
                error.message.includes('数据版本冲突') ||
                error.message.includes('版本冲突') ||
                error.message.includes('数据冲突') ||
                error.message.includes('version conflict') ||
                error.message.includes('conflict') ||
                error.message.includes('心愿不存在或无权限操作') ||
                error.message.includes('心愿不存在') ||
                error.message.includes('无权限操作')
              )) ||
              (error.errMsg && (
                error.errMsg.includes('数据版本冲突') ||
                error.errMsg.includes('版本冲突') ||
                error.errMsg.includes('数据冲突') ||
                error.errMsg.includes('version conflict') ||
                error.errMsg.includes('conflict') ||
                error.errMsg.includes('心愿不存在或无权限操作') ||
                error.errMsg.includes('心愿不存在') ||
                error.errMsg.includes('无权限操作')
              ))
            )) {
              console.log('[uniCloud Error Interceptor] 拦截多设备冲突错误:', error.message || error.errMsg)

              // 触发静默刷新
              setTimeout(() => {
                uni.$emit('silent-refresh-data', {
                  reason: 'unicloud_conflict',
                  timestamp: new Date().toISOString()
                })
              }, 100)

              return false // 阻止默认错误处理
            }
            return true // 允许其他错误正常处理
          })
        }

        // 🔧 重写 uniCloud.callFunction 以拦截错误
        const originalCallFunction = uniCloud.callFunction
        if (originalCallFunction) {
          uniCloud.callFunction = function(options) {
            const originalFail = options.fail
            const originalComplete = options.complete

            options.fail = function(error) {
              // 检查是否是多设备冲突错误
              if (error && (error.message || error.errMsg) && (
                (error.message && (
                  error.message.includes('心愿不存在或无权限操作') ||
                  error.message.includes('心愿不存在') ||
                  error.message.includes('无权限操作')
                )) ||
                (error.errMsg && (
                  error.errMsg.includes('心愿不存在或无权限操作') ||
                  error.errMsg.includes('心愿不存在') ||
                  error.errMsg.includes('无权限操作')
                ))
              )) {
                console.log('[uniCloud.callFunction Interceptor] 拦截多设备冲突错误:', error.message || error.errMsg)

                // 触发静默刷新
                setTimeout(() => {
                  uni.$emit('silent-refresh-data', {
                    reason: 'callfunction_conflict',
                    timestamp: new Date().toISOString()
                  })
                }, 100)

                // 不调用原始的 fail 回调，静默处理
                return
              }

              // 其他错误正常处理
              if (originalFail) {
                originalFail(error)
              }
            }

            return originalCallFunction.call(this, options)
          }
        }
      }

      try {
        await syncManager.init()
        console.log('[main.js] 🚀 实时推送同步系统启动成功')
      } catch (error) {
        console.error('[main.js] 同步管理器初始化失败:', error)
      }
    } else {
      console.warn('[main.js] uni 对象不可用，跳过同步管理器初始化')
    }
  }, 100) // 延迟100ms初始化
  
  return {
    app
  }
}
// #endif

// 初始化uniCloud
// 如果项目只有一个服务空间，可以不用传参数，会自动使用默认的服务空间
if (typeof uniCloud !== 'undefined') {
  try {
    console.log('[main.js] uniCloud 对象存在，开始初始化...');

    // 检查是否有多个服务空间配置
    const spaces = uniCloud.getSpaces && uniCloud.getSpaces();
    console.log('[main.js] 服务空间配置:', spaces);

    if (spaces && spaces.length > 1) {
      // 如果有多个服务空间，需要指定使用哪个
      // 这里使用第一个服务空间，你可以根据需要调整
      console.log('[main.js] 检测到多个服务空间，使用第一个:', spaces[0]);
      uniCloud.init({
        provider: spaces[0].provider,
        spaceId: spaces[0].spaceId,
        clientSecret: spaces[0].clientSecret
      });
    } else if (spaces && spaces.length === 1) {
      console.log('[main.js] 检测到单个服务空间，自动使用:', spaces[0]);
    } else {
      console.warn('[main.js] 未检测到服务空间配置');
    }

    console.log('[main.js] ✅ uniCloud initialized successfully');
  } catch (error) {
    console.error('[main.js] ❌ uniCloud initialization failed:', error);
  }
} else {
  console.error('[main.js] ❌ uniCloud 对象不存在');
}