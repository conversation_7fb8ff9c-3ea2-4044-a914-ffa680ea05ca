import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'
import syncManager from './utils/syncManager.js'
// 确保主包引用 comment store（消除编译警告）
import { useCommentStore } from './store/comment.js'

export function createApp() {
  const app = createSSRApp(App)
  const pinia = createPinia()
  
  app.use(pinia)
  
  // 延迟初始化同步管理器，确保 uni 对象完全准备好
  setTimeout(async () => {
    if (typeof uni !== 'undefined') {
      try {
        await syncManager.init()
        console.log('[main.js] 🚀 实时推送同步系统启动成功')
      } catch (error) {
        console.error('[main.js] 同步管理器初始化失败:', error)
      }
    } else {
      console.warn('[main.js] uni 对象不可用，跳过同步管理器初始化')
    }
  }, 100) // 延迟100ms初始化
  
  return {
    app
  }
}
// #endif

// 初始化uniCloud
// 如果项目只有一个服务空间，可以不用传参数，会自动使用默认的服务空间
if (typeof uniCloud !== 'undefined') {
  try {
    console.log('[main.js] uniCloud 对象存在，开始初始化...');

    // 检查是否有多个服务空间配置
    const spaces = uniCloud.getSpaces && uniCloud.getSpaces();
    console.log('[main.js] 服务空间配置:', spaces);

    if (spaces && spaces.length > 1) {
      // 如果有多个服务空间，需要指定使用哪个
      // 这里使用第一个服务空间，你可以根据需要调整
      console.log('[main.js] 检测到多个服务空间，使用第一个:', spaces[0]);
      uniCloud.init({
        provider: spaces[0].provider,
        spaceId: spaces[0].spaceId,
        clientSecret: spaces[0].clientSecret
      });
    } else if (spaces && spaces.length === 1) {
      console.log('[main.js] 检测到单个服务空间，自动使用:', spaces[0]);
    } else {
      console.warn('[main.js] 未检测到服务空间配置');
    }

    console.log('[main.js] ✅ uniCloud initialized successfully');
  } catch (error) {
    console.error('[main.js] ❌ uniCloud initialization failed:', error);
  }
} else {
  console.error('[main.js] ❌ uniCloud 对象不存在');
}