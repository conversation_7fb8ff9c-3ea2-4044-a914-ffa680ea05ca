{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\n\timport { useUserStore } from './store/user.js'\n\timport { useWishStore } from './store/wish.js'\n\timport { useGroupStore } from './store/group.js'\n\timport { useMessageStore } from './store/message.js'\n\timport { useCommentStore, initCommentStore } from './store/comment.js'  // 确保主包使用 comment store\n\timport { authService } from './services/authService.js'\n\timport syncManager from './utils/syncManager.js'\n\t\n\texport default {\n\t\tasync onLaunch() {\n\t\t\tconsole.log('App Launch')\n\t\t\t\n\t\t\t// 0. 初始化API请求拦截器 (应该在任何API调用之前)\n\t\t\tauthService.setupRequestInterceptor()\n\t\t\t\n\t\t\tconst userStore = useUserStore()\n\t\t\tconst wishStore = useWishStore()\n\t\t\tconst groupStore = useGroupStore()\n\t\t\tconst messageStore = useMessageStore()\n\n\t\t\t// 确保主包使用 comment store（消除编译警告）\n\t\t\tconst commentStoreRef = useCommentStore\n\n\t\t\t// 初始化 comment store（延迟初始化，确保 Pinia 已准备好）\n\t\t\tconst commentStore = initCommentStore()\n\t\t\t\n\t\t\t// 1. 应用启动时尝试从本地存储加载并验证用户状态\n\t\t\tawait userStore.loadUserFromStorage()\n\t\t\tconsole.log('[App.vue onLaunch] Initial login status after server verification: ', userStore.isLogin)\n\t\t\t\n\t\t\t// 2. 如果用户已登录，初始化 uni-push 2.0 多设备同步系统\n\t\t\tif (userStore.isLogin) {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('[App.vue onLaunch] 🚀 Initializing uni-push 2.0 sync system...')\n\n\t\t\t\t\t// 初始化同步管理器（基于 uni-push 2.0）\n\t\t\t\t\tawait syncManager.init()\n\t\t\t\t\tconsole.log('[App.vue onLaunch] ✅ uni-push 2.0 sync system initialized successfully')\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('[App.vue onLaunch] ❌ uni-push 2.0 sync system initialization failed:', error)\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 设置 uni-push 消息监听\n\t\t\tconsole.log('[App.vue] 设置 uni-push 消息监听')\n\t\t\tuni.onPushMessage((res) => {\n\t\t\t\tconsole.log('[App.vue] 收到推送消息:', res)\n\t\t\t\t// 将消息传递给同步管理器处理\n\t\t\t\tif (syncManager && syncManager.handlePushMessage) {\n\t\t\t\t\tsyncManager.handlePushMessage(res)\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t// 3. 在背景初始化数据库集合（非阻塞）\n\t\t\tPromise.resolve().then(async () => {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('[App.vue onLaunch] Initializing database collections in background...')\n\t\t\t\t\tconst commentCenter = uniCloud.importObject('comment-center')\n\t\t\t\t\tconst initResult = await commentCenter.ensureCollectionsExist()\n\t\t\t\t\tconsole.log('[App.vue onLaunch] Database collections initialized:', initResult)\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.warn('[App.vue onLaunch] Database initialization failed (non-critical):', error)\n\t\t\t\t}\n\t\t\t})\n\t\t\t\n\t\t\t// 注意：不在这里初始化stores，交给loginSuccess或页面首次进入时处理\n\t\t\t// 这样避免和loginSuccess中的重新初始化冲突\n\t\t\tconsole.log('[App.vue onLaunch] User login status checked. Store initialization will be handled by pages or loginSuccess.')\n\t\t\t\n\t\t\t// 3. 导航拦截器调整\n\t\t\t// 定义那些整个页面都需要登录才能访问的页面 (如果为空，则所有页面级权限都由按需检查处理)\n\t\t\tconst strictlyAuthRequiredPages = [\n\t\t\t\t'/pages/profile/profile', // 用户中心主页\n\t\t\t\t'/pages/message/message', // 消息中心\n\t\t\t\t'/subpkg-profile/pages/addressManage/addressManage', // 地址管理\n\t\t\t\t'/subpkg-profile/pages/addressEdit/addressEdit', // 地址编辑\n\t\t\t\t'/subpkg-profile/pages/historyWish/historyWish', // 历史心愿\n\t\t\t\t// 添加其他必须先登录才能进入的页面\n\t\t\t]\n\t\t\t\n\t\t\tconst handleNavigation = (args, navigationType) => {\n\t\t\t\tconst targetUrl = args.url.split('?')[0]\n\t\t\t\tlet absoluteTargetUrl = targetUrl.startsWith('/') ? targetUrl : `/${targetUrl}`\n\t\t\t\t// 对于tabbar页面，uni.switchTab的args.url已经是绝对路径了\n\t\t\t\tif (navigationType === 'switchTab') {\n\t\t\t\t\tabsoluteTargetUrl = args.url\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log(`[App NavInterceptor (${navigationType})] Attempting to navigate to: ${absoluteTargetUrl}`)\n\t\t\t\t\n\t\t\t\tif (strictlyAuthRequiredPages.includes(absoluteTargetUrl)) {\n\t\t\t\t\t// 对于严格要求登录的页面，直接调用 authService.ensureAuthenticated\n\t\t\t\t\t// 注意：拦截器内调用异步的 ensureAuthenticated 可能会复杂化返回值处理\n\t\t\t\t\t// 简单处理：如果未登录，ensureAuthenticated内部会导航到登录页，并应返回false给拦截器\n\t\t\t\t\t// 但ensureAuthenticated本身是异步的，拦截器invoke是同步期望返回值的\n\t\t\t\t\t\n\t\t\t\t\t// 方案1: 拦截器内只检查登录状态，不调用完整的ensureAuthenticated弹窗，直接跳转\n\t\t\t\t\tif (!userStore.checkLoginStatus()) {\n\t\t\t\t\t\tconsole.log(`[App NavInterceptor (${navigationType})] Strictly required page ${absoluteTargetUrl} and user not logged in. Redirecting.`)\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/login/login?redirect=' + encodeURIComponent(args.url)\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn false // 阻止当前导航\n\t\t\t\t\t}\n\t\t\t\t\t// 方案2: (更复杂，不推荐直接在拦截器invoke里这么做，除非你的ensureAuthenticated能同步返回状态或你改造拦截器)\n\t\t\t\t\t// const isAuthenticated = await authService.ensureAuthenticated({ redirectPath: args.url, promptMessage: '访问此页面需要登录' });\n\t\t\t\t\t// return isAuthenticated;\n\t\t\t\t}\n\t\t\t\t// 对于非严格要求的页面，由页面内部的 authService.ensureAuthenticated 按需处理\n\t\t\t\treturn true // 允许导航，后续由页面内逻辑按需判断\n\t\t\t}\n\t\t\t\n\t\t\tuni.addInterceptor('navigateTo', {\n\t\t\t\tinvoke(args) {\n\t\t\t\t\treturn handleNavigation(args, 'navigateTo')\n\t\t\t\t},\n\t\t\t\tfail(err) {\n\t\t\t\t\tconsole.error('[Interceptor navigateTo fail]:', err.errMsg)\n\t\t\t\t}\n\t\t\t})\n\t\t\t\n\t\t\tuni.addInterceptor('redirectTo', {\n\t\t\t\tinvoke(args) {\n\t\t\t\t\treturn handleNavigation(args, 'redirectTo')\n\t\t\t\t},\n\t\t\t\tfail(err) {\n\t\t\t\t\tconsole.error('[Interceptor redirectTo fail]:', err.errMsg)\n\t\t\t\t}\n\t\t\t})\n\t\t\t\n\t\t\tuni.addInterceptor('reLaunch', {\n\t\t\t\tinvoke(args) {\n\t\t\t\t\treturn handleNavigation(args, 'reLaunch')\n\t\t\t\t},\n\t\t\t\tfail(err) {\n\t\t\t\t\tconsole.error('[Interceptor reLaunch fail]:', err.errMsg)\n\t\t\t\t}\n\t\t\t})\n\t\t\t\n\t\t\tuni.addInterceptor('switchTab', {\n\t\t\t\tasync invoke(args) { // switchTab可以稍微特殊处理，因为它可能需要更强的即时性\n\t\t\t\t\tconst userStoreInstance = useUserStore() // 获取最新实例\n\t\t\t\t\t// 对于 tabBar 页面，在切换前可以尝试轻量级刷新一次登录状态，但不是必须，看产品需求\n\t\t\t\t\t// await userStoreInstance.loadUserFromStorage(); // 如果希望每次切换都强校验\n\t\t\t\t\t\n\t\t\t\t\treturn handleNavigation(args, 'switchTab')\n\t\t\t\t},\n\t\t\t\tfail(err) {\n\t\t\t\t\tconsole.error('[Interceptor switchTab fail]:', err.errMsg)\n\t\t\t\t}\n\t\t\t})\n\t\t\t\n\t\t\tconsole.log('[App.vue onLaunch] App launch sequence finished.')\n\t\t},\n\n\n\t\tonShow: function() {\n\t\t\tconsole.log('App Show')\n\t\t\t// 通知同步管理器应用前台显示\n\t\t\tif (syncManager && syncManager.onAppShow) {\n\t\t\t\tsyncManager.onAppShow()\n\t\t\t}\n\t\t},\n\t\tonHide: function() {\n\t\t\tconsole.log('App Hide')\n\t\t\t// 通知同步管理器应用进入后台\n\t\t\tif (syncManager && syncManager.onAppHide) {\n\t\t\t\tsyncManager.onAppHide()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/*每个页面公共css */\n\t/* 引入字体图标 */\n\t@import './static/css/iconfont.css';\n\t\n\t/* 全局样式 */\n\tpage {\n\t\tbackground-color: #f5f5f5;\n\t\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n\t\tcolor: #333;\n\t}\n\t\n\t/* 卡片样式 */\n\t.card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 12rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n\t\tpadding: 24rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t/* 毛玻璃效果 */\n\t.glass {\n\t\tbackground: rgba(255, 255, 255, 0.7);\n\t\tbackdrop-filter: blur(10px);\n\t\t-webkit-backdrop-filter: blur(10px);\n\t}\n\t\n\t/* 主题颜色 */\n\t.primary-bg {\n\t\tbackground-color: #8a2be2; /* 紫色，适合心愿主题 */\n\t}\n\t\n\t.primary-text {\n\t\tcolor: #8a2be2;\n\t}\n\t\n\t.secondary-bg {\n\t\tbackground-color: #f0e6ff;\n\t}\n\t\n\t/* 文本溢出显示省略号 */\n\t.text-ellipsis {\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n</style>\n", "import App from './App'\r\n\r\n// #ifndef VUE3\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n  ...App\r\n})\r\napp.$mount()\r\n// #endif\r\n\r\n// #ifdef VUE3\r\nimport { createSSRApp } from 'vue'\r\nimport { createPinia } from 'pinia'\r\nimport syncManager from './utils/syncManager.js'\r\n// 确保主包引用 comment store（消除编译警告）\r\nimport { useCommentStore } from './store/comment.js'\r\n\r\nexport function createApp() {\r\n  const app = createSSRApp(App)\r\n  const pinia = createPinia()\r\n\r\n  app.use(pinia)\r\n\r\n  // 🚀 添加全局错误处理，拦截版本冲突弹窗\r\n  app.config.errorHandler = (err, instance, info) => {\r\n    // 检查是否是版本冲突相关错误\r\n    if (err && err.message && (\r\n      err.message.includes('数据版本冲突') ||\r\n      err.message.includes('版本冲突') ||\r\n      err.message.includes('数据冲突') ||\r\n      err.message.includes('version conflict') ||\r\n      err.message.includes('conflict')\r\n    )) {\r\n      console.log('[Global Error Handler] 静默处理版本冲突错误:', err.message)\r\n      return // 静默处理，不显示弹窗\r\n    }\r\n\r\n    // 其他错误正常处理\r\n    console.error('[Global Error Handler] 应用错误:', err, info)\r\n  }\r\n  \r\n  // 延迟初始化同步管理器，确保 uni 对象完全准备好\r\n  setTimeout(async () => {\r\n    if (typeof uni !== 'undefined') {\r\n      // 🚀 添加 uni.showModal 拦截器，阻止版本冲突弹窗\r\n      uni.addInterceptor('showModal', {\r\n        invoke(args) {\r\n          // 检查弹窗内容是否包含版本冲突相关信息\r\n          if (args && (args.title || args.content)) {\r\n            const title = args.title || ''\r\n            const content = args.content || ''\r\n            const text = (title + ' ' + content).toLowerCase()\r\n\r\n            if (text.includes('数据版本冲突') ||\r\n                text.includes('版本冲突') ||\r\n                text.includes('数据冲突') ||\r\n                text.includes('version conflict') ||\r\n                text.includes('conflict')) {\r\n              console.log('[Modal Interceptor] 拦截版本冲突弹窗:', { title, content })\r\n              return false // 阻止弹窗显示\r\n            }\r\n          }\r\n          return args // 允许其他弹窗正常显示\r\n        }\r\n      })\r\n\r\n      // 🚀 添加 uniCloud 错误拦截（如果存在的话）\r\n      if (typeof uniCloud !== 'undefined' && uniCloud.onError) {\r\n        uniCloud.onError((error) => {\r\n          if (error && error.message && (\r\n            error.message.includes('数据版本冲突') ||\r\n            error.message.includes('版本冲突') ||\r\n            error.message.includes('数据冲突') ||\r\n            error.message.includes('version conflict') ||\r\n            error.message.includes('conflict')\r\n          )) {\r\n            console.log('[uniCloud Error Interceptor] 拦截版本冲突错误:', error.message)\r\n            return false // 阻止默认错误处理\r\n          }\r\n          return true // 允许其他错误正常处理\r\n        })\r\n      }\r\n\r\n      try {\r\n        await syncManager.init()\r\n        console.log('[main.js] 🚀 实时推送同步系统启动成功')\r\n      } catch (error) {\r\n        console.error('[main.js] 同步管理器初始化失败:', error)\r\n      }\r\n    } else {\r\n      console.warn('[main.js] uni 对象不可用，跳过同步管理器初始化')\r\n    }\r\n  }, 100) // 延迟100ms初始化\r\n  \r\n  return {\r\n    app\r\n  }\r\n}\r\n// #endif\r\n\r\n// 初始化uniCloud\r\n// 如果项目只有一个服务空间，可以不用传参数，会自动使用默认的服务空间\r\nif (typeof uniCloud !== 'undefined') {\r\n  try {\r\n    console.log('[main.js] uniCloud 对象存在，开始初始化...');\r\n\r\n    // 检查是否有多个服务空间配置\r\n    const spaces = uniCloud.getSpaces && uniCloud.getSpaces();\r\n    console.log('[main.js] 服务空间配置:', spaces);\r\n\r\n    if (spaces && spaces.length > 1) {\r\n      // 如果有多个服务空间，需要指定使用哪个\r\n      // 这里使用第一个服务空间，你可以根据需要调整\r\n      console.log('[main.js] 检测到多个服务空间，使用第一个:', spaces[0]);\r\n      uniCloud.init({\r\n        provider: spaces[0].provider,\r\n        spaceId: spaces[0].spaceId,\r\n        clientSecret: spaces[0].clientSecret\r\n      });\r\n    } else if (spaces && spaces.length === 1) {\r\n      console.log('[main.js] 检测到单个服务空间，自动使用:', spaces[0]);\r\n    } else {\r\n      console.warn('[main.js] 未检测到服务空间配置');\r\n    }\r\n\r\n    console.log('[main.js] ✅ uniCloud initialized successfully');\r\n  } catch (error) {\r\n    console.error('[main.js] ❌ uniCloud initialization failed:', error);\r\n  }\r\n} else {\r\n  console.error('[main.js] ❌ uniCloud 对象不存在');\r\n}"], "names": ["uni", "authService", "useUserStore", "useWishStore", "useGroupStore", "useMessageStore", "initCommentStore", "syncManager", "uniCloud", "createSSRApp", "App", "createPinia"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AASC,MAAK,YAAU;AAAA,EACd,MAAM,WAAW;AAChBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,YAAY;AAGxBC,yBAAAA,YAAY,wBAAwB;AAEpC,UAAM,YAAYC,WAAAA,aAAa;AACbC,4BAAa;AACZC,8BAAc;AACZC,kCAAgB;AAMhBC,mCAAiB;AAGtC,UAAM,UAAU,oBAAoB;AACpCN,kBAAA,MAAA,MAAA,OAAA,iBAAY,uEAAuE,UAAU,OAAO;AAGpG,QAAI,UAAU,SAAS;AACtB,UAAI;AACHA,sBAAAA,oCAAY,gEAAgE;AAG5E,cAAMO,kBAAAA,YAAY,KAAK;AACvBP,sBAAAA,MAAA,MAAA,OAAA,iBAAY,wEAAwE;AAAA,MAEnF,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,iBAAc,wEAAwE,KAAK;AAAA,MAC5F;AAAA,IACD;AAGAA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,4BAA4B;AACxCA,wBAAI,cAAc,CAAC,QAAQ;AAC1BA,oBAAAA,MAAA,MAAA,OAAA,iBAAY,qBAAqB,GAAG;AAEpC,UAAIO,kBAAU,eAAKA,kBAAW,YAAC,mBAAmB;AACjDA,0BAAW,YAAC,kBAAkB,GAAG;AAAA,MAClC;AAAA,KACA;AAGD,YAAQ,UAAU,KAAK,YAAY;AAClC,UAAI;AACHP,sBAAAA,MAAA,MAAA,OAAA,iBAAY,uEAAuE;AACnF,cAAM,gBAAgBQ,cAAAA,GAAS,aAAa,gBAAgB;AAC5D,cAAM,aAAa,MAAM,cAAc,uBAAuB;AAC9DR,sBAAAA,oCAAY,wDAAwD,UAAU;AAAA,MAC7E,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,QAAA,iBAAa,qEAAqE,KAAK;AAAA,MACxF;AAAA,KACA;AAIDA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,8GAA8G;AAI1H,UAAM,4BAA4B;AAAA,MACjC;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA;AAAA,IAED;AAEA,UAAM,mBAAmB,CAAC,MAAM,mBAAmB;AAClD,YAAM,YAAY,KAAK,IAAI,MAAM,GAAG,EAAE,CAAC;AACvC,UAAI,oBAAoB,UAAU,WAAW,GAAG,IAAI,YAAY,IAAI,SAAS;AAE7E,UAAI,mBAAmB,aAAa;AACnC,4BAAoB,KAAK;AAAA,MAC1B;AAEAA,0BAAA,MAAA,OAAA,iBAAY,wBAAwB,cAAc,iCAAiC,iBAAiB,EAAE;AAEtG,UAAI,0BAA0B,SAAS,iBAAiB,GAAG;AAO1D,YAAI,CAAC,UAAU,oBAAoB;AAClCA,8BAAA,MAAA,OAAA,kBAAY,wBAAwB,cAAc,6BAA6B,iBAAiB,uCAAuC;AACvIA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,iCAAiC,mBAAmB,KAAK,GAAG;AAAA,WACjE;AACD,iBAAO;AAAA,QACR;AAAA,MAID;AAEA,aAAO;AAAA,IACR;AAEAA,kBAAG,MAAC,eAAe,cAAc;AAAA,MAChC,OAAO,MAAM;AACZ,eAAO,iBAAiB,MAAM,YAAY;AAAA,MAC1C;AAAA,MACD,KAAK,KAAK;AACTA,6DAAc,kCAAkC,IAAI,MAAM;AAAA,MAC3D;AAAA,KACA;AAEDA,kBAAG,MAAC,eAAe,cAAc;AAAA,MAChC,OAAO,MAAM;AACZ,eAAO,iBAAiB,MAAM,YAAY;AAAA,MAC1C;AAAA,MACD,KAAK,KAAK;AACTA,6DAAc,kCAAkC,IAAI,MAAM;AAAA,MAC3D;AAAA,KACA;AAEDA,kBAAG,MAAC,eAAe,YAAY;AAAA,MAC9B,OAAO,MAAM;AACZ,eAAO,iBAAiB,MAAM,UAAU;AAAA,MACxC;AAAA,MACD,KAAK,KAAK;AACTA,6DAAc,gCAAgC,IAAI,MAAM;AAAA,MACzD;AAAA,KACA;AAEDA,kBAAG,MAAC,eAAe,aAAa;AAAA,MAC/B,MAAM,OAAO,MAAM;AACQE;AAI1B,eAAO,iBAAiB,MAAM,WAAW;AAAA,MACzC;AAAA,MACD,KAAK,KAAK;AACTF,6DAAc,iCAAiC,IAAI,MAAM;AAAA,MAC1D;AAAA,KACA;AAEDA,kBAAAA,MAAA,MAAA,OAAA,kBAAY,kDAAkD;AAAA,EAC9D;AAAA,EAGD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,kBAAA,UAAU;AAEtB,QAAIO,kBAAU,eAAKA,kBAAW,YAAC,WAAW;AACzCA,wBAAAA,YAAY,UAAU;AAAA,IACvB;AAAA,EACA;AAAA,EACD,QAAQ,WAAW;AAClBP,kBAAAA,MAAY,MAAA,OAAA,kBAAA,UAAU;AAEtB,QAAIO,kBAAU,eAAKA,kBAAW,YAAC,WAAW;AACzCA,wBAAAA,YAAY,UAAU;AAAA,IACvB;AAAA,EACD;AACD;ACxJM,SAAS,YAAY;AAC1B,QAAM,MAAME,cAAY,aAACC,SAAG;AAC5B,QAAM,QAAQC,cAAAA,YAAa;AAE3B,MAAI,IAAI,KAAK;AAGb,MAAI,OAAO,eAAe,CAAC,KAAK,UAAU,SAAS;AAEjD,QAAI,OAAO,IAAI,YACb,IAAI,QAAQ,SAAS,QAAQ,KAC7B,IAAI,QAAQ,SAAS,MAAM,KAC3B,IAAI,QAAQ,SAAS,MAAM,KAC3B,IAAI,QAAQ,SAAS,kBAAkB,KACvC,IAAI,QAAQ,SAAS,UAAU,IAC9B;AACDX,oBAAY,MAAA,MAAA,OAAA,iBAAA,sCAAsC,IAAI,OAAO;AAC7D;AAAA,IACD;AAGDA,kBAAA,MAAA,MAAA,SAAA,iBAAc,gCAAgC,KAAK,IAAI;AAAA,EACxD;AAGD,aAAW,YAAY;AACrB,QAAI,OAAOA,cAAG,UAAK,aAAa;AAE9BA,oBAAG,MAAC,eAAe,aAAa;AAAA,QAC9B,OAAO,MAAM;AAEX,cAAI,SAAS,KAAK,SAAS,KAAK,UAAU;AACxC,kBAAM,QAAQ,KAAK,SAAS;AAC5B,kBAAM,UAAU,KAAK,WAAW;AAChC,kBAAM,QAAQ,QAAQ,MAAM,SAAS,YAAa;AAElD,gBAAI,KAAK,SAAS,QAAQ,KACtB,KAAK,SAAS,MAAM,KACpB,KAAK,SAAS,MAAM,KACpB,KAAK,SAAS,kBAAkB,KAChC,KAAK,SAAS,UAAU,GAAG;AAC7BA,4BAAY,MAAA,MAAA,OAAA,iBAAA,iCAAiC,EAAE,OAAO,SAAS;AAC/D,qBAAO;AAAA,YACR;AAAA,UACF;AACD,iBAAO;AAAA,QACR;AAAA,MACT,CAAO;AAGD,UAAI,OAAOQ,cAAQ,OAAK,eAAeA,cAAAA,GAAS,SAAS;AACvDA,yBAAS,QAAQ,CAAC,UAAU;AAC1B,cAAI,SAAS,MAAM,YACjB,MAAM,QAAQ,SAAS,QAAQ,KAC/B,MAAM,QAAQ,SAAS,MAAM,KAC7B,MAAM,QAAQ,SAAS,MAAM,KAC7B,MAAM,QAAQ,SAAS,kBAAkB,KACzC,MAAM,QAAQ,SAAS,UAAU,IAChC;AACDR,0BAAY,MAAA,MAAA,OAAA,iBAAA,0CAA0C,MAAM,OAAO;AACnE,mBAAO;AAAA,UACR;AACD,iBAAO;AAAA,QACjB,CAAS;AAAA,MACF;AAED,UAAI;AACF,cAAMO,kBAAAA,YAAY,KAAM;AACxBP,sBAAAA,MAAA,MAAA,OAAA,iBAAY,2BAA2B;AAAA,MACxC,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,iBAAc,yBAAyB,KAAK;AAAA,MAC7C;AAAA,IACP,OAAW;AACLA,oBAAAA,qCAAa,gCAAgC;AAAA,IAC9C;AAAA,EACF,GAAE,GAAG;AAEN,SAAO;AAAA,IACL;AAAA,EACD;AACH;AAKA,IAAI,OAAOQ,cAAAA,OAAa,aAAa;AACnC,MAAI;AACFR,kBAAAA,qCAAY,kCAAkC;AAG9C,UAAM,SAASQ,cAAQ,GAAC,aAAaA,cAAQ,GAAC,UAAS;AACvDR,kBAAA,MAAA,MAAA,OAAA,kBAAY,qBAAqB,MAAM;AAEvC,QAAI,UAAU,OAAO,SAAS,GAAG;AAG/BA,oBAAY,MAAA,MAAA,OAAA,kBAAA,8BAA8B,OAAO,CAAC,CAAC;AACnDQ,oBAAAA,GAAS,KAAK;AAAA,QACZ,UAAU,OAAO,CAAC,EAAE;AAAA,QACpB,SAAS,OAAO,CAAC,EAAE;AAAA,QACnB,cAAc,OAAO,CAAC,EAAE;AAAA,MAChC,CAAO;AAAA,IACF,WAAU,UAAU,OAAO,WAAW,GAAG;AACxCR,oBAAY,MAAA,MAAA,OAAA,kBAAA,6BAA6B,OAAO,CAAC,CAAC;AAAA,IACxD,OAAW;AACLA,oBAAAA,MAAA,MAAA,QAAA,kBAAa,sBAAsB;AAAA,IACpC;AAEDA,kBAAAA,MAAY,MAAA,OAAA,kBAAA,+CAA+C;AAAA,EAC5D,SAAQ,OAAO;AACdA,kBAAc,MAAA,MAAA,SAAA,kBAAA,+CAA+C,KAAK;AAAA,EACnE;AACH,OAAO;AACLA,gBAAAA,MAAA,MAAA,SAAA,kBAAc,4BAA4B;AAC5C;;;"}