// uniCloud 连接测试脚本
// 在微信开发者工具的控制台中运行此代码来测试连接

console.log('=== uniCloud 连接测试开始 ===');

// 1. 检查 uniCloud 是否可用
if (typeof uniCloud === 'undefined') {
  console.error('❌ uniCloud 未定义，请检查是否正确引入');
} else {
  console.log('✅ uniCloud 对象存在');
}

// 2. 检查服务空间配置
try {
  const spaces = uniCloud.getSpaces && uniCloud.getSpaces();
  console.log('📋 服务空间配置:', spaces);
} catch (error) {
  console.error('❌ 获取服务空间配置失败:', error);
}

// 3. 测试简单的云函数调用
async function testCloudFunction() {
  try {
    console.log('🚀 开始测试云函数调用...');
    
    // 测试调用一个简单的云函数
    const result = await uniCloud.callFunction({
      name: 'wish-center',
      data: {
        action: 'test'
      }
    });
    
    console.log('✅ 云函数调用成功:', result);
  } catch (error) {
    console.error('❌ 云函数调用失败:', error);
    console.error('错误详情:', {
      code: error.code,
      message: error.message,
      requestId: error.requestId
    });
  }
}

// 4. 测试数据库连接
async function testDatabase() {
  try {
    console.log('🗄️ 开始测试数据库连接...');
    
    const db = uniCloud.database();
    const result = await db.collection('uni-id-users').limit(1).get();
    
    console.log('✅ 数据库连接成功:', result);
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
  }
}

// 执行测试
setTimeout(() => {
  testCloudFunction();
  testDatabase();
}, 1000);

console.log('=== 请在控制台查看测试结果 ===');
