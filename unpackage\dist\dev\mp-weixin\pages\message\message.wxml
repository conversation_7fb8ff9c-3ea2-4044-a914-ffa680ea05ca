<view class="message-container"><view class="message-header"><view class="title">消息通知</view><view wx:if="{{a}}" class="read-all" bindtap="{{b}}">全部已读</view></view><view class="message-list"><view wx:if="{{c}}" class="empty-list"><text class="empty-text">暂无消息</text></view><view wx:else><view wx:for="{{d}}" wx:for-item="message" wx:key="k" class="{{['message-item', 'card', message.l && 'unread', message.m && 'comment-message']}}" bindtap="{{message.n}}"><view wx:if="{{message.a}}" class="message-dot"></view><block wx:if="{{message.b}}"><view class="message-header-row"><image class="comment-avatar" src="{{message.c}}" mode="aspectFill"></image><view class="message-title">{{message.d}}</view></view><view class="message-wish-title">心愿：{{message.e}}</view><view class="message-content comment-content">{{message.f}}</view><view class="message-time">{{message.g}}</view><view class="message-action">点击查看详情</view></block><block wx:else><view class="message-title">{{message.h}}</view><view class="message-content">{{message.i}}</view><view class="message-time">{{message.j}}</view></block></view></view></view></view>