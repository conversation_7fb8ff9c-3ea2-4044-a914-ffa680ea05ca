"use strict";
const common_vendor = require("../../common/vendor.js");
const store_message = require("../../store/message.js");
const store_wish = require("../../store/wish.js");
const _sfc_main = {
  setup() {
    const messageStore = store_message.useMessageStore();
    const wishStore = store_wish.useWishStore();
    return {
      messageStore,
      wishStore
    };
  },
  computed: {
    messages() {
      return this.messageStore.getAllMessages;
    },
    unreadCount() {
      return this.messageStore.getUnreadCount;
    }
  },
  onLoad() {
    this.messageStore.initMessages();
    this.updateTabBarBadge();
  },
  onShow() {
    this.updateTabBarBadge();
  },
  methods: {
    // 更新tabbar徽标
    updateTabBarBadge() {
      const unreadCount = this.unreadCount;
      if (unreadCount > 0) {
        common_vendor.index.setTabBarBadge({
          index: 1,
          // 消息选项卡的索引
          text: unreadCount.toString()
        });
      } else {
        common_vendor.index.removeTabBarBadge({
          index: 1
        });
      }
    },
    // 处理消息点击
    handleMessageClick(message) {
      this.messageStore.markAsRead(message.id);
      if (message.type === "comment" && message.wishId) {
        common_vendor.index.navigateTo({
          url: `/subpkg-wish/pages/wishDetail/wishDetail?id=${message.wishId}`
        });
      }
    },
    // 全部标记为已读
    markAllAsRead() {
      this.messageStore.markAllAsRead();
      common_vendor.index.showToast({
        title: "已全部标记为已读",
        icon: "success"
      });
    },
    // 格式化时间
    formatTime(dateString) {
      const date = new Date(dateString);
      const now = /* @__PURE__ */ new Date();
      const diff = now - date;
      if (diff < 24 * 60 * 60 * 1e3 && date.getDate() === now.getDate()) {
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `今天 ${hours}:${minutes}`;
      }
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      if (date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth() && date.getFullYear() === yesterday.getFullYear()) {
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `昨天 ${hours}:${minutes}`;
      }
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.unreadCount > 0
  }, $options.unreadCount > 0 ? {
    b: common_vendor.o((...args) => $options.markAllAsRead && $options.markAllAsRead(...args))
  } : {}, {
    c: $options.messages.length === 0
  }, $options.messages.length === 0 ? {} : {
    d: common_vendor.f($options.messages, (message, index, i0) => {
      return common_vendor.e({
        a: !message.isRead
      }, !message.isRead ? {} : {}, {
        b: message.type === "comment"
      }, message.type === "comment" ? {
        c: message.commentAvatar || "/static/default-avatar.png",
        d: common_vendor.t(message.title),
        e: common_vendor.t(message.wishTitle || "未知心愿"),
        f: common_vendor.t(message.content),
        g: common_vendor.t($options.formatTime(message.createDate))
      } : {
        h: common_vendor.t(message.title),
        i: common_vendor.t(message.content),
        j: common_vendor.t($options.formatTime(message.createDate))
      }, {
        k: message.id,
        l: !message.isRead ? 1 : "",
        m: message.type === "comment" ? 1 : "",
        n: common_vendor.o(($event) => $options.handleMessageClick(message), message.id)
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/message/message.js.map
