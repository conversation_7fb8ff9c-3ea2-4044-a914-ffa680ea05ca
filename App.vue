<script>
	import { useUserStore } from './store/user.js'
	import { useWishStore } from './store/wish.js'
	import { useGroupStore } from './store/group.js'
	import { useMessageStore } from './store/message.js'
	import { useCommentStore, initCommentStore } from './store/comment.js'  // 确保主包使用 comment store
	import { authService } from './services/authService.js'
	import syncManager from './utils/syncManager.js'
	
	export default {
		async onLaunch() {
			console.log('App Launch')
			
			// 0. 初始化API请求拦截器 (应该在任何API调用之前)
			authService.setupRequestInterceptor()
			
			const userStore = useUserStore()
			const wishStore = useWishStore()
			const groupStore = useGroupStore()
			const messageStore = useMessageStore()

			// 确保主包使用 comment store（消除编译警告）
			const commentStoreRef = useCommentStore

			// 初始化 comment store（延迟初始化，确保 Pinia 已准备好）
			const commentStore = initCommentStore()
			
			// 1. 应用启动时尝试从本地存储加载并验证用户状态
			await userStore.loadUserFromStorage()
			console.log('[App.vue onLaunch] Initial login status after server verification: ', userStore.isLogin)
			
			// 2. 如果用户已登录，初始化 uni-push 2.0 多设备同步系统
			if (userStore.isLogin) {
				try {
					console.log('[App.vue onLaunch] 🚀 Initializing uni-push 2.0 sync system...')

					// 初始化同步管理器（基于 uni-push 2.0）
					await syncManager.init()
					console.log('[App.vue onLaunch] ✅ uni-push 2.0 sync system initialized successfully')

				} catch (error) {
					console.error('[App.vue onLaunch] ❌ uni-push 2.0 sync system initialization failed:', error)
				}
			}

			// 设置 uni-push 消息监听
			console.log('[App.vue] 设置 uni-push 消息监听')
			uni.onPushMessage((res) => {
				console.log('[App.vue] 收到推送消息:', res)
				// 将消息传递给同步管理器处理
				if (syncManager && syncManager.handlePushMessage) {
					syncManager.handlePushMessage(res)
				}
			})

			// 3. 在背景初始化数据库集合（非阻塞）
			Promise.resolve().then(async () => {
				try {
					console.log('[App.vue onLaunch] Initializing database collections in background...')
					const commentCenter = uniCloud.importObject('comment-center')
					const initResult = await commentCenter.ensureCollectionsExist()
					console.log('[App.vue onLaunch] Database collections initialized:', initResult)
				} catch (error) {
					console.warn('[App.vue onLaunch] Database initialization failed (non-critical):', error)
				}
			})
			
			// 注意：不在这里初始化stores，交给loginSuccess或页面首次进入时处理
			// 这样避免和loginSuccess中的重新初始化冲突
			console.log('[App.vue onLaunch] User login status checked. Store initialization will be handled by pages or loginSuccess.')
			
			// 3. 导航拦截器调整
			// 定义那些整个页面都需要登录才能访问的页面 (如果为空，则所有页面级权限都由按需检查处理)
			const strictlyAuthRequiredPages = [
				'/pages/profile/profile', // 用户中心主页
				'/pages/message/message', // 消息中心
				'/subpkg-profile/pages/addressManage/addressManage', // 地址管理
				'/subpkg-profile/pages/addressEdit/addressEdit', // 地址编辑
				'/subpkg-profile/pages/historyWish/historyWish', // 历史心愿
				// 添加其他必须先登录才能进入的页面
			]
			
			const handleNavigation = (args, navigationType) => {
				const targetUrl = args.url.split('?')[0]
				let absoluteTargetUrl = targetUrl.startsWith('/') ? targetUrl : `/${targetUrl}`
				// 对于tabbar页面，uni.switchTab的args.url已经是绝对路径了
				if (navigationType === 'switchTab') {
					absoluteTargetUrl = args.url
				}
				
				console.log(`[App NavInterceptor (${navigationType})] Attempting to navigate to: ${absoluteTargetUrl}`)
				
				if (strictlyAuthRequiredPages.includes(absoluteTargetUrl)) {
					// 对于严格要求登录的页面，直接调用 authService.ensureAuthenticated
					// 注意：拦截器内调用异步的 ensureAuthenticated 可能会复杂化返回值处理
					// 简单处理：如果未登录，ensureAuthenticated内部会导航到登录页，并应返回false给拦截器
					// 但ensureAuthenticated本身是异步的，拦截器invoke是同步期望返回值的
					
					// 方案1: 拦截器内只检查登录状态，不调用完整的ensureAuthenticated弹窗，直接跳转
					if (!userStore.checkLoginStatus()) {
						console.log(`[App NavInterceptor (${navigationType})] Strictly required page ${absoluteTargetUrl} and user not logged in. Redirecting.`)
						uni.navigateTo({
							url: '/pages/login/login?redirect=' + encodeURIComponent(args.url)
						})
						return false // 阻止当前导航
					}
					// 方案2: (更复杂，不推荐直接在拦截器invoke里这么做，除非你的ensureAuthenticated能同步返回状态或你改造拦截器)
					// const isAuthenticated = await authService.ensureAuthenticated({ redirectPath: args.url, promptMessage: '访问此页面需要登录' });
					// return isAuthenticated;
				}
				// 对于非严格要求的页面，由页面内部的 authService.ensureAuthenticated 按需处理
				return true // 允许导航，后续由页面内逻辑按需判断
			}
			
			uni.addInterceptor('navigateTo', {
				invoke(args) {
					return handleNavigation(args, 'navigateTo')
				},
				fail(err) {
					console.error('[Interceptor navigateTo fail]:', err.errMsg)
				}
			})
			
			uni.addInterceptor('redirectTo', {
				invoke(args) {
					return handleNavigation(args, 'redirectTo')
				},
				fail(err) {
					console.error('[Interceptor redirectTo fail]:', err.errMsg)
				}
			})
			
			uni.addInterceptor('reLaunch', {
				invoke(args) {
					return handleNavigation(args, 'reLaunch')
				},
				fail(err) {
					console.error('[Interceptor reLaunch fail]:', err.errMsg)
				}
			})
			
			uni.addInterceptor('switchTab', {
				async invoke(args) { // switchTab可以稍微特殊处理，因为它可能需要更强的即时性
					const userStoreInstance = useUserStore() // 获取最新实例
					// 对于 tabBar 页面，在切换前可以尝试轻量级刷新一次登录状态，但不是必须，看产品需求
					// await userStoreInstance.loadUserFromStorage(); // 如果希望每次切换都强校验
					
					return handleNavigation(args, 'switchTab')
				},
				fail(err) {
					console.error('[Interceptor switchTab fail]:', err.errMsg)
				}
			})
			
			console.log('[App.vue onLaunch] App launch sequence finished.')
		},


		onShow: function() {
			console.log('App Show')
			// 通知同步管理器应用前台显示
			if (syncManager && syncManager.onAppShow) {
				syncManager.onAppShow()
			}
		},
		onHide: function() {
			console.log('App Hide')
			// 通知同步管理器应用进入后台
			if (syncManager && syncManager.onAppHide) {
				syncManager.onAppHide()
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	/* 引入字体图标 */
	@import './static/css/iconfont.css';
	
	/* 全局样式 */
	page {
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
		color: #333;
	}
	
	/* 卡片样式 */
	.card {
		background-color: #ffffff;
		border-radius: 12rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		padding: 24rpx;
		margin-bottom: 20rpx;
	}
	
	/* 毛玻璃效果 */
	.glass {
		background: rgba(255, 255, 255, 0.7);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
	}
	
	/* 主题颜色 */
	.primary-bg {
		background-color: #8a2be2; /* 紫色，适合心愿主题 */
	}
	
	.primary-text {
		color: #8a2be2;
	}
	
	.secondary-bg {
		background-color: #f0e6ff;
	}
	
	/* 文本溢出显示省略号 */
	.text-ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>
