{"version": 3, "file": "group.js", "sources": ["store/group.js"], "sourcesContent": ["/**\n * 分组数据管理 Store\n * \n * 重要说明：已彻底移除所有自动同步功能\n * - 不再有定时同步器\n * - 网络恢复时不会自动同步\n * - 所有同步都需要手动触发\n * \n * 手动同步方法：\n * - manualSync(): 完整同步（用户主动调用）\n * - syncPendingData(): 同步待上传数据\n * \n * 同步状态检查：\n * - hasPendingSync: 是否有待同步数据\n * - pendingSyncCount: 待同步数量\n * - pendingSyncGroups: 待同步分组列表\n */\n\nimport { defineStore } from 'pinia'\nimport { useUserStore } from './user.js' // Import user store\nimport { devLog } from '@/utils/envUtils.js'\n\nexport const useGroupStore = defineStore('group', {\n  state: () => ({\n    groups: [],\n    isLoading: false,\n    lastSyncTime: null,\n    isOnline: true, // 网络状态\n\n    // 🚀 增强同步状态管理\n    _syncOperations: new Set(), // 正在进行的同步操作\n    _lastSyncTime: 0 // 上次同步时间\n  }),\n  \n  getters: {\n    // 获取所有分组\n    getAllGroups: (state) => state.groups,\n    \n    // 根据ID获取分组\n    getGroupById: (state) => (id) => {\n      return state.groups.find(group => group.id === id) || null\n    },\n    \n    // 检查是否有待同步的数据\n    hasPendingSync: (state) => {\n      return state.groups.some(group => group._needSync)\n    },\n    \n    // 获取待同步的分组数量\n    pendingSyncCount: (state) => {\n      return state.groups.filter(group => group._needSync).length\n    },\n    \n    // 获取待同步的分组列表\n    pendingSyncGroups: (state) => {\n      return state.groups.filter(group => group._needSync)\n    }\n  },\n  \n  actions: {\n    // 初始化分组数据\n    async initGroups() {\n      // 尝试从本地存储加载\n      const storedGroups = uni.getStorageSync('groups')\n      if (storedGroups) {\n        const parsed = JSON.parse(storedGroups)\n        // 确保每个分组对象都有 id 字段\n        this.groups = parsed.map(group => ({\n          ...group,\n          id: group.id || group._id // 如果没有 id 字段，使用 _id\n        }))\n      }\n      \n      // 初始化网络监听\n      this.initNetworkMonitor()\n      \n      // 尝试从云端同步数据\n      await this.syncFromCloud()\n      \n      // 确保默认分组存在\n      this.ensureDefaultGroups()\n    },\n    \n    // 初始化网络监听\n    initNetworkMonitor() {\n      // 检查当前网络状态\n      this.checkNetworkStatus()\n      \n      // 监听网络状态变化\n      uni.onNetworkStatusChange((res) => {\n        const wasOnline = this.isOnline\n        this.isOnline = res.isConnected\n        \n        devLog.log(`分组Store - 网络状态变化: ${wasOnline ? '在线' : '离线'} -> ${this.isOnline ? '在线' : '离线'}`)\n        \n        // 网络恢复时只记录状态，不自动同步\n        if (!wasOnline && this.isOnline) {\n          devLog.log('分组Store - 网络恢复，但不启用自动同步')\n        }\n      })\n    },\n    \n    // 检查网络状态\n    async checkNetworkStatus() {\n      try {\n        const networkType = await uni.getNetworkType()\n        this.isOnline = networkType.networkType !== 'none'\n        devLog.log('分组Store - 当前网络状态:', this.isOnline ? '在线' : '离线')\n      } catch (error) {\n        devLog.error('分组Store - 检查网络状态失败:', error)\n        this.isOnline = false\n      }\n    },\n\n    // 🚀 新增：检查同步操作是否可以执行\n    _canStartSync(operation) {\n      const now = Date.now()\n\n      // 检查是否有相同操作正在进行\n      if (this._syncOperations.has(operation)) {\n        devLog.log(`[groupStore] 同步操作 ${operation} 已在进行中`)\n        return false\n      }\n\n      // 检查是否距离上次同步太近（防抖）\n      if (now - this._lastSyncTime < 1000) {\n        devLog.log(`[groupStore] 同步操作过于频繁，跳过`)\n        return false\n      }\n\n      return true\n    },\n\n    // 🚀 新增：开始同步操作\n    _startSyncOperation(operation) {\n      this._syncOperations.add(operation)\n      this._lastSyncTime = Date.now()\n      devLog.log(`[groupStore] 开始同步操作: ${operation}`)\n    },\n\n    // 🚀 新增：结束同步操作\n    _endSyncOperation(operation) {\n      this._syncOperations.delete(operation)\n      devLog.log(`[groupStore] 结束同步操作: ${operation}`)\n    },\n    \n\n    \n    // 从云端同步数据 - 🚀 增强防重复机制\n    async syncFromCloud() {\n      const operation = 'syncFromCloud'\n\n      // 🚀 检查是否可以开始同步\n      if (!this._canStartSync(operation)) {\n        return\n      }\n\n      const userStore = useUserStore() // Get user store instance\n      if (!userStore.isLogin) {\n        devLog.log('[store/group.js syncFromCloud] User not logged in, skipping cloud sync.');\n        // 如果未登录，可以选择是否加载默认数据或清空列表\n        if (this.groups.length === 0) {\n            // this._initDefaultGroups(); // 根据产品需求决定\n        }\n        return; // Do not proceed if not logged in\n      }\n\n      try {\n        this._startSyncOperation(operation)\n        this.isLoading = true\n        devLog.log('[groupStore] Starting intelligent group sync...');\n        \n        // 第一步：轻量级同步检查\n        const syncNeeded = await this._checkGroupSyncNeeded();\n        \n        if (!syncNeeded) {\n          devLog.log('[groupStore] Local group data is up to date, no sync needed');\n          // 确保默认分组存在\n          this.ensureDefaultGroups()\n          return;\n        }\n        \n        // 第二步：如果需要同步，获取完整数据并进行智能合并\n        await this._performIntelligentGroupSync();\n        \n      } catch (error) {\n        devLog.error('[groupStore] 智能分组同步失败:', error)\n        // 如果云端同步失败，确保至少有默认分组\n        devLog.log('[groupStore] Initializing default groups due to sync failure...');\n          this._initDefaultGroups()\n      } finally {\n        this.isLoading = false\n        this._endSyncOperation(operation)\n\n        // 最终安全检查：确保至少有默认分组\n        if (!this.groups || this.groups.length === 0) {\n          devLog.log('[groupStore] Final safety check: no groups found, initializing defaults');\n          this._initDefaultGroups();\n        }\n      }\n    },\n    \n    // 检查分组是否需要同步（轻量级调用）\n    async _checkGroupSyncNeeded() {\n      // 临时特性开关：如果云端API未准备好，直接返回需要同步\n      const enableLightweightCheck = false; // TODO: 等云端API就绪后设为true\n      \n      if (!enableLightweightCheck) {\n        devLog.log('[groupStore] Lightweight group sync check disabled, performing full sync');\n        return true;\n      }\n      \n      try {\n        const groupCenter = uniCloud.importObject('group-center')\n        \n        // 调用轻量级API，只获取同步摘要信息\n        const result = await groupCenter.getGroupSyncSummary({\n          lastSyncTime: this.lastSyncTime,\n          localDataCount: this.groups.filter(g => !g.isDefault).length, // 只计算非默认分组\n          localLastModified: this._getLocalGroupLastModified()\n        })\n        \n        if (result.errCode === 0) {\n          const { needFullSync, cloudLastModified, cloudDataCount } = result.data;\n          \n          // 比较时间戳和数据量来判断是否需要同步\n          const localLastModified = this._getLocalGroupLastModified();\n          const localNonDefaultCount = this.groups.filter(g => !g.isDefault).length;\n          const needSync = needFullSync || \n                          !localLastModified || \n                          !cloudLastModified ||\n                          new Date(cloudLastModified) > new Date(localLastModified) ||\n                          cloudDataCount !== localNonDefaultCount;\n          \n          devLog.log('[groupStore] Group sync check result:', {\n            needSync,\n            localLastModified,\n            cloudLastModified,\n            localNonDefaultCount,\n            cloudCount: cloudDataCount\n          });\n          \n          return needSync;\n        } else {\n          // 如果轻量级检查失败，默认进行同步\n          devLog.warn('[groupStore] Group sync check failed, defaulting to sync');\n          return true;\n        }\n      } catch (error) {\n        devLog.error('[groupStore] Group sync check error:', error);\n        // 网络错误时，不进行同步\n        if (this._isNetworkError(error)) {\n          return false;\n        }\n        // 其他错误时，进行同步\n        return true;\n      }\n    },\n    \n    // 获取本地分组数据的最后修改时间\n    _getLocalGroupLastModified() {\n      const nonDefaultGroups = this.groups.filter(group => !group.isDefault);\n      if (nonDefaultGroups.length === 0) return null;\n      \n      const latestGroup = nonDefaultGroups.reduce((latest, current) => {\n        const currentTime = new Date(current.updateDate || current.createDate || 0);\n        const latestTime = new Date(latest.updateDate || latest.createDate || 0);\n        return currentTime > latestTime ? current : latest;\n      });\n      \n      return latestGroup.updateDate || latestGroup.createDate;\n    },\n    \n    // 执行智能分组同步（合并数据而不是覆盖）\n    async _performIntelligentGroupSync() {\n      devLog.log('[groupStore] Performing intelligent group data merge...');\n      \n      const groupCenter = uniCloud.importObject('group-center')\n      const result = await groupCenter.getGroupList({\n        includeDeleted: true // 包含已删除的数据用于冲突解决\n      })\n      \n      if (result.errCode === 0) {\n        const cloudGroups = result.data || [];\n        devLog.log('[groupStore] Cloud group data received:', cloudGroups.length, 'items');\n        \n        // 执行智能数据合并\n        const mergedData = this._mergeGroupData(this.groups, cloudGroups);\n        \n        // 同步前数据状态记录\n        devLog.log('[groupStore] 开始数据合并:', {\n          localCount: this.groups.length,\n          cloudCount: cloudGroups.length\n        });\n        \n        // 安全更新本地数据，避免DOM错误\n        try {\n          // 使用 nextTick 和温和的更新策略\n          await new Promise((resolve) => {\n            // 如果支持nextTick，使用它\n            if (this.$nextTick) {\n              this.$nextTick(() => {\n        this.groups = mergedData;\n                resolve();\n              });\n            } else {\n              // 降级：延迟更新\n              setTimeout(() => {\n                this.groups = mergedData;\n                resolve();\n              }, 0);\n            }\n          });\n        \n        // 同步后数据状态记录\n          devLog.log('[groupStore] 数据合并完成:', {\n          mergedCount: mergedData.length\n        });\n        \n        this.lastSyncTime = new Date().toISOString()\n        this._saveToStorage()\n        \n        // 确保默认分组存在\n        this.ensureDefaultGroups()\n        \n          devLog.log('[groupStore] Intelligent group sync completed, merged data:', this.groups.length, 'items');\n        } catch (error) {\n          devLog.error('[groupStore] Error updating groups data:', error);\n          // 降级：直接更新\n          this.groups = mergedData;\n          this.lastSyncTime = new Date().toISOString()\n          this._saveToStorage()\n          this.ensureDefaultGroups()\n        }\n      } else {\n        throw new Error(result.errMsg || '获取云端分组数据失败');\n      }\n    },\n    \n    // 智能合并本地和云端分组数据\n    _mergeGroupData(localGroups, cloudGroups) {\n      devLog.log('[groupStore] Merging local and cloud group data...');\n      \n      // 安全处理空数据\n      const safeLocalGroups = Array.isArray(localGroups) ? localGroups : [];\n      const safeCloudGroups = Array.isArray(cloudGroups) ? cloudGroups : [];\n      \n      devLog.log('[groupStore] Local groups before merge:', safeLocalGroups.map(g => ({ id: g.id, name: g.name, isDefault: g.isDefault, order: g.order })));\n      devLog.log('[groupStore] Cloud groups before merge:', safeCloudGroups.map(g => ({ id: g.id, name: g.name, isDefault: g.isDefault, order: g.order })));\n      \n      // 如果两边都是空数据，直接返回空数组，不触发响应式更新\n      if (safeLocalGroups.length === 0 && safeCloudGroups.length === 0) {\n        devLog.log('[groupStore] Both local and cloud groups are empty, returning empty array');\n        return [];\n      }\n      \n      const mergedMap = new Map();\n      \n      // 首先保留所有默认分组（本地优先）\n      const defaultGroups = safeLocalGroups.filter(group => group.isDefault);\n      defaultGroups.forEach(group => {\n        if (group.id) { // 确保有有效的ID\n        mergedMap.set(group.id, { \n          ...group,\n          _source: 'local-default'\n        });\n        }\n      });\n      \n      // 处理云端非默认数据（确保id字段映射）\n      safeCloudGroups.forEach(cloudGroup => {\n        if (!cloudGroup.isDefault && cloudGroup.id) {\n          // 确保云端数据有id字段\n          const groupWithId = {\n            ...cloudGroup,\n            id: cloudGroup.id || cloudGroup._id, // 确保id字段存在\n            _source: 'cloud'\n          };\n          mergedMap.set(groupWithId.id, groupWithId);\n        }\n      });\n      \n      // 处理本地非默认数据，确保不丢失\n      const localNonDefaultGroups = safeLocalGroups.filter(group => !group.isDefault);\n      localNonDefaultGroups.forEach(localGroup => {\n        const id = localGroup.id;\n        const existingGroup = mergedMap.get(id);\n        \n        if (!existingGroup) {\n          // 本地独有的数据（可能是新创建的或离线创建的）\n          devLog.log(`[groupStore] Preserving local-only group: ${localGroup.name}`);\n          mergedMap.set(id, {\n            ...localGroup,\n            _source: 'local-only',\n            _needSync: true // 标记需要同步到云端\n          });\n        } else {\n          // 存在冲突，比较时间戳\n          const localTime = new Date(localGroup.updateDate || localGroup.createDate || 0);\n          const cloudTime = new Date(existingGroup.updateDate || existingGroup.createDate || 0);\n          \n          if (localTime > cloudTime) {\n            // 本地数据更新，使用本地数据\n            devLog.log(`[groupStore] Local group data newer for ${localGroup.name}, using local version`);\n            mergedMap.set(id, {\n              ...localGroup,\n              _source: 'local-newer',\n              _needSync: true // 需要同步到云端\n            });\n          } else if (localTime < cloudTime) {\n            // 云端数据更新，使用云端数据\n            devLog.log(`[groupStore] Cloud group data newer for ${existingGroup.name}, using cloud version`);\n            // 保持云端数据，无需额外处理\n          } else {\n            // 时间戳相同，检查内容是否有差异\n            if (this._hasGroupContentDifference(localGroup, existingGroup)) {\n              devLog.log(`[groupStore] Group content difference detected for ${localGroup.name}, preferring cloud data`);\n              // 内容有差异但时间戳相同，优先使用云端数据\n            } else {\n              // 时间戳相同且内容相同，保持云端数据（避免_needSync标记）\n              devLog.log(`[groupStore] Groups are identical for ${localGroup.name}, keeping cloud version`);\n            }\n          }\n        }\n      });\n      \n      // 转换为数组并过滤已删除的项目\n      let mergedArray = Array.from(mergedMap.values())\n        .filter(group => !group._deleted);\n      \n      // 修复排序：确保新添加的分组有正确的order值\n      mergedArray = this._fixGroupOrder(mergedArray);\n      \n      devLog.log('[groupStore] Group data merge completed:', {\n        localCount: safeLocalGroups.length,\n        cloudCount: safeCloudGroups.length,\n        mergedCount: mergedArray.length\n      });\n      devLog.log('[groupStore] Merged groups:', mergedArray.map(g => ({ id: g.id, name: g.name, isDefault: g.isDefault, order: g.order, source: g._source })));\n      \n      return mergedArray;\n    },\n    \n    // 修复分组排序\n    _fixGroupOrder(groups) {\n      // 为没有order的分组分配order值\n      let maxOrder = Math.max(0, ...groups.filter(g => g.order !== undefined).map(g => g.order));\n      \n      groups.forEach(group => {\n        if (group.order === undefined || group.order === null) {\n          maxOrder += 1;\n          group.order = maxOrder;\n          devLog.log(`[groupStore] Assigned order ${maxOrder} to group: ${group.name}`);\n        }\n      });\n      \n      // 按order排序\n      return groups.sort((a, b) => (a.order || 0) - (b.order || 0));\n    },\n    \n    // 检查两个分组对象是否有内容差异\n    _hasGroupContentDifference(group1, group2) {\n      const keys = ['name', 'icon', 'color', 'description'];\n      \n      return keys.some(key => {\n        return group1[key] !== group2[key];\n      });\n    },\n    \n    // 检查是否为网络错误\n    _isNetworkError(error) {\n      if (error.code === 'NETWORK_ERROR') return true;\n      if (error.message && (\n        error.message.includes('网络') || \n        error.message.includes('network') ||\n        error.message.includes('timeout')\n      )) return true;\n      \n      return false;\n    },\n    \n    // 初始化默认分组\n    _initDefaultGroups() {\n      const defaultGroups = [\n        {\n          id: 'all',\n          name: '全部',\n          isDefault: true,\n          order: 0\n        },\n        {\n          id: 'gift',\n          name: '礼物',\n          isDefault: true,\n          order: 1\n        },\n        {\n          id: 'friend-visible',\n          name: '朋友可见',\n          isDefault: true,\n          order: 2\n        }\n      ];\n      \n      // 安全地更新数组，避免DOM错误\n      try {\n        // 清空当前数组\n        this.groups.splice(0, this.groups.length);\n        // 添加新元素\n        this.groups.push(...defaultGroups);\n      } catch (error) {\n        devLog.error('[groupStore] Error updating default groups safely, falling back to direct assignment:', error);\n        // 降级：直接赋值\n        this.groups = defaultGroups;\n      }\n      \n      this._saveToStorage()\n    },\n    \n    // 确保默认分组存在\n    ensureDefaultGroups() {\n      // 检查\"全部\"分组是否存在\n      const allGroupExists = this.groups.some(group => group.id === 'all')\n      if (!allGroupExists) {\n        this.groups.push({\n          id: 'all',\n          name: '全部',\n          isDefault: true,\n          order: 0 // 确保始终在第一位\n        })\n      } else {\n        // 确保\"全部\"分组始终order为0\n        const allGroupIndex = this.groups.findIndex(group => group.id === 'all')\n        if (allGroupIndex > -1) {\n          this.groups[allGroupIndex].order = 0\n        }\n      }\n      \n      // 检查\"礼物\"分组是否存在\n      const giftGroupExists = this.groups.some(group => group.id === 'gift')\n      if (!giftGroupExists) {\n        this.groups.push({\n          id: 'gift',\n          name: '礼物',\n          isDefault: true,\n          order: 1\n        })\n      }\n      \n      // 检查\"朋友可见\"分组是否存在\n      const friendVisibleGroupExists = this.groups.some(group => group.id === 'friend-visible')\n      if (!friendVisibleGroupExists) {\n        this.groups.push({\n          id: 'friend-visible',\n          name: '朋友可见',\n          isDefault: true,\n          order: 2\n        })\n      }\n      \n      // 保存到本地存储\n      this._saveToStorage()\n    },\n    \n    // 添加新分组\n    async addGroup(name) {\n      const userStore = useUserStore(); // Get user store instance\n      \n      // 直接检查登录状态，不需要重新加载\n      if (!userStore.isLogin) {\n        devLog.error('[store/group.js addGroup] Critical: User not logged in before attempting to create group.');\n        uni.showToast({\n          title: '请先登录以创建分组',\n          icon: 'none',\n          duration: 3000\n        });\n        // Attempt to redirect to login. Note: This might be hard if called from a non-page context.\n        // Consider if a global event or a different redirect mechanism is needed if this store action\n        // can be called from contexts where uni.navigateTo isn't appropriate.\n        // For now, assuming it's triggered from a page context that can handle this.\n        /* No, let the page handler do the redirect. This store should just fail.\n        uni.navigateTo({\n            url: '/pages/login/login?redirect=' + encodeURIComponent(getCurrentPages().pop().route) // This is risky from a store\n        });\n        */\n        return null; // Signal failure\n      }\n\n      if (!name || typeof name !== 'string') {\n        devLog.error('分组名称不合法');\n        uni.showToast({ title: '分组名称不合法', icon: 'none' });\n        return null;\n      }\n      \n      // 前端预检查，避免不必要的云函数调用\n      if (this._checkGroupNameExists(name)) {\n        devLog.warn('前端检查：分组名称已存在 - ', name);\n        uni.showToast({ title: '分组名称已存在', icon: 'none' });\n        return null;\n      }\n      \n      try {\n        // 添加时间戳\n        const timestamp = Date.now();\n\n        // 先调用云函数创建\n        const groupCenter = uniCloud.importObject('group-center');\n        const result = await groupCenter.createGroup({\n          name: name,\n          icon: '',\n          color: '#8a2be2',\n          // 时间戳支持\n          timestamp,\n          deviceId: uni.getSystemInfoSync().deviceId || 'unknown'\n        });\n        \n        if (result.errCode === 0) {\n          // 云端创建成功，确保新分组有正确的排序位置\n          // 计算最大order值，但要考虑默认分组的排序\n          const nonDefaultGroups = this.groups.filter(g => !g.isDefault);\n          const defaultGroupsMaxOrder = 2; // 默认分组最大order是2（朋友可见）\n          const maxOrder = Math.max(defaultGroupsMaxOrder, ...nonDefaultGroups.map(g => g.order || 0));\n          \n          const newGroup = {\n            ...result.data,\n            id: result.data.id || result.data._id, // 确保id字段存在\n            order: result.data.order || maxOrder + 1 // 确保新分组排在所有分组之后\n          };\n          \n          // 添加到本地列表\n          this.groups.push(newGroup);\n          \n          // 重新排序以确保顺序正确\n          this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));\n          \n          // 立即保存到本地存储，确保新分组被保存\n          this._saveToStorage();\n          \n          // 分组添加成功记录\n          devLog.log('[groupStore] 分组添加成功:', {\n            groupName: newGroup.name,\n            groupId: result.data.id,\n            groupOrder: newGroup.order\n          });\n          \n          // 触发响应式更新\n          this._triggerReactiveUpdate();\n          \n          // 数据已保存到本地，等待网络恢复后自动同步\n\n          // 数据变化通知已通过云函数推送处理\n          \n          uni.showToast({ title: '分组创建成功', icon: 'success' });\n          \n          devLog.log('[groupStore] New group added:', newGroup.name, 'with order:', newGroup.order);\n          devLog.log('[groupStore] Current groups after adding:', this.groups.map(g => ({ id: g.id, name: g.name, order: g.order })));\n          \n          return result.data.id;\n        } else {\n          // 云函数返回了业务错误 (例如：名称已存在，权限不足等)\n          devLog.error('云函数创建分组失败:', result.errMsg, '(errCode:', result.errCode, ')');\n          uni.showToast({\n            title: result.errMsg || '创建分组失败',\n            icon: 'none',\n            duration: 3000\n          });\n          return null; //明确告知创建失败\n        }\n      } catch (error) {\n        // 这个 catch 主要处理网络错误或 uniCloud.importObject/调用 本身抛出的JS错误\n        devLog.error('调用云函数添加分组时发生异常:', error);\n        \n        const isNetworkIssue = error.message && (error.message.includes('network') || error.message.includes('超时'));\n\n        if (isNetworkIssue) {\n          devLog.log('网络问题，尝试离线保存分组:', name);\n        const id = 'group_' + Date.now();\n        // 计算最大order值，但要考虑默认分组的排序\n        const nonDefaultGroups = this.groups.filter(g => !g.isDefault);\n        const defaultGroupsMaxOrder = 2; // 默认分组最大order是2（朋友可见）\n        const maxOrder = Math.max(defaultGroupsMaxOrder, ...nonDefaultGroups.map(g => g.order || 0));\n        \n        const newGroup = {\n          id,\n          name,\n          isDefault: false,\n          order: maxOrder + 1,\n            _needSync: true\n        };\n        this.groups.push(newGroup);\n        \n        // 立即保存到本地存储\n        this._saveToStorage();\n        \n        // 触发响应式更新\n        this._triggerReactiveUpdate();\n        \n        // 数据已标记为需要同步，等待网络恢复后自动同步\n        \n        uni.showToast({\n            title: '网络似乎有问题，已保存到本地',\n          icon: 'none'\n          });\n          \n        devLog.log('[groupStore] Offline group added:', newGroup.name, 'with order:', newGroup.order);\n        devLog.log('[groupStore] Current groups after offline adding:', this.groups.map(g => ({ id: g.id, name: g.name, order: g.order })));\n        \n        return id;\n        } else {\n          uni.showToast({\n            title: '创建分组时发生未知错误',\n            icon: 'none'\n          });\n          return null;\n        }\n      }\n    },\n    \n    // 更新分组名称\n    async updateGroup(id, name) {\n      const group = this.groups.find(g => g.id === id)\n      if (group && !group.isDefault) {\n        // 检查是否重复（忽略大小写，排除当前分组）\n        if (this._checkGroupNameExists(name, id)) {\n          devLog.warn('[groupStore] updateGroup: 分组名称已存在 -', name);\n          uni.showToast({\n            title: '分组名称已存在',\n            icon: 'none'\n          });\n          return;\n        }\n        \n        try {\n          // 先更新云端\n          const groupCenter = uniCloud.importObject('group-center')\n          const result = await groupCenter.updateGroup(id, { name })\n          \n          if (result.errCode === 0) {\n            // 云端更新成功，更新本地数据\n            group.name = name\n            this._saveToStorage()\n            this._triggerReactiveUpdate()\n            \n            // 数据已保存到本地，等待网络恢复后自动同步\n            \n                    // 数据变化通知已通过云函数推送处理\n          } else {\n            throw new Error(result.errMsg || '更新分组失败')\n          }\n        } catch (error) {\n          devLog.error('更新分组失败:', error)\n          \n          // 云端失败时，先更新本地\n          group.name = name\n          group._needSync = true\n          this._saveToStorage()\n          this._triggerReactiveUpdate()\n          \n          // 数据已标记为需要同步，等待网络恢复后自动同步\n          \n          uni.showToast({\n            title: '已保存到本地，稍后将同步到云端',\n            icon: 'none'\n          })\n        }\n      }\n    },\n    \n    // 删除分组（优化版本：先本地删除，后台同步）\n    // \n    // 优化后的删除流程：\n    // 1. 立即删除本地数据，提供即时响应（用户体验好）\n    // 2. 后台异步删除云端数据，不阻塞用户操作\n    // 3. 避免调用loadUserFromStorage()造成的重复登录验证\n    // 4. 避免触发完整的数据重新同步\n    async deleteGroup(id) {\n      // 验证参数\n      if (!id) {\n        devLog.error('[groupStore] deleteGroup: 分组ID不能为空');\n        uni.showToast({\n          title: '分组ID不能为空',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      devLog.log('[groupStore] deleteGroup called with id:', id);\n      \n      // 不能删除默认分组\n      const group = this.groups.find(g => g.id === id)\n      if (!group) {\n        devLog.error('[groupStore] deleteGroup: 找不到指定的分组, id:', id);\n        uni.showToast({\n          title: '找不到指定的分组',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (group.isDefault) {\n        devLog.error('[groupStore] deleteGroup: 不能删除默认分组, id:', id);\n        uni.showToast({\n          title: '不能删除默认分组',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (group && !group.isDefault) {\n        // 立即删除本地数据，提供即时响应\n        this.groups = this.groups.filter(g => g.id !== id)\n        \n        // 触发响应式更新\n        this._triggerReactiveUpdate()\n        \n        this._saveToStorage()\n        \n        devLog.log('[groupStore] deleteGroup: 本地删除成功, id:', id);\n        \n        // 后台异步删除云端数据，不阻塞用户操作\n        this._deleteGroupFromCloud(id);\n        \n        return true;\n      }\n      \n      devLog.log('[groupStore] deleteGroup: 不符合删除条件, id:', id);\n      return false;\n    },\n    \n    // 后台删除云端分组数据\n    async _deleteGroupFromCloud(id) {\n      try {\n        devLog.log('[groupStore] 后台删除云端分组, id:', id);\n        \n        const groupCenter = uniCloud.importObject('group-center')\n        const result = await groupCenter.deleteGroup(id)\n        \n        if (result.errCode === 0) {\n          devLog.log('[groupStore] 云端分组删除成功, id:', id);\n          \n                  // 数据变化通知已通过云函数推送处理\n        } else {\n          devLog.warn('[groupStore] 云端分组删除失败, id:', id, 'error:', result.errMsg);\n          // 云端删除失败，但本地已删除，用户已得到即时响应\n          // 可以在后续同步时处理这种不一致情况\n        }\n      } catch (error) {\n        devLog.error('[groupStore] 后台删除云端分组失败, id:', id, 'error:', error);\n        // 静默处理错误，不影响用户体验\n        // 删除操作从用户角度看已经成功完成\n      }\n    },\n    \n    // 更新分组顺序\n    async updateGroupOrder(id, order) {\n      // 查找目标分组\n      const groupIndex = this.groups.findIndex(group => group.id === id)\n      \n      if (groupIndex !== -1) {\n        try {\n          // 先更新云端\n          const groupCenter = uniCloud.importObject('group-center')\n          const result = await groupCenter.updateGroup(id, { order })\n          \n          if (result.errCode === 0) {\n            // 云端更新成功，更新本地数据\n            this.groups[groupIndex] = {\n              ...this.groups[groupIndex],\n              order\n            }\n            this._saveToStorage()\n            \n                    // 数据变化通知已通过云函数推送处理\n            \n            return true\n          } else {\n            throw new Error(result.errMsg || '更新排序失败')\n          }\n        } catch (error) {\n          devLog.error('更新分组排序失败:', error)\n          \n          // 云端失败时，先更新本地\n          this.groups[groupIndex] = {\n            ...this.groups[groupIndex],\n            order,\n            _needSync: true\n          }\n          this._saveToStorage()\n          return true\n        }\n      }\n      \n      return false\n    },\n    \n    // 重置分组数据\n    resetGroups() {\n      // 清除本地存储\n      uni.removeStorageSync('groups')\n      \n      // 重新初始化默认分组\n      this._initDefaultGroups()\n      \n      // 返回通知\n      return {\n        success: true,\n        message: '分组数据已重置'\n      }\n    },\n    \n    // 手动同步待上传的本地数据到云端（仅在用户主动操作时调用）\n    async syncPendingData() {\n      const pendingGroups = this.groups.filter(group => group._needSync)\n      \n      if (pendingGroups.length === 0) {\n        devLog.log('[groupStore] No pending groups to sync');\n        return;\n      }\n      \n      devLog.log(`[groupStore] Manual sync triggered for ${pendingGroups.length} pending groups`);\n      \n      for (const group of pendingGroups) {\n        try {\n          const groupCenter = uniCloud.importObject('group-center')\n          \n          if (group.id.startsWith('group_')) {\n            // 临时ID，需要创建\n            const result = await groupCenter.createGroup({\n              name: group.name,\n              icon: group.icon || '',\n              color: group.color || '#8a2be2'\n            })\n            if (result.errCode === 0) {\n              // 更新本地ID\n              group.id = result.data.id\n              delete group._needSync\n              devLog.log(`[groupStore] Successfully synced new group: ${group.name}`);\n            }\n          } else {\n            // 已有ID，需要更新\n            const result = await groupCenter.updateGroup(group.id, {\n              name: group.name,\n              order: group.order\n            })\n            if (result.errCode === 0) {\n              delete group._needSync\n              devLog.log(`[groupStore] Successfully synced updated group: ${group.name}`);\n            }\n          }\n        } catch (error) {\n          // 特殊处理：分组不存在或无权限操作（通常是多设备同步冲突）\n          if (error.message && (\n            error.message.includes('分组不存在') || \n            error.message.includes('无权限') ||\n            error.message.includes('not found') ||\n            error.message.includes('permission')\n          )) {\n            // 多设备冲突处理 - 不显示错误，只输出信息日志\n            devLog.log(`[groupStore] 多设备同步冲突处理: 分组 \"${group.name}\" 在云端不存在，可能已被其他设备删除`);\n            \n            // 移除本地的同步标记，避免重复尝试\n            delete group._needSync;\n            \n            // 如果分组在云端不存在，从本地也移除（但保留默认分组）\n            if (!group.isDefault) {\n              const localIndex = this.groups.findIndex(g => g.id === group.id);\n              if (localIndex !== -1) {\n                devLog.log(`[groupStore] 清理本地已删除的分组: ${group.name}`);\n                this.groups.splice(localIndex, 1);\n              }\n            }\n          } else {\n            // 其他类型的错误才输出错误日志\n            devLog.error('同步分组失败:', group.name, error)\n          }\n        }\n      }\n      \n      this._saveToStorage()\n    },\n    \n    // 智能同步 - 只同步有差异的数据（用于下拉刷新）\n    async smartSync() {\n      devLog.log('[groupStore] Starting smart sync...');\n      \n      if (!this.isOnline) {\n        devLog.warn('[groupStore] Network unavailable, skipping smart sync');\n        return { hasUpdates: false, updatedCount: 0, reason: 'offline' };\n      }\n\n      try {\n        // 1. 先上传本地待同步的数据\n        const pendingGroups = this.groups.filter(group => group._needSync);\n        if (pendingGroups.length > 0) {\n          devLog.log(`[groupStore] Uploading ${pendingGroups.length} pending groups...`);\n          await this.syncPendingData();\n        }\n\n        // 2. 获取云端数据摘要信息\n        const groupCenter = uniCloud.importObject('group-center');\n        const summaryResult = await groupCenter.getGroupSyncSummary();\n        \n        if (summaryResult.errCode !== 0) {\n          throw new Error(summaryResult.errMsg || '获取云端分组摘要失败');\n        }\n\n        const cloudSummary = summaryResult.data || {};\n        devLog.log('[groupStore] Cloud summary received:', cloudSummary);\n\n        // 3. 对比本地数据摘要\n        const localSummary = this._generateLocalSummary();\n        devLog.log('[groupStore] Local summary:', localSummary);\n\n        // 4. 判断是否需要同步\n        const needsSync = this._compareDataSummaries(localSummary, cloudSummary);\n        \n        if (!needsSync) {\n          devLog.log('[groupStore] Data is up to date, no sync needed');\n          return { hasUpdates: false, updatedCount: 0, reason: 'up_to_date' };\n        }\n\n        // 5. 执行增量同步\n        devLog.log('[groupStore] Data differences detected, performing incremental sync...');\n        const syncResult = await this._performIncrementalSync(cloudSummary);\n        \n        // 6. 清理可能的无效同步标记\n        this.cleanupInvalidSyncMarkers();\n        \n        devLog.log('[groupStore] Smart sync completed:', syncResult);\n        return syncResult;\n\n      } catch (error) {\n        devLog.error('[groupStore] Smart sync failed:', error);\n        throw error;\n      }\n    },\n\n    // 生成本地数据摘要\n    _generateLocalSummary() {\n      const nonDefaultGroups = this.groups.filter(g => !g.isDefault);\n      \n      return {\n        count: nonDefaultGroups.length,\n        lastModified: this._getLocalGroupLastModified(),\n        ids: nonDefaultGroups.map(g => g.id).sort(),\n        checksum: this._calculateGroupsChecksum(nonDefaultGroups)\n      };\n    },\n\n    // 计算分组数据校验和\n    _calculateGroupsChecksum(groups) {\n      const dataStr = groups\n        .map(g => `${g._id || g.id}:${g.name}:${g.updateDate || g.createDate}:${g.order}`)\n        .sort()\n        .join('|');\n      \n      // 简单的字符串哈希（用于快速对比）\n      let hash = 0;\n      for (let i = 0; i < dataStr.length; i++) {\n        const char = dataStr.charCodeAt(i);\n        hash = ((hash << 5) - hash) + char;\n        hash = hash & hash; // Convert to 32bit integer\n      }\n      return hash.toString(36);\n    },\n\n    // 对比数据摘要\n    _compareDataSummaries(localSummary, cloudSummary) {\n      // 如果数量不同，需要同步\n      if (localSummary.count !== cloudSummary.count) {\n        devLog.log('[groupStore] Count difference detected:', localSummary.count, 'vs', cloudSummary.count);\n        return true;\n      }\n\n      // 如果校验和不同，需要同步\n      if (localSummary.checksum !== cloudSummary.checksum) {\n        devLog.log('[groupStore] Checksum difference detected:', localSummary.checksum, 'vs', cloudSummary.checksum);\n        return true;\n      }\n\n      // 如果云端最后修改时间更新，需要同步\n      if (cloudSummary.lastModified && localSummary.lastModified) {\n        const cloudTime = new Date(cloudSummary.lastModified).getTime();\n        const localTime = new Date(localSummary.lastModified).getTime();\n        \n        if (cloudTime > localTime) {\n          devLog.log('[groupStore] Cloud data is newer:', cloudSummary.lastModified, 'vs', localSummary.lastModified);\n          return true;\n        }\n      }\n\n      return false;\n    },\n\n    // 执行增量同步\n    async _performIncrementalSync(cloudSummary) {\n      // 获取完整的云端数据\n      const groupCenter = uniCloud.importObject('group-center');\n      const result = await groupCenter.getGroupList({ includeDeleted: true });\n      \n      if (result.errCode !== 0) {\n        throw new Error(result.errMsg || '获取云端分组数据失败');\n      }\n\n      const cloudGroups = result.data || [];\n      const beforeCount = this.groups.length;\n\n      // 使用现有的智能合并逻辑\n      const mergedData = this._mergeGroupData(this.groups, cloudGroups);\n      \n      // 安全更新数据\n      await this._safeUpdateGroups(mergedData);\n      \n      const afterCount = this.groups.length;\n      const updatedCount = Math.abs(afterCount - beforeCount);\n\n      this.lastSyncTime = new Date().toISOString();\n      this._saveToStorage();\n\n      return {\n        hasUpdates: true,\n        updatedCount: updatedCount,\n        beforeCount: beforeCount,\n        afterCount: afterCount,\n        reason: 'incremental_sync'\n      };\n    },\n\n    // 安全更新分组数据\n    async _safeUpdateGroups(newData) {\n      try {\n        // 使用已有的安全更新策略\n        await new Promise((resolve) => {\n          if (this.$nextTick) {\n            this.$nextTick(() => {\n              this.groups = newData;\n              resolve();\n            });\n          } else {\n            setTimeout(() => {\n              this.groups = newData;\n              resolve();\n            }, 0);\n          }\n        });\n      } catch (error) {\n        devLog.error('[groupStore] Error in safe update, falling back to direct assignment:', error);\n        this.groups = newData;\n      }\n    },\n\n    // 清理无效的同步标记（多设备冲突后的清理）\n    cleanupInvalidSyncMarkers() {\n      let cleanedCount = 0;\n      \n      this.groups.forEach(group => {\n        // 如果分组有同步标记但缺少必要字段，清除标记\n        if (group._needSync && (!group.id || !group.name)) {\n          devLog.log(`[groupStore] 清理无效的同步标记: ${group.name || '未知分组'}`);\n          delete group._needSync;\n          cleanedCount++;\n        }\n      });\n      \n      if (cleanedCount > 0) {\n        devLog.log(`[groupStore] 已清理 ${cleanedCount} 个无效的同步标记`);\n        this._saveToStorage();\n      }\n      \n      return cleanedCount;\n    },\n    \n    // 手动触发完整同步（供用户主动调用）\n    async manualSync() {\n      if (!this.isOnline) {\n        console.log('[groupStore] 当前网络不可用，跳过同步');\n        return;\n      }\n\n      try {\n        // 🔧 删除加载弹窗，静默同步\n        // 先同步待上传的数据\n        await this.syncPendingData();\n\n        // 再从云端同步最新数据\n        await this.syncFromCloud();\n\n        // 🔧 删除同步完成弹窗\n        devLog.log('[groupStore] Manual sync completed');\n\n      } catch (error) {\n        devLog.error('[groupStore] Manual sync failed:', error);\n        // 🔧 删除同步失败弹窗\n        console.log('[groupStore] 同步失败');\n      }\n    },\n    \n    // 保存到本地存储\n    _saveToStorage() {\n      uni.setStorageSync('groups', JSON.stringify(this.groups))\n      uni.setStorageSync('groupsLastSyncTime', this.lastSyncTime)\n    },\n    \n    // 清理本地数据（用于重新登录时清除旧用户数据）\n    clearLocalData() {\n      devLog.log('[groupStore] Clearing user-specific data...');\n      \n      // 只清理用户自定义的分组，保留默认分组\n      this.groups = this.groups.filter(group => group.isDefault === true);\n      \n      // 确保默认分组的完整性\n      this.ensureDefaultGroups();\n      \n      this.lastSyncTime = null;\n      this.isLoading = false;\n      \n      // 清除本地存储，但会通过 _saveToStorage 重新保存默认分组\n      uni.removeStorageSync('groups');\n      uni.removeStorageSync('groupsLastSyncTime');\n      \n      // 保存清理后的数据（包含默认分组）\n      this._saveToStorage();\n      \n      devLog.log('[groupStore] User-specific data cleared, default groups preserved');\n    },\n    \n    // 强制重新初始化（用于登录后重新同步）\n    async forceInit() {\n      devLog.log('[groupStore] Force initialization...');\n      \n      // 清空当前数据\n      this.groups = [];\n      this.lastSyncTime = null;\n      this.isLoading = false;\n      \n      // 清理本地存储\n      uni.removeStorageSync('groups');\n      uni.removeStorageSync('groupsLastSyncTime');\n      \n      // 强制从云端同步\n      await this.syncFromCloud();\n      \n      // 确保默认分组存在\n      this.ensureDefaultGroups();\n      \n      devLog.log('[groupStore] Force initialization completed');\n    },\n    \n    // 清理重复分组和修复排序\n    async cleanupAndFixGroups() {\n      devLog.log('[groupStore] Starting group cleanup and fix...');\n      \n      try {\n        // 检查是否有重复的分组名称\n        const groupNames = new Map();\n        const duplicates = [];\n        \n        this.groups.forEach((group, index) => {\n          if (!group.isDefault) { // 只检查非默认分组\n            const lowerName = group.name.toLowerCase();\n            if (groupNames.has(lowerName)) {\n              duplicates.push({\n                index,\n                group,\n                originalIndex: groupNames.get(lowerName).index\n              });\n            } else {\n              groupNames.set(lowerName, { group, index });\n            }\n          }\n        });\n        \n        // 移除重复的分组（保留最新的）\n        if (duplicates.length > 0) {\n          devLog.log('[groupStore] Found and removing', duplicates.length, 'duplicate groups');\n\n          // 从后往前移除，避免索引变化\n          duplicates.sort((a, b) => b.index - a.index);\n          duplicates.forEach(duplicate => {\n            this.groups.splice(duplicate.index, 1);\n          });\n        }\n        \n        // 修复排序\n        const fixedGroups = this._fixGroupOrder(this.groups);\n        this.groups = fixedGroups;\n        \n        // 保存修复后的数据\n        this._saveToStorage();\n        \n        devLog.log('[groupStore] Group cleanup completed');\n        \n        // 如果有修复，显示提示\n        if (duplicates.length > 0) {\n          uni.showToast({\n            title: `已清理${duplicates.length}个重复分组`,\n            icon: 'success',\n            duration: 2000\n          });\n        }\n        \n        return {\n          duplicatesRemoved: duplicates.length,\n          totalGroups: this.groups.length\n        };\n        \n      } catch (error) {\n        devLog.error('[groupStore] Group cleanup failed:', error);\n        return { error: error.message };\n      }\n    },\n    \n    // 手动触发数据修复（供用户或开发者调用）\n    async repairGroupData() {\n      try {\n        // 🔧 删除加载弹窗，静默修复\n\n        // 执行清理\n        const result = await this.cleanupAndFixGroups();\n\n        // 重新同步确保数据一致性\n        if (this.isOnline) {\n          await this.syncFromCloud();\n        }\n\n        // 🔧 删除修复完成弹窗\n        devLog.log('[groupStore] Data repair completed');\n\n        return result;\n\n      } catch (error) {\n        devLog.error('[groupStore] Repair failed:', error);\n        // 🔧 删除修复失败弹窗\n        console.log('[groupStore] 数据修复失败');\n        \n        throw error;\n      }\n    },\n    \n    // 触发响应式更新（确保UI立即响应）\n    _triggerReactiveUpdate() {\n      // 优化响应式更新，避免DOM错误\n      try {\n        // 方法1：使用 nextTick 确保DOM更新在合适的时机\n        this.$nextTick && this.$nextTick(() => {\n          // 在nextTick中触发更新可以避免DOM操作冲突\n          devLog.log('[groupStore] Reactive update triggered via nextTick, groups count:', this.groups.length);\n        });\n        \n        // 方法2：温和的响应式更新 - 避免强制数组重建\n        // 只在必要时创建新数组引用\n        if (this.groups && this.groups.length > 0) {\n          // 确保数组引用变化但不破坏现有对象引用\n          const updatedGroups = this.groups.map(group => ({ ...group }));\n          this.groups = updatedGroups;\n        }\n        \n        devLog.log('[groupStore] Gentle reactive update completed, groups count:', this.groups.length);\n      } catch (error) {\n        devLog.error('[groupStore] Reactive update failed:', error);\n        // 降级到基本更新\n        this.groups = [...(this.groups || [])];\n      }\n    },\n    \n\n    \n    // 调试方法：检查分组数据完整性（仅开发环境）\n    debugGroupData() {\n      // 生产环境直接返回空对象，节省资源\n      if (process.env.NODE_ENV === 'production') {\n        return { message: 'Debug功能在生产环境中禁用' };\n      }\n      \n      devLog.log('[groupStore] === 分组数据调试信息 ===');\n      devLog.log('[groupStore] 总分组数量:', this.groups.length);\n      \n      this.groups.forEach((group, index) => {\n        devLog.log(`[groupStore] 分组 ${index}:`, {\n          id: group.id,\n          _id: group._id,\n          name: group.name,\n          isDefault: group.isDefault,\n          _needSync: group._needSync,\n          _source: group._source\n        });\n      });\n      \n      const pendingGroups = this.groups.filter(g => g._needSync);\n      devLog.log('[groupStore] 待同步分组数量:', pendingGroups.length);\n      \n      if (pendingGroups.length > 0) {\n        devLog.log('[groupStore] 待同步分组详情:', pendingGroups.map(g => ({\n          id: g.id,\n          name: g.name\n        })));\n      }\n      \n      return {\n        totalCount: this.groups.length,\n        pendingCount: pendingGroups.length,\n        groups: this.groups.map(g => ({\n          id: g.id,\n          _id: g._id,\n          name: g.name,\n          isDefault: g.isDefault,\n          _needSync: g._needSync\n        }))\n      };\n    },\n\n    /**\n     * 🚀 处理实时数据更新\n     * 当收到实时推送时调用此方法\n     */\n    async _updateLocalFromRealtimeData(realtimeData) {\n      try {\n        devLog.log('[groupStore] 📂 收到实时分组数据更新:', realtimeData.length, '个分组');\n        \n        // 合并实时数据到本地\n        const mergedData = this._mergeGroupData(this.groups, realtimeData);\n        \n        // 更新本地数据\n        this.groups = mergedData;\n        \n        // 保存到本地存储\n        this._saveToStorage();\n        \n        // 触发响应式更新\n        this._triggerReactiveUpdate();\n        \n        devLog.log('[groupStore] ✅ 实时分组数据更新完成');\n        \n      } catch (error) {\n        devLog.error('[groupStore] 处理实时分组数据更新失败:', error);\n      }\n    },\n    \n    // 重新整理分组顺序，确保正确的排序\n    reorganizeGroupOrder() {\n      devLog.log('[groupStore] Reorganizing group order...');\n      \n      // 分离默认分组和用户分组\n      const defaultGroups = this.groups.filter(g => g.isDefault);\n      const userGroups = this.groups.filter(g => !g.isDefault);\n      \n      // 确保默认分组有正确的order\n      defaultGroups.forEach((group, index) => {\n        if (group.id === 'all') {\n          group.order = 0;\n        } else if (group.id === 'gift') {\n          group.order = 1;\n        } else if (group.id === 'friend-visible') {\n          group.order = 2;\n        } else {\n          group.order = index;\n        }\n      });\n      \n      // 重新分配用户分组的order，从3开始\n      userGroups.sort((a, b) => (a.order || 999) - (b.order || 999));\n      userGroups.forEach((group, index) => {\n        group.order = 3 + index; // 默认分组占用0-2，用户分组从3开始\n      });\n      \n      // 合并并排序\n      this.groups = [...defaultGroups, ...userGroups];\n      this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));\n      \n      // 保存到本地存储\n      this._saveToStorage();\n      \n      devLog.log('[groupStore] Group order reorganized:', this.groups.map(g => ({ \n        id: g.id, \n        name: g.name, \n        order: g.order, \n        isDefault: g.isDefault \n      })));\n      \n      // 触发响应式更新\n      this._triggerReactiveUpdate();\n    },\n\n    // 🚀 新增：支持新同步架构的方法\n\n    /**\n     * 添加分组到列表（供实时同步调用）\n     */\n    addGroupToList(group) {\n      const existingIndex = this.groups.findIndex(g => g.id === group.id || g._id === group._id);\n      if (existingIndex === -1) {\n        this.groups.push({\n          ...group,\n          id: group.id || group._id // 确保有 id 字段\n        });\n        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));\n        this._saveToStorage();\n        devLog.log('[groupStore] 🚀 添加分组到列表:', group.name);\n      }\n    },\n\n    /**\n     * 更新列表中的分组（供实时同步调用）\n     */\n    updateGroupInList(group) {\n      const index = this.groups.findIndex(g => g.id === group.id || g._id === group._id);\n      if (index !== -1) {\n        this.groups[index] = {\n          ...group,\n          id: group.id || group._id // 确保有 id 字段\n        };\n        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));\n        this._saveToStorage();\n        devLog.log('[groupStore] 🚀 更新列表中的分组:', group.name);\n      }\n    },\n\n    /**\n     * 从列表中移除分组（供实时同步调用）\n     */\n    removeGroupFromList(groupId) {\n      const index = this.groups.findIndex(g => g.id === groupId || g._id === groupId);\n      if (index !== -1) {\n        const groupName = this.groups[index].name;\n        this.groups.splice(index, 1);\n        this._saveToStorage();\n        devLog.log('[groupStore] 🚀 从列表中移除分组:', groupName);\n      }\n    },\n\n    // 注意：syncFromCloud 方法已在上面定义，这里移除重复定义\n\n    // ==================== 新增同步优化方法 ====================\n\n    /**\n     * 通用分组名称检查方法（避免重复逻辑）\n     */\n    _checkGroupNameExists(name, excludeId = null) {\n      if (!name) return false;\n\n      const lowerName = name.toLowerCase();\n      return this.groups.some(group => {\n        // 排除指定ID的分组（用于更新时检查）\n        if (excludeId && (group.id === excludeId || group._id === excludeId)) {\n          return false;\n        }\n        return group.name.toLowerCase() === lowerName;\n      });\n    },\n\n    /**\n     * 从同步中添加分组（避免触发推送）\n     */\n    addGroupFromSync(group) {\n      const existingIndex = this.groups.findIndex(g => g.id === group.id || g._id === group._id)\n      if (existingIndex === -1) {\n        this.groups.push({\n          ...group,\n          id: group._id || group.id\n        })\n        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0))\n        this._saveToStorage()\n        devLog.log(`[groupStore] 从同步添加分组: ${group.name}`)\n      }\n    },\n\n    /**\n     * 从同步中更新分组（避免触发推送）\n     */\n    updateGroupFromSync(group) {\n      const index = this.groups.findIndex(g => g.id === group.id || g._id === group._id)\n      if (index !== -1) {\n        this.groups[index] = {\n          ...group,\n          id: group._id || group.id\n        }\n        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0))\n        this._saveToStorage()\n        devLog.log(`[groupStore] 从同步更新分组: ${group.name}`)\n      }\n    },\n\n    /**\n     * 从同步中删除分组（避免触发推送）\n     */\n    removeGroupById(groupId) {\n      const index = this.groups.findIndex(g => g.id === groupId || g._id === groupId)\n      if (index !== -1) {\n        const removedGroup = this.groups.splice(index, 1)[0]\n        this._saveToStorage()\n        devLog.log(`[groupStore] 从同步删除分组: ${removedGroup.name}`)\n      }\n    }\n  }\n})\n"], "names": ["defineStore", "uni", "devLog", "useUserStore", "uniCloud"], "mappings": ";;;;;AAsBa,MAAA,gBAAgBA,0BAAY,SAAS;AAAA,EAChD,OAAO,OAAO;AAAA,IACZ,QAAQ,CAAC;AAAA,IACT,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA;AAAA;AAAA,IAGV,qCAAqB,IAAI;AAAA;AAAA,IACzB,eAAe;AAAA;AAAA,EAAA;AAAA,EAGjB,SAAS;AAAA;AAAA,IAEP,cAAc,CAAC,UAAU,MAAM;AAAA;AAAA,IAG/B,cAAc,CAAC,UAAU,CAAC,OAAO;AAC/B,aAAO,MAAM,OAAO,KAAK,WAAS,MAAM,OAAO,EAAE,KAAK;AAAA,IACxD;AAAA;AAAA,IAGA,gBAAgB,CAAC,UAAU;AACzB,aAAO,MAAM,OAAO,KAAK,CAAA,UAAS,MAAM,SAAS;AAAA,IACnD;AAAA;AAAA,IAGA,kBAAkB,CAAC,UAAU;AAC3B,aAAO,MAAM,OAAO,OAAO,CAAS,UAAA,MAAM,SAAS,EAAE;AAAA,IACvD;AAAA;AAAA,IAGA,mBAAmB,CAAC,UAAU;AAC5B,aAAO,MAAM,OAAO,OAAO,CAAA,UAAS,MAAM,SAAS;AAAA,IACrD;AAAA,EACF;AAAA,EAEA,SAAS;AAAA;AAAA,IAEP,MAAM,aAAa;AAEX,YAAA,eAAeC,cAAAA,MAAI,eAAe,QAAQ;AAChD,UAAI,cAAc;AACV,cAAA,SAAS,KAAK,MAAM,YAAY;AAEjC,aAAA,SAAS,OAAO,IAAI,CAAU,WAAA;AAAA,UACjC,GAAG;AAAA,UACH,IAAI,MAAM,MAAM,MAAM;AAAA;AAAA,QACtB,EAAA;AAAA,MACJ;AAGA,WAAK,mBAAmB;AAGxB,YAAM,KAAK;AAGX,WAAK,oBAAoB;AAAA,IAC3B;AAAA;AAAA,IAGA,qBAAqB;AAEnB,WAAK,mBAAmB;AAGpBA,0BAAA,sBAAsB,CAAC,QAAQ;AACjC,cAAM,YAAY,KAAK;AACvB,aAAK,WAAW,IAAI;AAEbC,uBAAAA,OAAA,IAAI,qBAAqB,YAAY,OAAO,IAAI,OAAO,KAAK,WAAW,OAAO,IAAI,EAAE;AAGvF,YAAA,CAAC,aAAa,KAAK,UAAU;AAC/BA,gCAAO,IAAI,yBAAyB;AAAA,QACtC;AAAA,MAAA,CACD;AAAA,IACH;AAAA;AAAA,IAGA,MAAM,qBAAqB;AACrB,UAAA;AACI,cAAA,cAAc,MAAMD,oBAAI;AACzB,aAAA,WAAW,YAAY,gBAAgB;AAC5CC,uBAAA,OAAO,IAAI,qBAAqB,KAAK,WAAW,OAAO,IAAI;AAAA,eACpD,OAAO;AACPA,uBAAAA,OAAA,MAAM,uBAAuB,KAAK;AACzC,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAAA;AAAA,IAGA,cAAc,WAAW;AACjB,YAAA,MAAM,KAAK;AAGjB,UAAI,KAAK,gBAAgB,IAAI,SAAS,GAAG;AAChCA,uBAAA,OAAA,IAAI,qBAAqB,SAAS,QAAQ;AAC1C,eAAA;AAAA,MACT;AAGI,UAAA,MAAM,KAAK,gBAAgB,KAAM;AACnCA,uBAAAA,OAAO,IAAI,0BAA0B;AAC9B,eAAA;AAAA,MACT;AAEO,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,oBAAoB,WAAW;AACxB,WAAA,gBAAgB,IAAI,SAAS;AAC7B,WAAA,gBAAgB,KAAK;AACnBA,qBAAAA,OAAA,IAAI,wBAAwB,SAAS,EAAE;AAAA,IAChD;AAAA;AAAA,IAGA,kBAAkB,WAAW;AACtB,WAAA,gBAAgB,OAAO,SAAS;AAC9BA,qBAAAA,OAAA,IAAI,wBAAwB,SAAS,EAAE;AAAA,IAChD;AAAA;AAAA,IAKA,MAAM,gBAAgB;AACpB,YAAM,YAAY;AAGlB,UAAI,CAAC,KAAK,cAAc,SAAS,GAAG;AAClC;AAAA,MACF;AAEA,YAAM,YAAYC,WAAAA;AACd,UAAA,CAAC,UAAU,SAAS;AACtBD,8BAAO,IAAI,yEAAyE;AAEhF,YAAA,KAAK,OAAO,WAAW;AAAG;AAG9B;AAAA,MACF;AAEI,UAAA;AACF,aAAK,oBAAoB,SAAS;AAClC,aAAK,YAAY;AACjBA,8BAAO,IAAI,iDAAiD;AAGtD,cAAA,aAAa,MAAM,KAAK;AAE9B,YAAI,CAAC,YAAY;AACfA,gCAAO,IAAI,6DAA6D;AAExE,eAAK,oBAAoB;AACzB;AAAA,QACF;AAGA,cAAM,KAAK;eAEJ,OAAO;AACPA,uBAAAA,OAAA,MAAM,0BAA0B,KAAK;AAE5CA,8BAAO,IAAI,iEAAiE;AAC1E,aAAK,mBAAmB;AAAA,MAAA,UAC1B;AACA,aAAK,YAAY;AACjB,aAAK,kBAAkB,SAAS;AAGhC,YAAI,CAAC,KAAK,UAAU,KAAK,OAAO,WAAW,GAAG;AAC5CA,gCAAO,IAAI,yEAAyE;AACpF,eAAK,mBAAmB;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGA,MAAM,wBAAwB;AAIC;AAC3BA,8BAAO,IAAI,0EAA0E;AAC9E,eAAA;AAAA,MACT;AAAA,IA+CF;AAAA;AAAA,IAGA,6BAA6B;AAC3B,YAAM,mBAAmB,KAAK,OAAO,OAAO,CAAS,UAAA,CAAC,MAAM,SAAS;AACrE,UAAI,iBAAiB,WAAW;AAAU,eAAA;AAE1C,YAAM,cAAc,iBAAiB,OAAO,CAAC,QAAQ,YAAY;AAC/D,cAAM,cAAc,IAAI,KAAK,QAAQ,cAAc,QAAQ,cAAc,CAAC;AAC1E,cAAM,aAAa,IAAI,KAAK,OAAO,cAAc,OAAO,cAAc,CAAC;AAChE,eAAA,cAAc,aAAa,UAAU;AAAA,MAAA,CAC7C;AAEM,aAAA,YAAY,cAAc,YAAY;AAAA,IAC/C;AAAA;AAAA,IAGA,MAAM,+BAA+B;AACnCA,4BAAO,IAAI,yDAAyD;AAE9D,YAAA,cAAcE,cAAAA,GAAS,aAAa,cAAc;AAClD,YAAA,SAAS,MAAM,YAAY,aAAa;AAAA,QAC5C,gBAAgB;AAAA;AAAA,MAAA,CACjB;AAEG,UAAA,OAAO,YAAY,GAAG;AAClB,cAAA,cAAc,OAAO,QAAQ;AACnCF,uBAAA,OAAO,IAAI,2CAA2C,YAAY,QAAQ,OAAO;AAGjF,cAAM,aAAa,KAAK,gBAAgB,KAAK,QAAQ,WAAW;AAGhEA,uBAAA,OAAO,IAAI,wBAAwB;AAAA,UACjC,YAAY,KAAK,OAAO;AAAA,UACxB,YAAY,YAAY;AAAA,QAAA,CACzB;AAGG,YAAA;AAEI,gBAAA,IAAI,QAAQ,CAAC,YAAY;AAE7B,gBAAI,KAAK,WAAW;AAClB,mBAAK,UAAU,MAAM;AAC3B,qBAAK,SAAS;AACE;cAAA,CACT;AAAA,YAAA,OACI;AAEL,yBAAW,MAAM;AACf,qBAAK,SAAS;AACN;iBACP,CAAC;AAAA,YACN;AAAA,UAAA,CACD;AAGDA,yBAAA,OAAO,IAAI,wBAAwB;AAAA,YACnC,aAAa,WAAW;AAAA,UAAA,CACzB;AAED,eAAK,gBAAe,oBAAI,KAAK,GAAE,YAAY;AAC3C,eAAK,eAAe;AAGpB,eAAK,oBAAoB;AAEvBA,yBAAA,OAAO,IAAI,+DAA+D,KAAK,OAAO,QAAQ,OAAO;AAAA,iBAC9F,OAAO;AACPA,yBAAAA,OAAA,MAAM,4CAA4C,KAAK;AAE9D,eAAK,SAAS;AACd,eAAK,gBAAe,oBAAI,KAAK,GAAE,YAAY;AAC3C,eAAK,eAAe;AACpB,eAAK,oBAAoB;AAAA,QAC3B;AAAA,MAAA,OACK;AACL,cAAM,IAAI,MAAM,OAAO,UAAU,YAAY;AAAA,MAC/C;AAAA,IACF;AAAA;AAAA,IAGA,gBAAgB,aAAa,aAAa;AACxCA,4BAAO,IAAI,oDAAoD;AAG/D,YAAM,kBAAkB,MAAM,QAAQ,WAAW,IAAI,cAAc;AACnE,YAAM,kBAAkB,MAAM,QAAQ,WAAW,IAAI,cAAc;AAEnEA,4BAAO,IAAI,2CAA2C,gBAAgB,IAAI,CAAM,OAAA,EAAE,IAAI,EAAE,IAAI,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,OAAO,EAAE,QAAQ,CAAC;AACpJA,4BAAO,IAAI,2CAA2C,gBAAgB,IAAI,CAAM,OAAA,EAAE,IAAI,EAAE,IAAI,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,OAAO,EAAE,QAAQ,CAAC;AAGpJ,UAAI,gBAAgB,WAAW,KAAK,gBAAgB,WAAW,GAAG;AAChEA,8BAAO,IAAI,2EAA2E;AACtF,eAAO;MACT;AAEM,YAAA,gCAAgB;AAGtB,YAAM,gBAAgB,gBAAgB,OAAO,CAAA,UAAS,MAAM,SAAS;AACrE,oBAAc,QAAQ,CAAS,UAAA;AAC7B,YAAI,MAAM,IAAI;AACJ,oBAAA,IAAI,MAAM,IAAI;AAAA,YACtB,GAAG;AAAA,YACH,SAAS;AAAA,UAAA,CACV;AAAA,QACD;AAAA,MAAA,CACD;AAGD,sBAAgB,QAAQ,CAAc,eAAA;AACpC,YAAI,CAAC,WAAW,aAAa,WAAW,IAAI;AAE1C,gBAAM,cAAc;AAAA,YAClB,GAAG;AAAA,YACH,IAAI,WAAW,MAAM,WAAW;AAAA;AAAA,YAChC,SAAS;AAAA,UAAA;AAED,oBAAA,IAAI,YAAY,IAAI,WAAW;AAAA,QAC3C;AAAA,MAAA,CACD;AAGD,YAAM,wBAAwB,gBAAgB,OAAO,CAAS,UAAA,CAAC,MAAM,SAAS;AAC9E,4BAAsB,QAAQ,CAAc,eAAA;AAC1C,cAAM,KAAK,WAAW;AAChB,cAAA,gBAAgB,UAAU,IAAI,EAAE;AAEtC,YAAI,CAAC,eAAe;AAElBA,yBAAA,OAAO,IAAI,6CAA6C,WAAW,IAAI,EAAE;AACzE,oBAAU,IAAI,IAAI;AAAA,YAChB,GAAG;AAAA,YACH,SAAS;AAAA,YACT,WAAW;AAAA;AAAA,UAAA,CACZ;AAAA,QAAA,OACI;AAEL,gBAAM,YAAY,IAAI,KAAK,WAAW,cAAc,WAAW,cAAc,CAAC;AAC9E,gBAAM,YAAY,IAAI,KAAK,cAAc,cAAc,cAAc,cAAc,CAAC;AAEpF,cAAI,YAAY,WAAW;AAEzBA,2BAAA,OAAO,IAAI,2CAA2C,WAAW,IAAI,uBAAuB;AAC5F,sBAAU,IAAI,IAAI;AAAA,cAChB,GAAG;AAAA,cACH,SAAS;AAAA,cACT,WAAW;AAAA;AAAA,YAAA,CACZ;AAAA,UAAA,WACQ,YAAY,WAAW;AAEhCA,2BAAA,OAAO,IAAI,2CAA2C,cAAc,IAAI,uBAAuB;AAAA,UAAA,OAE1F;AAEL,gBAAI,KAAK,2BAA2B,YAAY,aAAa,GAAG;AAC9DA,6BAAA,OAAO,IAAI,sDAAsD,WAAW,IAAI,yBAAyB;AAAA,YAAA,OAEpG;AAELA,6BAAA,OAAO,IAAI,yCAAyC,WAAW,IAAI,yBAAyB;AAAA,YAC9F;AAAA,UACF;AAAA,QACF;AAAA,MAAA,CACD;AAGG,UAAA,cAAc,MAAM,KAAK,UAAU,OAAA,CAAQ,EAC5C,OAAO,CAAA,UAAS,CAAC,MAAM,QAAQ;AAGpB,oBAAA,KAAK,eAAe,WAAW;AAE7CA,qBAAA,OAAO,IAAI,4CAA4C;AAAA,QACrD,YAAY,gBAAgB;AAAA,QAC5B,YAAY,gBAAgB;AAAA,QAC5B,aAAa,YAAY;AAAA,MAAA,CAC1B;AACMA,qBAAAA,OAAA,IAAI,+BAA+B,YAAY,IAAI,QAAM,EAAE,IAAI,EAAE,IAAI,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,OAAO,EAAE,OAAO,QAAQ,EAAE,QAAQ,EAAE,CAAC;AAEhJ,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,eAAe,QAAQ;AAErB,UAAI,WAAW,KAAK,IAAI,GAAG,GAAG,OAAO,OAAO,CAAA,MAAK,EAAE,UAAU,MAAS,EAAE,IAAI,CAAK,MAAA,EAAE,KAAK,CAAC;AAEzF,aAAO,QAAQ,CAAS,UAAA;AACtB,YAAI,MAAM,UAAU,UAAa,MAAM,UAAU,MAAM;AACzC,sBAAA;AACZ,gBAAM,QAAQ;AACdA,gCAAO,IAAI,+BAA+B,QAAQ,cAAc,MAAM,IAAI,EAAE;AAAA,QAC9E;AAAA,MAAA,CACD;AAGM,aAAA,OAAO,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAAA,IAC9D;AAAA;AAAA,IAGA,2BAA2B,QAAQ,QAAQ;AACzC,YAAM,OAAO,CAAC,QAAQ,QAAQ,SAAS,aAAa;AAE7C,aAAA,KAAK,KAAK,CAAO,QAAA;AACtB,eAAO,OAAO,GAAG,MAAM,OAAO,GAAG;AAAA,MAAA,CAClC;AAAA,IACH;AAAA;AAAA,IAGA,gBAAgB,OAAO;AACrB,UAAI,MAAM,SAAS;AAAwB,eAAA;AAC3C,UAAI,MAAM,YACR,MAAM,QAAQ,SAAS,IAAI,KAC3B,MAAM,QAAQ,SAAS,SAAS,KAChC,MAAM,QAAQ,SAAS,SAAS;AACxB,eAAA;AAEH,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,qBAAqB;AACnB,YAAM,gBAAgB;AAAA,QACpB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,MAAA;AAIE,UAAA;AAEF,aAAK,OAAO,OAAO,GAAG,KAAK,OAAO,MAAM;AAEnC,aAAA,OAAO,KAAK,GAAG,aAAa;AAAA,eAC1B,OAAO;AACPA,uBAAAA,OAAA,MAAM,yFAAyF,KAAK;AAE3G,aAAK,SAAS;AAAA,MAChB;AAEA,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA,IAGA,sBAAsB;AAEpB,YAAM,iBAAiB,KAAK,OAAO,KAAK,CAAS,UAAA,MAAM,OAAO,KAAK;AACnE,UAAI,CAAC,gBAAgB;AACnB,aAAK,OAAO,KAAK;AAAA,UACf,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO;AAAA;AAAA,QAAA,CACR;AAAA,MAAA,OACI;AAEL,cAAM,gBAAgB,KAAK,OAAO,UAAU,CAAS,UAAA,MAAM,OAAO,KAAK;AACvE,YAAI,gBAAgB,IAAI;AACjB,eAAA,OAAO,aAAa,EAAE,QAAQ;AAAA,QACrC;AAAA,MACF;AAGA,YAAM,kBAAkB,KAAK,OAAO,KAAK,CAAS,UAAA,MAAM,OAAO,MAAM;AACrE,UAAI,CAAC,iBAAiB;AACpB,aAAK,OAAO,KAAK;AAAA,UACf,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO;AAAA,QAAA,CACR;AAAA,MACH;AAGA,YAAM,2BAA2B,KAAK,OAAO,KAAK,CAAS,UAAA,MAAM,OAAO,gBAAgB;AACxF,UAAI,CAAC,0BAA0B;AAC7B,aAAK,OAAO,KAAK;AAAA,UACf,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO;AAAA,QAAA,CACR;AAAA,MACH;AAGA,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA,IAGA,MAAM,SAAS,MAAM;AACnB,YAAM,YAAYC,WAAAA;AAGd,UAAA,CAAC,UAAU,SAAS;AACtBD,8BAAO,MAAM,2FAA2F;AACxGD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAUM,eAAA;AAAA,MACT;AAEA,UAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACrCC,8BAAO,MAAM,SAAS;AACtBD,sBAAA,MAAI,UAAU,EAAE,OAAO,WAAW,MAAM,QAAQ;AACzC,eAAA;AAAA,MACT;AAGI,UAAA,KAAK,sBAAsB,IAAI,GAAG;AAC7BC,uBAAAA,OAAA,KAAK,mBAAmB,IAAI;AACnCD,sBAAA,MAAI,UAAU,EAAE,OAAO,WAAW,MAAM,QAAQ;AACzC,eAAA;AAAA,MACT;AAEI,UAAA;AAEI,cAAA,YAAY,KAAK;AAGjB,cAAA,cAAcG,cAAAA,GAAS,aAAa,cAAc;AAClD,cAAA,SAAS,MAAM,YAAY,YAAY;AAAA,UAC3C;AAAA,UACA,MAAM;AAAA,UACN,OAAO;AAAA;AAAA,UAEP;AAAA,UACA,UAAUH,cAAA,MAAI,kBAAkB,EAAE,YAAY;AAAA,QAAA,CAC/C;AAEG,YAAA,OAAO,YAAY,GAAG;AAGxB,gBAAM,mBAAmB,KAAK,OAAO,OAAO,CAAK,MAAA,CAAC,EAAE,SAAS;AAC7D,gBAAM,wBAAwB;AACxB,gBAAA,WAAW,KAAK,IAAI,uBAAuB,GAAG,iBAAiB,IAAI,CAAK,MAAA,EAAE,SAAS,CAAC,CAAC;AAE3F,gBAAM,WAAW;AAAA,YACf,GAAG,OAAO;AAAA,YACV,IAAI,OAAO,KAAK,MAAM,OAAO,KAAK;AAAA;AAAA,YAClC,OAAO,OAAO,KAAK,SAAS,WAAW;AAAA;AAAA,UAAA;AAIpC,eAAA,OAAO,KAAK,QAAQ;AAGpB,eAAA,OAAO,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAG1D,eAAK,eAAe;AAGpBC,yBAAA,OAAO,IAAI,wBAAwB;AAAA,YACjC,WAAW,SAAS;AAAA,YACpB,SAAS,OAAO,KAAK;AAAA,YACrB,YAAY,SAAS;AAAA,UAAA,CACtB;AAGD,eAAK,uBAAuB;AAM5BD,wBAAA,MAAI,UAAU,EAAE,OAAO,UAAU,MAAM,WAAW;AAElDC,gCAAO,IAAI,iCAAiC,SAAS,MAAM,eAAe,SAAS,KAAK;AACxFA,gCAAO,IAAI,6CAA6C,KAAK,OAAO,IAAI,QAAM,EAAE,IAAI,EAAE,IAAI,MAAM,EAAE,MAAM,OAAO,EAAE,QAAQ,CAAC;AAE1H,iBAAO,OAAO,KAAK;AAAA,QAAA,OACd;AAELA,gCAAO,MAAM,cAAc,OAAO,QAAQ,aAAa,OAAO,SAAS,GAAG;AAC1ED,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,UAAU;AAAA,YACxB,MAAM;AAAA,YACN,UAAU;AAAA,UAAA,CACX;AACM,iBAAA;AAAA,QACT;AAAA,eACO,OAAO;AAEPC,uBAAAA,OAAA,MAAM,mBAAmB,KAAK;AAE/B,cAAA,iBAAiB,MAAM,YAAY,MAAM,QAAQ,SAAS,SAAS,KAAK,MAAM,QAAQ,SAAS,IAAI;AAEzG,YAAI,gBAAgB;AACXA,yBAAAA,OAAA,IAAI,kBAAkB,IAAI;AAC7B,gBAAA,KAAK,WAAW,KAAK,IAAI;AAE/B,gBAAM,mBAAmB,KAAK,OAAO,OAAO,CAAK,MAAA,CAAC,EAAE,SAAS;AAC7D,gBAAM,wBAAwB;AACxB,gBAAA,WAAW,KAAK,IAAI,uBAAuB,GAAG,iBAAiB,IAAI,CAAK,MAAA,EAAE,SAAS,CAAC,CAAC;AAE3F,gBAAM,WAAW;AAAA,YACf;AAAA,YACA;AAAA,YACA,WAAW;AAAA,YACX,OAAO,WAAW;AAAA,YAChB,WAAW;AAAA,UAAA;AAEV,eAAA,OAAO,KAAK,QAAQ;AAGzB,eAAK,eAAe;AAGpB,eAAK,uBAAuB;AAI5BD,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACT,MAAM;AAAA,UAAA,CACL;AAEHC,gCAAO,IAAI,qCAAqC,SAAS,MAAM,eAAe,SAAS,KAAK;AAC5FA,gCAAO,IAAI,qDAAqD,KAAK,OAAO,IAAI,QAAM,EAAE,IAAI,EAAE,IAAI,MAAM,EAAE,MAAM,OAAO,EAAE,QAAQ,CAAC;AAE3H,iBAAA;AAAA,QAAA,OACA;AACLD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AACM,iBAAA;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGA,MAAM,YAAY,IAAI,MAAM;AAC1B,YAAM,QAAQ,KAAK,OAAO,KAAK,CAAK,MAAA,EAAE,OAAO,EAAE;AAC3C,UAAA,SAAS,CAAC,MAAM,WAAW;AAE7B,YAAI,KAAK,sBAAsB,MAAM,EAAE,GAAG;AACjCC,yBAAAA,OAAA,KAAK,uCAAuC,IAAI;AACvDD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AACD;AAAA,QACF;AAEI,YAAA;AAEI,gBAAA,cAAcG,cAAAA,GAAS,aAAa,cAAc;AACxD,gBAAM,SAAS,MAAM,YAAY,YAAY,IAAI,EAAE,MAAM;AAErD,cAAA,OAAO,YAAY,GAAG;AAExB,kBAAM,OAAO;AACb,iBAAK,eAAe;AACpB,iBAAK,uBAAuB;AAAA,UAAA,OAKvB;AACL,kBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,UAC3C;AAAA,iBACO,OAAO;AACPF,yBAAAA,OAAA,MAAM,WAAW,KAAK;AAG7B,gBAAM,OAAO;AACb,gBAAM,YAAY;AAClB,eAAK,eAAe;AACpB,eAAK,uBAAuB;AAI5BD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,MAAM,YAAY,IAAI;AAEpB,UAAI,CAAC,IAAI;AACPC,8BAAO,MAAM,oCAAoC;AACjDD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACM,eAAA;AAAA,MACT;AAEOC,qBAAAA,OAAA,IAAI,4CAA4C,EAAE;AAGzD,YAAM,QAAQ,KAAK,OAAO,KAAK,CAAK,MAAA,EAAE,OAAO,EAAE;AAC/C,UAAI,CAAC,OAAO;AACHA,uBAAAA,OAAA,MAAM,2CAA2C,EAAE;AAC1DD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACM,eAAA;AAAA,MACT;AAEA,UAAI,MAAM,WAAW;AACZC,uBAAAA,OAAA,MAAM,2CAA2C,EAAE;AAC1DD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACM,eAAA;AAAA,MACT;AAEI,UAAA,SAAS,CAAC,MAAM,WAAW;AAE7B,aAAK,SAAS,KAAK,OAAO,OAAO,CAAK,MAAA,EAAE,OAAO,EAAE;AAGjD,aAAK,uBAAuB;AAE5B,aAAK,eAAe;AAEbC,uBAAAA,OAAA,IAAI,yCAAyC,EAAE;AAGtD,aAAK,sBAAsB,EAAE;AAEtB,eAAA;AAAA,MACT;AAEOA,qBAAAA,OAAA,IAAI,0CAA0C,EAAE;AAChD,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,MAAM,sBAAsB,IAAI;AAC1B,UAAA;AACKA,uBAAAA,OAAA,IAAI,8BAA8B,EAAE;AAErC,cAAA,cAAcE,cAAAA,GAAS,aAAa,cAAc;AACxD,cAAM,SAAS,MAAM,YAAY,YAAY,EAAE;AAE3C,YAAA,OAAO,YAAY,GAAG;AACjBF,yBAAAA,OAAA,IAAI,8BAA8B,EAAE;AAAA,QAAA,OAGtC;AACLA,yBAAA,OAAO,KAAK,8BAA8B,IAAI,UAAU,OAAO,MAAM;AAAA,QAGvE;AAAA,eACO,OAAO;AACdA,uBAAA,OAAO,MAAM,gCAAgC,IAAI,UAAU,KAAK;AAAA,MAGlE;AAAA,IACF;AAAA;AAAA,IAGA,MAAM,iBAAiB,IAAI,OAAO;AAEhC,YAAM,aAAa,KAAK,OAAO,UAAU,CAAS,UAAA,MAAM,OAAO,EAAE;AAEjE,UAAI,eAAe,IAAI;AACjB,YAAA;AAEI,gBAAA,cAAcE,cAAAA,GAAS,aAAa,cAAc;AACxD,gBAAM,SAAS,MAAM,YAAY,YAAY,IAAI,EAAE,OAAO;AAEtD,cAAA,OAAO,YAAY,GAAG;AAEnB,iBAAA,OAAO,UAAU,IAAI;AAAA,cACxB,GAAG,KAAK,OAAO,UAAU;AAAA,cACzB;AAAA,YAAA;AAEF,iBAAK,eAAe;AAIb,mBAAA;AAAA,UAAA,OACF;AACL,kBAAM,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,UAC3C;AAAA,iBACO,OAAO;AACPF,yBAAAA,OAAA,MAAM,aAAa,KAAK;AAG1B,eAAA,OAAO,UAAU,IAAI;AAAA,YACxB,GAAG,KAAK,OAAO,UAAU;AAAA,YACzB;AAAA,YACA,WAAW;AAAA,UAAA;AAEb,eAAK,eAAe;AACb,iBAAA;AAAA,QACT;AAAA,MACF;AAEO,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,cAAc;AAEZD,0BAAI,kBAAkB,QAAQ;AAG9B,WAAK,mBAAmB;AAGjB,aAAA;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MAAA;AAAA,IAEb;AAAA;AAAA,IAGA,MAAM,kBAAkB;AACtB,YAAM,gBAAgB,KAAK,OAAO,OAAO,CAAA,UAAS,MAAM,SAAS;AAE7D,UAAA,cAAc,WAAW,GAAG;AAC9BC,8BAAO,IAAI,wCAAwC;AACnD;AAAA,MACF;AAEAA,qBAAA,OAAO,IAAI,0CAA0C,cAAc,MAAM,iBAAiB;AAE1F,iBAAW,SAAS,eAAe;AAC7B,YAAA;AACI,gBAAA,cAAcE,cAAAA,GAAS,aAAa,cAAc;AAExD,cAAI,MAAM,GAAG,WAAW,QAAQ,GAAG;AAE3B,kBAAA,SAAS,MAAM,YAAY,YAAY;AAAA,cAC3C,MAAM,MAAM;AAAA,cACZ,MAAM,MAAM,QAAQ;AAAA,cACpB,OAAO,MAAM,SAAS;AAAA,YAAA,CACvB;AACG,gBAAA,OAAO,YAAY,GAAG;AAElB,oBAAA,KAAK,OAAO,KAAK;AACvB,qBAAO,MAAM;AACbF,6BAAA,OAAO,IAAI,+CAA+C,MAAM,IAAI,EAAE;AAAA,YACxE;AAAA,UAAA,OACK;AAEL,kBAAM,SAAS,MAAM,YAAY,YAAY,MAAM,IAAI;AAAA,cACrD,MAAM,MAAM;AAAA,cACZ,OAAO,MAAM;AAAA,YAAA,CACd;AACG,gBAAA,OAAO,YAAY,GAAG;AACxB,qBAAO,MAAM;AACbA,6BAAA,OAAO,IAAI,mDAAmD,MAAM,IAAI,EAAE;AAAA,YAC5E;AAAA,UACF;AAAA,iBACO,OAAO;AAEV,cAAA,MAAM,YACR,MAAM,QAAQ,SAAS,OAAO,KAC9B,MAAM,QAAQ,SAAS,KAAK,KAC5B,MAAM,QAAQ,SAAS,WAAW,KAClC,MAAM,QAAQ,SAAS,YAAY,IAClC;AAEDA,2BAAA,OAAO,IAAI,+BAA+B,MAAM,IAAI,qBAAqB;AAGzE,mBAAO,MAAM;AAGT,gBAAA,CAAC,MAAM,WAAW;AACd,oBAAA,aAAa,KAAK,OAAO,UAAU,OAAK,EAAE,OAAO,MAAM,EAAE;AAC/D,kBAAI,eAAe,IAAI;AACrBA,+BAAA,OAAO,IAAI,4BAA4B,MAAM,IAAI,EAAE;AAC9C,qBAAA,OAAO,OAAO,YAAY,CAAC;AAAA,cAClC;AAAA,YACF;AAAA,UAAA,OACK;AAELA,2BAAA,OAAO,MAAM,WAAW,MAAM,MAAM,KAAK;AAAA,UAC3C;AAAA,QACF;AAAA,MACF;AAEA,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA,IAGA,MAAM,YAAY;AAChBA,4BAAO,IAAI,qCAAqC;AAE5C,UAAA,CAAC,KAAK,UAAU;AAClBA,8BAAO,KAAK,uDAAuD;AACnE,eAAO,EAAE,YAAY,OAAO,cAAc,GAAG,QAAQ;MACvD;AAEI,UAAA;AAEF,cAAM,gBAAgB,KAAK,OAAO,OAAO,CAAA,UAAS,MAAM,SAAS;AAC7D,YAAA,cAAc,SAAS,GAAG;AAC5BA,yBAAA,OAAO,IAAI,0BAA0B,cAAc,MAAM,oBAAoB;AAC7E,gBAAM,KAAK;QACb;AAGM,cAAA,cAAcE,cAAAA,GAAS,aAAa,cAAc;AAClD,cAAA,gBAAgB,MAAM,YAAY;AAEpC,YAAA,cAAc,YAAY,GAAG;AAC/B,gBAAM,IAAI,MAAM,cAAc,UAAU,YAAY;AAAA,QACtD;AAEM,cAAA,eAAe,cAAc,QAAQ;AACpCF,uBAAAA,OAAA,IAAI,wCAAwC,YAAY;AAGzD,cAAA,eAAe,KAAK;AACnBA,uBAAAA,OAAA,IAAI,+BAA+B,YAAY;AAGtD,cAAM,YAAY,KAAK,sBAAsB,cAAc,YAAY;AAEvE,YAAI,CAAC,WAAW;AACdA,gCAAO,IAAI,iDAAiD;AAC5D,iBAAO,EAAE,YAAY,OAAO,cAAc,GAAG,QAAQ;QACvD;AAGAA,8BAAO,IAAI,wEAAwE;AACnF,cAAM,aAAa,MAAM,KAAK,wBAAwB,YAAY;AAGlE,aAAK,0BAA0B;AAExBA,uBAAAA,OAAA,IAAI,sCAAsC,UAAU;AACpD,eAAA;AAAA,eAEA,OAAO;AACPA,uBAAAA,OAAA,MAAM,mCAAmC,KAAK;AAC/C,cAAA;AAAA,MACR;AAAA,IACF;AAAA;AAAA,IAGA,wBAAwB;AACtB,YAAM,mBAAmB,KAAK,OAAO,OAAO,CAAK,MAAA,CAAC,EAAE,SAAS;AAEtD,aAAA;AAAA,QACL,OAAO,iBAAiB;AAAA,QACxB,cAAc,KAAK,2BAA2B;AAAA,QAC9C,KAAK,iBAAiB,IAAI,OAAK,EAAE,EAAE,EAAE,KAAK;AAAA,QAC1C,UAAU,KAAK,yBAAyB,gBAAgB;AAAA,MAAA;AAAA,IAE5D;AAAA;AAAA,IAGA,yBAAyB,QAAQ;AACzB,YAAA,UAAU,OACb,IAAI,CAAK,MAAA,GAAG,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,cAAc,EAAE,UAAU,IAAI,EAAE,KAAK,EAAE,EAChF,KAAA,EACA,KAAK,GAAG;AAGX,UAAI,OAAO;AACX,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACjC,cAAA,OAAO,QAAQ,WAAW,CAAC;AACxB,gBAAA,QAAQ,KAAK,OAAQ;AAC9B,eAAO,OAAO;AAAA,MAChB;AACO,aAAA,KAAK,SAAS,EAAE;AAAA,IACzB;AAAA;AAAA,IAGA,sBAAsB,cAAc,cAAc;AAE5C,UAAA,aAAa,UAAU,aAAa,OAAO;AAC7CA,8BAAO,IAAI,2CAA2C,aAAa,OAAO,MAAM,aAAa,KAAK;AAC3F,eAAA;AAAA,MACT;AAGI,UAAA,aAAa,aAAa,aAAa,UAAU;AACnDA,8BAAO,IAAI,8CAA8C,aAAa,UAAU,MAAM,aAAa,QAAQ;AACpG,eAAA;AAAA,MACT;AAGI,UAAA,aAAa,gBAAgB,aAAa,cAAc;AAC1D,cAAM,YAAY,IAAI,KAAK,aAAa,YAAY,EAAE;AACtD,cAAM,YAAY,IAAI,KAAK,aAAa,YAAY,EAAE;AAEtD,YAAI,YAAY,WAAW;AACzBA,gCAAO,IAAI,qCAAqC,aAAa,cAAc,MAAM,aAAa,YAAY;AACnG,iBAAA;AAAA,QACT;AAAA,MACF;AAEO,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,MAAM,wBAAwB,cAAc;AAEpC,YAAA,cAAcE,cAAAA,GAAS,aAAa,cAAc;AACxD,YAAM,SAAS,MAAM,YAAY,aAAa,EAAE,gBAAgB,MAAM;AAElE,UAAA,OAAO,YAAY,GAAG;AACxB,cAAM,IAAI,MAAM,OAAO,UAAU,YAAY;AAAA,MAC/C;AAEM,YAAA,cAAc,OAAO,QAAQ;AAC7B,YAAA,cAAc,KAAK,OAAO;AAGhC,YAAM,aAAa,KAAK,gBAAgB,KAAK,QAAQ,WAAW;AAG1D,YAAA,KAAK,kBAAkB,UAAU;AAEjC,YAAA,aAAa,KAAK,OAAO;AAC/B,YAAM,eAAe,KAAK,IAAI,aAAa,WAAW;AAEtD,WAAK,gBAAe,oBAAI,KAAK,GAAE,YAAY;AAC3C,WAAK,eAAe;AAEb,aAAA;AAAA,QACL,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MAAA;AAAA,IAEZ;AAAA;AAAA,IAGA,MAAM,kBAAkB,SAAS;AAC3B,UAAA;AAEI,cAAA,IAAI,QAAQ,CAAC,YAAY;AAC7B,cAAI,KAAK,WAAW;AAClB,iBAAK,UAAU,MAAM;AACnB,mBAAK,SAAS;AACN;YAAA,CACT;AAAA,UAAA,OACI;AACL,uBAAW,MAAM;AACf,mBAAK,SAAS;AACN;eACP,CAAC;AAAA,UACN;AAAA,QAAA,CACD;AAAA,eACM,OAAO;AACPF,uBAAAA,OAAA,MAAM,yEAAyE,KAAK;AAC3F,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAAA;AAAA,IAGA,4BAA4B;AAC1B,UAAI,eAAe;AAEd,WAAA,OAAO,QAAQ,CAAS,UAAA;AAE3B,YAAI,MAAM,cAAc,CAAC,MAAM,MAAM,CAAC,MAAM,OAAO;AACjDA,yBAAA,OAAO,IAAI,2BAA2B,MAAM,QAAQ,MAAM,EAAE;AAC5D,iBAAO,MAAM;AACb;AAAA,QACF;AAAA,MAAA,CACD;AAED,UAAI,eAAe,GAAG;AACbA,uBAAA,OAAA,IAAI,oBAAoB,YAAY,WAAW;AACtD,aAAK,eAAe;AAAA,MACtB;AAEO,aAAA;AAAA,IACT;AAAA;AAAA,IAGA,MAAM,aAAa;AACb,UAAA,CAAC,KAAK,UAAU;AAClBD,sBAAAA,MAAA,MAAA,OAAA,0BAAY,2BAA2B;AACvC;AAAA,MACF;AAEI,UAAA;AAGF,cAAM,KAAK;AAGX,cAAM,KAAK;AAGXC,8BAAO,IAAI,oCAAoC;AAAA,eAExC,OAAO;AACPA,uBAAAA,OAAA,MAAM,oCAAoC,KAAK;AAEtDD,sBAAAA,MAAA,MAAA,OAAA,0BAAY,mBAAmB;AAAA,MACjC;AAAA,IACF;AAAA;AAAA,IAGA,iBAAiB;AACfA,oBAAA,MAAI,eAAe,UAAU,KAAK,UAAU,KAAK,MAAM,CAAC;AACpDA,oBAAAA,MAAA,eAAe,sBAAsB,KAAK,YAAY;AAAA,IAC5D;AAAA;AAAA,IAGA,iBAAiB;AACfC,4BAAO,IAAI,6CAA6C;AAGxD,WAAK,SAAS,KAAK,OAAO,OAAO,CAAS,UAAA,MAAM,cAAc,IAAI;AAGlE,WAAK,oBAAoB;AAEzB,WAAK,eAAe;AACpB,WAAK,YAAY;AAGjBD,0BAAI,kBAAkB,QAAQ;AAC9BA,0BAAI,kBAAkB,oBAAoB;AAG1C,WAAK,eAAe;AAEpBC,4BAAO,IAAI,mEAAmE;AAAA,IAChF;AAAA;AAAA,IAGA,MAAM,YAAY;AAChBA,4BAAO,IAAI,sCAAsC;AAGjD,WAAK,SAAS;AACd,WAAK,eAAe;AACpB,WAAK,YAAY;AAGjBD,0BAAI,kBAAkB,QAAQ;AAC9BA,0BAAI,kBAAkB,oBAAoB;AAG1C,YAAM,KAAK;AAGX,WAAK,oBAAoB;AAEzBC,4BAAO,IAAI,6CAA6C;AAAA,IAC1D;AAAA;AAAA,IAGA,MAAM,sBAAsB;AAC1BA,4BAAO,IAAI,gDAAgD;AAEvD,UAAA;AAEI,cAAA,iCAAiB;AACvB,cAAM,aAAa,CAAA;AAEnB,aAAK,OAAO,QAAQ,CAAC,OAAO,UAAU;AAChC,cAAA,CAAC,MAAM,WAAW;AACd,kBAAA,YAAY,MAAM,KAAK,YAAY;AACrC,gBAAA,WAAW,IAAI,SAAS,GAAG;AAC7B,yBAAW,KAAK;AAAA,gBACd;AAAA,gBACA;AAAA,gBACA,eAAe,WAAW,IAAI,SAAS,EAAE;AAAA,cAAA,CAC1C;AAAA,YAAA,OACI;AACL,yBAAW,IAAI,WAAW,EAAE,OAAO,MAAO,CAAA;AAAA,YAC5C;AAAA,UACF;AAAA,QAAA,CACD;AAGG,YAAA,WAAW,SAAS,GAAG;AACzBA,yBAAA,OAAO,IAAI,mCAAmC,WAAW,QAAQ,kBAAkB;AAGnF,qBAAW,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAC3C,qBAAW,QAAQ,CAAa,cAAA;AAC9B,iBAAK,OAAO,OAAO,UAAU,OAAO,CAAC;AAAA,UAAA,CACtC;AAAA,QACH;AAGA,cAAM,cAAc,KAAK,eAAe,KAAK,MAAM;AACnD,aAAK,SAAS;AAGd,aAAK,eAAe;AAEpBA,8BAAO,IAAI,sCAAsC;AAG7C,YAAA,WAAW,SAAS,GAAG;AACzBD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MAAM,WAAW,MAAM;AAAA,YAC9B,MAAM;AAAA,YACN,UAAU;AAAA,UAAA,CACX;AAAA,QACH;AAEO,eAAA;AAAA,UACL,mBAAmB,WAAW;AAAA,UAC9B,aAAa,KAAK,OAAO;AAAA,QAAA;AAAA,eAGpB,OAAO;AACPC,uBAAAA,OAAA,MAAM,sCAAsC,KAAK;AACjD,eAAA,EAAE,OAAO,MAAM;MACxB;AAAA,IACF;AAAA;AAAA,IAGA,MAAM,kBAAkB;AAClB,UAAA;AAII,cAAA,SAAS,MAAM,KAAK;AAG1B,YAAI,KAAK,UAAU;AACjB,gBAAM,KAAK;QACb;AAGAA,8BAAO,IAAI,oCAAoC;AAExC,eAAA;AAAA,eAEA,OAAO;AACPA,uBAAAA,OAAA,MAAM,+BAA+B,KAAK;AAEjDD,sBAAAA,MAAA,MAAA,OAAA,0BAAY,qBAAqB;AAE3B,cAAA;AAAA,MACR;AAAA,IACF;AAAA;AAAA,IAGA,yBAAyB;AAEnB,UAAA;AAEG,aAAA,aAAa,KAAK,UAAU,MAAM;AAErCC,yBAAA,OAAO,IAAI,sEAAsE,KAAK,OAAO,MAAM;AAAA,QAAA,CACpG;AAID,YAAI,KAAK,UAAU,KAAK,OAAO,SAAS,GAAG;AAEnC,gBAAA,gBAAgB,KAAK,OAAO,IAAI,YAAU,EAAE,GAAG,MAAQ,EAAA;AAC7D,eAAK,SAAS;AAAA,QAChB;AAEAA,uBAAA,OAAO,IAAI,gEAAgE,KAAK,OAAO,MAAM;AAAA,eACtF,OAAO;AACPA,uBAAAA,OAAA,MAAM,wCAAwC,KAAK;AAE1D,aAAK,SAAS,CAAC,GAAI,KAAK,UAAU,CAAG,CAAA;AAAA,MACvC;AAAA,IACF;AAAA;AAAA,IAKA,iBAAiB;AAMfA,4BAAO,IAAI,+BAA+B;AAC1CA,qBAAA,OAAO,IAAI,uBAAuB,KAAK,OAAO,MAAM;AAEpD,WAAK,OAAO,QAAQ,CAAC,OAAO,UAAU;AAC7BA,uBAAAA,OAAA,IAAI,mBAAmB,KAAK,KAAK;AAAA,UACtC,IAAI,MAAM;AAAA,UACV,KAAK,MAAM;AAAA,UACX,MAAM,MAAM;AAAA,UACZ,WAAW,MAAM;AAAA,UACjB,WAAW,MAAM;AAAA,UACjB,SAAS,MAAM;AAAA,QAAA,CAChB;AAAA,MAAA,CACF;AAED,YAAM,gBAAgB,KAAK,OAAO,OAAO,CAAA,MAAK,EAAE,SAAS;AAClDA,qBAAAA,OAAA,IAAI,yBAAyB,cAAc,MAAM;AAEpD,UAAA,cAAc,SAAS,GAAG;AAC5BA,uBAAA,OAAO,IAAI,yBAAyB,cAAc,IAAI,CAAM,OAAA;AAAA,UAC1D,IAAI,EAAE;AAAA,UACN,MAAM,EAAE;AAAA,UACR,CAAC;AAAA,MACL;AAEO,aAAA;AAAA,QACL,YAAY,KAAK,OAAO;AAAA,QACxB,cAAc,cAAc;AAAA,QAC5B,QAAQ,KAAK,OAAO,IAAI,CAAM,OAAA;AAAA,UAC5B,IAAI,EAAE;AAAA,UACN,KAAK,EAAE;AAAA,UACP,MAAM,EAAE;AAAA,UACR,WAAW,EAAE;AAAA,UACb,WAAW,EAAE;AAAA,QAAA,EACb;AAAA,MAAA;AAAA,IAEN;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM,6BAA6B,cAAc;AAC3C,UAAA;AACFA,uBAAA,OAAO,IAAI,+BAA+B,aAAa,QAAQ,KAAK;AAGpE,cAAM,aAAa,KAAK,gBAAgB,KAAK,QAAQ,YAAY;AAGjE,aAAK,SAAS;AAGd,aAAK,eAAe;AAGpB,aAAK,uBAAuB;AAE5BA,8BAAO,IAAI,2BAA2B;AAAA,eAE/B,OAAO;AACPA,uBAAAA,OAAA,MAAM,8BAA8B,KAAK;AAAA,MAClD;AAAA,IACF;AAAA;AAAA,IAGA,uBAAuB;AACrBA,4BAAO,IAAI,0CAA0C;AAGrD,YAAM,gBAAgB,KAAK,OAAO,OAAO,CAAA,MAAK,EAAE,SAAS;AACzD,YAAM,aAAa,KAAK,OAAO,OAAO,CAAK,MAAA,CAAC,EAAE,SAAS;AAGzC,oBAAA,QAAQ,CAAC,OAAO,UAAU;AAClC,YAAA,MAAM,OAAO,OAAO;AACtB,gBAAM,QAAQ;AAAA,QAAA,WACL,MAAM,OAAO,QAAQ;AAC9B,gBAAM,QAAQ;AAAA,QAAA,WACL,MAAM,OAAO,kBAAkB;AACxC,gBAAM,QAAQ;AAAA,QAAA,OACT;AACL,gBAAM,QAAQ;AAAA,QAChB;AAAA,MAAA,CACD;AAGU,iBAAA,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,QAAQ,EAAE,SAAS,IAAI;AAClD,iBAAA,QAAQ,CAAC,OAAO,UAAU;AACnC,cAAM,QAAQ,IAAI;AAAA,MAAA,CACnB;AAGD,WAAK,SAAS,CAAC,GAAG,eAAe,GAAG,UAAU;AACzC,WAAA,OAAO,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAG1D,WAAK,eAAe;AAEpBA,qBAAA,OAAO,IAAI,yCAAyC,KAAK,OAAO,IAAI,CAAM,OAAA;AAAA,QACxE,IAAI,EAAE;AAAA,QACN,MAAM,EAAE;AAAA,QACR,OAAO,EAAE;AAAA,QACT,WAAW,EAAE;AAAA,QACb,CAAC;AAGH,WAAK,uBAAuB;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,eAAe,OAAO;AACpB,YAAM,gBAAgB,KAAK,OAAO,UAAU,CAAK,MAAA,EAAE,OAAO,MAAM,MAAM,EAAE,QAAQ,MAAM,GAAG;AACzF,UAAI,kBAAkB,IAAI;AACxB,aAAK,OAAO,KAAK;AAAA,UACf,GAAG;AAAA,UACH,IAAI,MAAM,MAAM,MAAM;AAAA;AAAA,QAAA,CACvB;AACI,aAAA,OAAO,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAC1D,aAAK,eAAe;AACbA,uBAAAA,OAAA,IAAI,4BAA4B,MAAM,IAAI;AAAA,MACnD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,kBAAkB,OAAO;AACvB,YAAM,QAAQ,KAAK,OAAO,UAAU,CAAK,MAAA,EAAE,OAAO,MAAM,MAAM,EAAE,QAAQ,MAAM,GAAG;AACjF,UAAI,UAAU,IAAI;AACX,aAAA,OAAO,KAAK,IAAI;AAAA,UACnB,GAAG;AAAA,UACH,IAAI,MAAM,MAAM,MAAM;AAAA;AAAA,QAAA;AAEnB,aAAA,OAAO,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAC1D,aAAK,eAAe;AACbA,uBAAAA,OAAA,IAAI,6BAA6B,MAAM,IAAI;AAAA,MACpD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,oBAAoB,SAAS;AACrB,YAAA,QAAQ,KAAK,OAAO,UAAU,CAAA,MAAK,EAAE,OAAO,WAAW,EAAE,QAAQ,OAAO;AAC9E,UAAI,UAAU,IAAI;AAChB,cAAM,YAAY,KAAK,OAAO,KAAK,EAAE;AAChC,aAAA,OAAO,OAAO,OAAO,CAAC;AAC3B,aAAK,eAAe;AACbA,uBAAAA,OAAA,IAAI,6BAA6B,SAAS;AAAA,MACnD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,sBAAsB,MAAM,YAAY,MAAM;AAC5C,UAAI,CAAC;AAAa,eAAA;AAEZ,YAAA,YAAY,KAAK;AAChB,aAAA,KAAK,OAAO,KAAK,CAAS,UAAA;AAE/B,YAAI,cAAc,MAAM,OAAO,aAAa,MAAM,QAAQ,YAAY;AAC7D,iBAAA;AAAA,QACT;AACO,eAAA,MAAM,KAAK,YAAA,MAAkB;AAAA,MAAA,CACrC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA,IAKA,iBAAiB,OAAO;AACtB,YAAM,gBAAgB,KAAK,OAAO,UAAU,CAAK,MAAA,EAAE,OAAO,MAAM,MAAM,EAAE,QAAQ,MAAM,GAAG;AACzF,UAAI,kBAAkB,IAAI;AACxB,aAAK,OAAO,KAAK;AAAA,UACf,GAAG;AAAA,UACH,IAAI,MAAM,OAAO,MAAM;AAAA,QAAA,CACxB;AACI,aAAA,OAAO,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAC1D,aAAK,eAAe;AACpBA,uBAAA,OAAO,IAAI,yBAAyB,MAAM,IAAI,EAAE;AAAA,MAClD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,oBAAoB,OAAO;AACzB,YAAM,QAAQ,KAAK,OAAO,UAAU,CAAK,MAAA,EAAE,OAAO,MAAM,MAAM,EAAE,QAAQ,MAAM,GAAG;AACjF,UAAI,UAAU,IAAI;AACX,aAAA,OAAO,KAAK,IAAI;AAAA,UACnB,GAAG;AAAA,UACH,IAAI,MAAM,OAAO,MAAM;AAAA,QAAA;AAEpB,aAAA,OAAO,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAC1D,aAAK,eAAe;AACpBA,uBAAA,OAAO,IAAI,yBAAyB,MAAM,IAAI,EAAE;AAAA,MAClD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKA,gBAAgB,SAAS;AACjB,YAAA,QAAQ,KAAK,OAAO,UAAU,CAAA,MAAK,EAAE,OAAO,WAAW,EAAE,QAAQ,OAAO;AAC9E,UAAI,UAAU,IAAI;AAChB,cAAM,eAAe,KAAK,OAAO,OAAO,OAAO,CAAC,EAAE,CAAC;AACnD,aAAK,eAAe;AACpBA,uBAAA,OAAO,IAAI,yBAAyB,aAAa,IAAI,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AACF,CAAC;;"}