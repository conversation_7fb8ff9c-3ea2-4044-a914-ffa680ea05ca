// 评论管理云对象
const db = uniCloud.database();
const dbCmd = db.command;

module.exports = {
  // 云对象的初始化方法
  _before: function() {
    // 获取客户端信息
    const clientInfo = this.getClientInfo();
    this.clientInfo = clientInfo;

    // 获取用户ID - 使用内置方法或JWT解析
    const uniIdToken = this.getUniIdToken();
    this.uid = (uniIdToken && uniIdToken.uid) || (clientInfo.uniIdToken && clientInfo.uniIdToken.uid);
    
    if (!this.uid) {
      const token = clientInfo.uniIdToken || clientInfo.token;
      console.log('[comment-center] Trying to parse token:', token ? 'TOKEN_EXISTS' : 'NO_TOKEN');
      if (token) {
        try {
          const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
          console.log('[comment-center] Parsed token payload:', payload);
          this.uid = payload.uid;
        } catch (e) {
          console.error('[comment-center] Token parse error:', e);
        }
      }
    }
    
    console.log('[comment-center] Final uid:', this.uid);
    
    // 如果需要登录才能操作，在这里统一校验
    const methodsWithoutAuth = ['getCommentsByWish', 'ensureCollectionsExist']; // 不需要登录的方法
    if (!methodsWithoutAuth.includes(this.getMethodName()) && !this.uid) {
      console.error('[comment-center] No uid found, user not authenticated');
      throw new Error('需要登录后才能操作');
    }
    
    console.log('[comment-center] Authentication successful for uid:', this.uid);
  },

  /**
   * 🚀 发送数据同步推送通知（独立推送，减少数据传输）
   */
  async _sendSyncPush(action, dataId, data = null) {
    try {
      // 调用推送云对象
      const syncPush = uniCloud.importObject('sync-push')

      // 🚀 只推送评论数据变更，减少不必要的数据传输
      const result = await syncPush.pushDataSync({
        userId: this.uid,
        dataType: 'comment',
        action,
        dataId,
        data
      })

      if (result.errCode === 0) {
        console.log(`[comment-center] 推送通知发送成功: ${action}`)
      } else {
        console.warn(`[comment-center] 推送通知发送失败: ${result.errMsg}`)
      }
    } catch (error) {
      console.error('[comment-center] 发送推送通知失败:', error)
      // 推送失败不影响主要业务逻辑
    }
  },
  
  /**
   * 确保必需的集合存在
   * 这个方法可以在应用启动时调用，确保数据库集合已创建
   */
  async ensureCollectionsExist() {
    console.log('[comment-center] Ensuring collections exist...');
    
    const collections = ['wish_comments', 'messages'];
    const results = {};
    
    for (const collectionName of collections) {
      try {
        await db.collection(collectionName).limit(1).get();
        console.log(`[comment-center] Collection ${collectionName} exists`);
        results[collectionName] = 'exists';
      } catch (e) {
        console.log(`[comment-center] Creating collection ${collectionName}...`);
        try {
          // 创建一个临时记录来初始化集合，然后立即删除
          const tempRes = await db.collection(collectionName).add({
            _temp: true,
            createDate: new Date()
          });
          await db.collection(collectionName).doc(tempRes.id).remove();
          console.log(`[comment-center] Collection ${collectionName} created successfully`);
          results[collectionName] = 'created';
        } catch (createError) {
          console.error(`[comment-center] Failed to create collection ${collectionName}:`, createError);
          results[collectionName] = 'failed';
        }
      }
    }
    
    return {
      errCode: 0,
      data: results
    };
  },
  
  /**
   * 获取心愿的评论列表
   * @param {String} wishId - 心愿ID
   * @param {Object} options - 查询选项
   */
  async getCommentsByWish(wishId, options = {}) {
    console.log('[comment-center] getCommentsByWish called with wishId:', wishId);
    
    if (!wishId) {
      throw new Error('心愿ID不能为空');
    }
    
    const {
      page = 1,
      pageSize = 20,
      sortBy = 'createDate',
      sortOrder = 'desc',
      lastSyncTime = null // 上次同步时间，用于增量同步
    } = options;
    
    try {
      console.log('[comment-center] Querying comments for wishId:', wishId);
      
      // 构建查询条件 - 使用 wishId 而不是 wish_id
      const where = {
        wishId: wishId,
        isDeleted: false
      };
      
      // 如果提供了上次同步时间，只获取该时间之后更新的评论
      if (lastSyncTime) {
        where.updateDate = dbCmd.gte(new Date(lastSyncTime));
        console.log('[comment-center] Using incremental sync since:', lastSyncTime);
      }
      
      // 确保 wish_comments 集合存在
      try {
        await db.collection('wish_comments').limit(1).get();
        console.log('[comment-center] wish_comments collection exists');
      } catch (e) {
        console.log('[comment-center] wish_comments collection does not exist, returning empty result');
        return {
          errCode: 0,
          data: [],
          total: 0,
          page,
          pageSize
        };
      }
      
      // 先进行简单查询，避免复杂聚合
      const res = await db.collection('wish_comments')
        .where(where)
        .orderBy(sortBy, sortOrder === 'desc' ? 'desc' : 'asc')
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .get();
      
      console.log('[comment-center] Comments query result:', res);
      
      // 获取总数
      const countRes = await db.collection('wish_comments')
        .where(where)
        .count();
      
      console.log('[comment-center] Comments count result:', countRes);
      
      // 如果有评论，获取用户信息
      const commentsWithUserInfo = [];
      for (const comment of res.data) {
        try {
          // 获取用户信息
          const userRes = await db.collection('uni-id-users')
            .where({ _id: comment.userId })
            .field({ nickname: true, avatar: true })
            .get();
          
          const userInfo = userRes.data[0] || {};
          
          commentsWithUserInfo.push({
            ...comment,
            user_info: {
              nickname: userInfo.nickname || '匿名用户',
              avatar: userInfo.avatar || ''
            }
          });
        } catch (userError) {
          console.error('[comment-center] Error fetching user info for comment:', comment._id, userError);
          // 即使获取用户信息失败，也添加评论（使用默认用户信息）
          commentsWithUserInfo.push({
            ...comment,
            user_info: {
              nickname: '匿名用户',
              avatar: ''
            }
          });
        }
      }
      
      return {
        errCode: 0,
        data: commentsWithUserInfo,
        total: countRes.total,
        page,
        pageSize
      };
    } catch (error) {
      console.error('[comment-center] 获取评论列表失败:', error);
      console.error('[comment-center] Error stack:', error.stack);
      throw new Error('获取评论列表失败: ' + error.message);
    }
  },
  
  /**
   * 创建评论
   * @param {Object} commentData - 评论数据
   */
  async createComment(commentData) {
    console.log('[comment-center] createComment called with:', commentData);
    
    if (!commentData.wishId || !commentData.content) {
      throw new Error('心愿ID和评论内容不能为空');
    }
    
    try {
      // 检查心愿是否存在
      const wishRes = await db.collection('wishes')
        .where({ _id: commentData.wishId })
        .get();
      
      if (wishRes.data.length === 0) {
        // 🔧 多设备冲突处理：心愿可能已被其他设备删除，静默返回错误
        console.log('[comment-center] 多设备评论冲突：心愿不存在，可能已被其他设备删除，静默处理');
        return {
          errCode: 404,
          errMsg: '心愿已被删除',
          data: null
        };
      }
      
      const wish = wishRes.data[0];
      
      // 权限检查：只有公开或朋友可见的心愿才能评论
      if (wish.permission === 'private' && wish.userId !== this.uid) {
        throw new Error('无权限评论此心愿');
      }
      
      // 如果是回复评论，检查被回复的评论是否存在
      if (commentData.replyTo) {
        const replyToRes = await db.collection('wish_comments')
          .where({ 
            _id: commentData.replyTo,
            isDeleted: false
          })
          .get();
        
        if (replyToRes.data.length === 0) {
          throw new Error('被回复的评论不存在');
        }
      }
      
      // 准备评论数据
      const comment = {
        wishId: commentData.wishId,
        userId: this.uid,
        content: commentData.content.trim(),
        image: commentData.image || [],
        replyTo: commentData.replyTo || null,
        isDeleted: false,
        createDate: new Date(),
        updateDate: new Date()
      };
      
      console.log('[comment-center] Prepared comment data:', comment);
      
      // 确保必需的集合存在，避免在事务中创建集合
      const collectionsToEnsure = ['wish_comments', 'messages'];
      for (const collectionName of collectionsToEnsure) {
        try {
          await db.collection(collectionName).limit(1).get();
        } catch (e) {
          console.log(`[comment-center] Creating ${collectionName} collection...`);
          // 创建一个临时记录来初始化集合，然后立即删除
          const tempRes = await db.collection(collectionName).add({
            _temp: true,
            createDate: new Date()
          });
          await db.collection(collectionName).doc(tempRes.id).remove();
          console.log(`[comment-center] ${collectionName} collection created`);
        }
      }
      
      // 分步执行操作，避免事务中的集合创建问题
      let commentRes;
      try {
        // 第一步：插入评论
        commentRes = await db.collection('wish_comments').add(comment);
        console.log('[comment-center] Comment added with ID:', commentRes.id);
        
        // 第二步：更新心愿的评论数量
        const updateWishRes = await db.collection('wishes')
          .where({ _id: commentData.wishId })
          .update({
            commentCount: dbCmd.inc(1),
            updateDate: new Date()
          });
        console.log('[comment-center] Wish comment count updated:', updateWishRes);
        
        // 第三步：如果不是心愿作者自己评论，创建消息通知
        if (wish.userId !== this.uid) {
          // 获取评论者信息
          const userRes = await db.collection('uni-id-users')
            .where({ _id: this.uid })
            .get();
          
          const user = userRes.data[0] || {};
          console.log('[comment-center] User info for notification:', user);
          
          // 创建通知消息
          const messageRes = await db.collection('messages').add({
            userId: wish.userId,
            type: 'comment',
            title: `新评论: ${user.nickname || '匿名用户'}`,
            content: comment.content,
            wishId: commentData.wishId,
            commentId: commentRes.id,
            fromUserId: this.uid,
            isRead: false,
            createDate: new Date()
          });
          console.log('[comment-center] Notification message created:', messageRes.id);
        }
        
        console.log('[comment-center] All operations completed successfully');
        
        // 获取完整的评论信息（包含用户信息）
        const fullCommentRes = await db.collection('wish_comments')
          .aggregate()
          .match({ _id: commentRes.id })
          .lookup({
            from: 'uni-id-users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user_info'
          })
          .addFields({
            user_info: {
              $arrayElemAt: ['$user_info', 0]
            }
          })
          .project({
            _id: 1,
            wishId: 1,
            userId: 1,
            content: 1,
            image: 1,
            replyTo: 1,
            createDate: 1,
            updateDate: 1,
            'user_info.nickname': 1,
            'user_info.avatar': 1
          })
          .end();
        
        const result = {
          errCode: 0,
          data: fullCommentRes.data[0] || { ...comment, _id: commentRes.id }
        };

        console.log('[comment-center] Comment created successfully:', result);

        // 🚀 发送推送通知
        try {
          if (typeof this._sendSyncPush === 'function') {
            await this._sendSyncPush('create', commentRes.id, result.data);
          } else {
            console.warn('[comment-center] _sendSyncPush method not available, skipping push notification');
          }
        } catch (pushError) {
          console.error('[comment-center] Push notification failed:', pushError);
          // 推送失败不影响主要业务逻辑
        }

        return result;
      } catch (e) {
        console.error('[comment-center] Operation error:', e);
        
        // 如果评论已经创建但后续操作失败，尝试清理
        if (commentRes && commentRes.id) {
          try {
            console.log('[comment-center] Attempting to cleanup failed comment:', commentRes.id);
            await db.collection('wish_comments').doc(commentRes.id).remove();
            console.log('[comment-center] Failed comment cleaned up');
          } catch (cleanupError) {
            console.error('[comment-center] Failed to cleanup comment:', cleanupError);
          }
        }
        
        throw e;
      }
    } catch (error) {
      console.error('[comment-center] 创建评论失败:', error);
      console.error('[comment-center] Error stack:', error.stack);
      throw new Error(error.message || '评论发布失败');
    }
  },
  

  
  /**
   * 删除评论
   * @param {String} commentId - 评论ID
   */
  async deleteComment(commentId) {
    if (!commentId) {
      throw new Error('评论ID不能为空');
    }
    
    try {
      // 查询评论
      const commentRes = await db.collection('wish_comments')
        .where({ _id: commentId })
        .get();
      
      if (commentRes.data.length === 0) {
        throw new Error('评论不存在');
      }
      
      const comment = commentRes.data[0];
      
      // 权限检查：只有评论作者或心愿作者可以删除评论
      const wishRes = await db.collection('wishes')
        .where({ _id: comment.wishId })
        .get();
      
      if (wishRes.data.length === 0) {
        // 🔧 多设备冲突处理：心愿可能已被其他设备删除，静默返回错误
        console.log('[comment-center] 多设备删除评论冲突：关联心愿不存在，可能已被其他设备删除，静默处理');
        return {
          errCode: 404,
          errMsg: '关联心愿已被删除',
          data: null
        };
      }
      
      const wish = wishRes.data[0];
      
      const isCommentAuthor = comment.userId === this.uid;
      const isWishAuthor = wish.userId === this.uid;
      
      if (!isCommentAuthor && !isWishAuthor) {
        throw new Error('无权限删除此评论');
      }
      
      // 硬删除评论记录
      const deleteResult = await db.collection('wish_comments')
        .doc(commentId)
          .remove();
        
      if (deleteResult.deleted > 0) {
        // 🚀 发送推送通知
        try {
          if (typeof this._sendSyncPush === 'function') {
            await this._sendSyncPush('delete', commentId, { wishId: comment.wishId });
          } else {
            console.warn('[comment-center] _sendSyncPush method not available, skipping push notification');
          }
        } catch (pushError) {
          console.error('[comment-center] Push notification failed:', pushError);
          // 推送失败不影响主要业务逻辑
        }

        return {
          errCode: 0,
          message: '评论删除成功'
        };
      } else {
        throw new Error('删除失败，没有记录被删除');
      }
      
    } catch (error) {
      console.error('删除评论失败:', error);
      throw new Error(error.message || '评论删除失败');
    }
  },
  
  /**
   * 更新评论
   * @param {String} commentId - 评论ID
   * @param {Object} updates - 更新数据
   */
  async updateComment(commentId, updates) {
    if (!commentId) {
      throw new Error('评论ID不能为空');
    }
    
    try {
      // 检查评论是否存在且属于当前用户
      const commentRes = await db.collection('wish_comments')
        .where({
          _id: commentId,
          userId: this.uid,
          isDeleted: false
        })
        .get();
      
      if (commentRes.data.length === 0) {
        throw new Error('评论不存在或无权限修改');
      }
      
      // 准备更新数据
      const updateData = {
        updateDate: new Date()
      };
      
      // 允许更新的字段
      if (updates.content && updates.content.trim()) {
        updateData.content = updates.content.trim();
      }
      
      if (updates.image && Array.isArray(updates.image)) {
        updateData.image = updates.image;
      }
      
      // 更新评论
      const res = await db.collection('wish_comments')
        .where({ _id: commentId })
        .update(updateData);
      
      return {
        errCode: 0,
        updated: res.updated
      };
    } catch (error) {
      console.error('更新评论失败:', error);
      throw new Error(error.message || '评论更新失败');
    }
  },
  
  /**
   * 获取评论详情
   * @param {String} commentId - 评论ID
   */
  async getCommentDetail(commentId) {
    if (!commentId) {
      throw new Error('评论ID不能为空');
    }
    
    try {
      const res = await db.collection('wish_comments')
        .aggregate()
        .match({ 
          _id: commentId,
          isDeleted: false
        })
        .lookup({
          from: 'uni-id-users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user_info'
        })
        .addFields({
          user_info: {
            $arrayElemAt: ['$user_info', 0]
          }
        })
        .project({
          _id: 1,
          wishId: 1,
          userId: 1,
          content: 1,
          image: 1,
          replyTo: 1,
          createDate: 1,
          updateDate: 1,
          'user_info.nickname': 1,
          'user_info.avatar': 1
        })
        .end();
      
      if (res.data.length === 0) {
        throw new Error('评论不存在');
      }
      
      return {
        errCode: 0,
        data: res.data[0]
      };
    } catch (error) {
      console.error('获取评论详情失败:', error);
      throw new Error(error.message || '获取评论详情失败');
    }
  },

  /**
   * 根据ID获取单个评论
   * @param {string} commentId - 评论ID
   */
  async getCommentById(commentId) {
    if (!commentId) {
      return {
        errCode: 400,
        errMsg: '评论ID不能为空'
      }
    }

    try {
      const result = await db.collection('wish_comments')
        .where({ _id: commentId })
        .get()

      if (result.data.length === 0) {
        return {
          errCode: 404,
          errMsg: '评论不存在'
        }
      }

      return {
        errCode: 0,
        errMsg: '获取成功',
        data: result.data[0]
      }
    } catch (error) {
      console.error('[comment-center] getCommentById error:', error)
      return {
        errCode: 500,
        errMsg: '获取评论失败: ' + error.message
      }
    }
  }
};