<view class="group-selector"><button open-type="share" share-type="1" bindtap="{{c}}" bindlongpress="{{d}}" class="{{['share-group-btn', e && 'active', f && 'show-tooltip']}}" data-group-id="{{g}}" data-group-name="{{h}}" data-share-type="{{'group'}}"><image src="{{a}}" class="share-icon"></image><view class="share-btn-ripple"></view><view wx:if="{{b}}" class="share-tooltip"> 分享此分组及心愿 </view></button><button wx:if="{{i}}" class="debug-sync-btn" bindtap="{{k}}"><text class="debug-sync-text">{{j}}</text></button><scroll-view class="group-scroll-view" scroll-x="true" show-scrollbar="false"><view class="group-list"><view wx:for="{{l}}" wx:for-item="group" wx:key="b" class="{{['group-item', group.c && 'active', group.d && 'is-default']}}" bindtap="{{group.e}}" bindlongpress="{{group.f}}" data-id="{{group.g}}"><text class="group-text">{{group.a}}</text></view><view class="group-item add-group" bindtap="{{m}}"><view class="add-icon-wrapper"><text class="plus-icon">+</text></view></view><view class="spacing-placeholder"></view></view></scroll-view></view>