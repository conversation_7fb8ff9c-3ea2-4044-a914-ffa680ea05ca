"use strict";
const common_vendor = require("../../common/vendor.js");
const useAddressStore = common_vendor.defineStore("address", {
  state: () => ({
    addressList: [],
    defaultAddress: null,
    loading: false,
    lastSyncTime: null
    // 最后同步时间
  }),
  getters: {
    // 获取默认地址
    getDefaultAddress: (state) => {
      return state.addressList.find((addr) => addr.isDefault) || null;
    },
    // 获取所有有效地址
    getValidAddresses: (state) => {
      return state.addressList.filter((addr) => addr.status !== 0);
    }
  },
  actions: {
    /**
     * 设置地址列表
     */
    setAddressList(list) {
      this.addressList = list || [];
    },
    /**
     * 添加地址
     */
    addAddress(address) {
      if (address) {
        this.addressList.push(address);
      }
    },
    /**
     * 更新地址
     */
    updateAddress(addressId, updatedData) {
      const index = this.addressList.findIndex((addr) => addr._id === addressId);
      if (index !== -1) {
        this.addressList[index] = { ...this.addressList[index], ...updatedData };
      }
    },
    /**
     * 删除地址
     */
    removeAddress(addressId) {
      this.addressList = this.addressList.filter((addr) => addr._id !== addressId);
    },
    /**
     * 设置默认地址
     */
    setDefaultAddress(addressId) {
      this.addressList.forEach((addr) => {
        addr.isDefault = false;
      });
      const targetAddress = this.addressList.find((addr) => addr._id === addressId);
      if (targetAddress) {
        targetAddress.isDefault = true;
        this.defaultAddress = targetAddress;
      }
    },
    /**
     * 获取地址列表
     */
    async fetchAddressList() {
      try {
        this.loading = true;
        const addressCenter = common_vendor.nr.importObject("address-center");
        const result = await addressCenter.getAddressList();
        if (result.code === 0) {
          this.setAddressList(result.data.list);
          return result.data;
        } else {
          throw new Error(result.message || "获取地址列表失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-profile/store/address.js:90", "获取地址列表失败:", error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
    /**
     * 获取默认地址（从云端）
     */
    async fetchDefaultAddress() {
      try {
        const addressCenter = common_vendor.nr.importObject("address-center");
        const result = await addressCenter.getDefaultAddress();
        if (result.code === 0) {
          this.defaultAddress = result.data;
          return result.data;
        } else {
          this.defaultAddress = null;
          return null;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpkg-profile/store/address.js:113", "获取默认地址失败:", error);
        this.defaultAddress = null;
        return null;
      }
    },
    /**
     * 清空地址数据
     */
    clearAddressData() {
      this.addressList = [];
      this.defaultAddress = null;
    }
  }
});
exports.useAddressStore = useAddressStore;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/subpkg-profile/store/address.js.map
