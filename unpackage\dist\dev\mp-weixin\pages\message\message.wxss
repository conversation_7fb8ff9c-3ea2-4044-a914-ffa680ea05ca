/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.message-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
}
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  background-color: #fff;
}
.message-header .title {
  font-size: 32rpx;
  font-weight: 500;
}
.message-header .read-all {
  font-size: 28rpx;
  color: #8a2be2;
}
.message-list {
  padding: 20rpx;
}
.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100rpx;
}
.empty-list .empty-text {
  font-size: 28rpx;
  color: #999;
}
.message-item {
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.message-item.unread {
  border-left: 4rpx solid #8a2be2;
}
.message-item.comment-message {
  background-color: #fafafa;
}
.message-item .message-dot {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  background-color: #8a2be2;
  border-radius: 50%;
  top: 38rpx;
  right: 30rpx;
}
.message-item .message-header-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.message-item .message-header-row .comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
}
.message-item .message-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}
.message-item .message-wish-title {
  font-size: 28rpx;
  color: #8a2be2;
  margin-bottom: 12rpx;
}
.message-item .message-content {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}
.message-item .message-content.comment-content {
  background-color: #f0f0f0;
  padding: 16rpx;
  border-radius: 8rpx;
  color: #333;
}
.message-item .message-time {
  font-size: 24rpx;
  color: #999;
  text-align: right;
}
.message-item .message-action {
  font-size: 24rpx;
  color: #8a2be2;
  text-align: right;
  margin-top: 10rpx;
}