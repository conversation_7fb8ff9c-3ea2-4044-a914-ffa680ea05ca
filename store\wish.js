/**
 * 心愿数据管理 Store - 备份版本
 */

import { defineStore } from 'pinia'
import { useUserStore } from './user.js'
import { useGroupStore } from './group.js'
import { loadingManager } from '../utils/loadingManager.js'
import { devLog } from '../utils/devLog.js'

// 错误代码常量
const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  SERVER_ERROR: 'SERVER_ERROR'
}

// 错误处理工具类
const ErrorHandler = {
  getUserFriendlyMessage(error) {
    if (typeof error === 'string') return error
    return error.message || error.errMsg || '未知错误'
  },
  
  isNetworkError(error) {
    if (error.errCode === ERROR_CODES.NETWORK_ERROR) return true
    if (error.code === 'NETWORK_ERROR') return true
    return false
  },
  
  isVersionConflictError(error) {
    return error && error.message && (
      error.message.includes('数据版本冲突') ||
      error.message.includes('逻辑时钟冲突') ||
      error.message.includes('version conflict') ||
      error.message.includes('conflict')
    )
  },

  showError(error, title = '操作失败', context = 'unknown') {
    const message = this.getUserFriendlyMessage(error)
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
    console.error(`${title}:`, error)
  }
}

export const useWishStore = defineStore('wish', {
  state: () => ({
    wishList: [],
    currentGroupId: 'all',
    isLoading: false,
    lastSyncTime: null,
    isOnline: true
  }),

  getters: {
    activeWishes: (state) => {
      return state.wishList.filter(wish => !wish._deleted && !wish.isCompleted)
    }
  },

  actions: {
    async initWishList() {
      console.log('[wishStore] Initializing wish list...')
      this.isLoading = true
      
      try {
        // 从本地存储加载数据
        const storedWishes = uni.getStorageSync('wishList')
        if (storedWishes) {
          const parsed = JSON.parse(storedWishes)
          this.wishList = parsed.map(wish => ({
            ...wish,
            id: wish.id || wish._id
          }))
        }
        
        console.log('[wishStore] Wish list initialization completed')
      } catch (error) {
        console.error('[wishStore] Failed to initialize wish list:', error)
      } finally {
        this.isLoading = false
      }
    }
  }
})
