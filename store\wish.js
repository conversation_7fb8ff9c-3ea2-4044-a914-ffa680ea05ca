/**
 * 心愿数据管理 Store
 * 
 * 重要说明：已彻底移除所有自动同步功能
 * - 不再有定时同步器
 * - 网络恢复时不会自动同步
 * - 所有同步都需要手动触发
 * 
 * 手动同步方法：
 * - manualSync(): 完整同步（用户主动调用）
 * - syncPendingData(): 同步待上传数据
 * - performIncrementalSync(): 增量同步
 * 
 * 同步状态检查：
 * - pendingSyncCount: 待同步数量
 * - hasSyncErrors: 是否有同步错误
 * - syncStatusText: 同步状态文本
 * - syncInfo: 完整同步信息
 */

import { defineStore } from 'pinia'
import loadingManager from '@/utils/loadingManager.js'
import { useUserStore } from './user.js'

// 错误码常量
const ERROR_CODES = {
  SUCCESS: 0,
  NETWORK_ERROR: 1001,
  AUTH_ERROR: 1002,
  VALIDATION_ERROR: 1003,
  PERMISSION_ERROR: 1004,
  NOT_FOUND_ERROR: 1005,
  SERVER_ERROR: 1006,
  UNKNOWN_ERROR: 9999
}

// 🚀 简化版本冲突处理 - 完全静默
const ConflictManager = {
  // 静默处理版本冲突，不显示任何弹窗
  handleVersionConflict(error, context = 'unknown') {
    console.log(`[ConflictManager] 静默处理版本冲突: ${context}`, error.message)
    return true // 已处理，不显示弹窗
  }
}

// 错误处理工具类
const ErrorHandler = {
  // 获取用户友好的错误信息
  getUserFriendlyMessage(error) {
    if (typeof error === 'string') return error
    
    if (error.errCode) {
      switch (error.errCode) {
        case ERROR_CODES.NETWORK_ERROR:
          return '网络连接失败，请检查网络设置'
        case ERROR_CODES.AUTH_ERROR:
          return '登录已过期，请重新登录'
        case ERROR_CODES.VALIDATION_ERROR:
          return error.errMsg || '数据验证失败'
        case ERROR_CODES.PERMISSION_ERROR:
          return '没有权限执行此操作'
        case ERROR_CODES.NOT_FOUND_ERROR:
          return '数据不存在或已被删除'
        case ERROR_CODES.SERVER_ERROR:
          return '服务器错误，请稍后重试'
        default:
          return error.errMsg || '操作失败'
      }
    }
    
    // 处理网络错误
    if (error.message && (
      error.message.includes('网络') || 
      error.message.includes('network') ||
      error.message.includes('timeout') ||
      error.code === 'NETWORK_ERROR'
    )) {
      return '网络连接失败，数据已保存到本地'
    }
    
    return error.message || error.errMsg || '未知错误'
  },
  
  // 判断是否为网络错误
  isNetworkError(error) {
    if (error.errCode === ERROR_CODES.NETWORK_ERROR) return true
    if (error.code === 'NETWORK_ERROR') return true
    if (error.message && (
      error.message.includes('网络') || 
      error.message.includes('network') ||
      error.message.includes('timeout')
    )) return true
    
    return false
  },
  
  // 🚀 检查是否是版本冲突错误
  isVersionConflictError(error) {
    return error && error.message && (
      error.message.includes('数据版本冲突') ||
      error.message.includes('逻辑时钟冲突') ||
      error.message.includes('version conflict') ||
      error.message.includes('conflict')
    )
  },

  // 显示错误提示 - 🔧 版本冲突完全静默处理
  showError(error, title = '操作失败', context = 'unknown') {
    // 🚀 版本冲突完全静默处理，不显示任何弹窗
    if (this.isVersionConflictError(error)) {
      ConflictManager.handleVersionConflict(error, context)
      return // 不显示弹窗
    }

    const message = this.getUserFriendlyMessage(error)

    // 🔧 直接显示非冲突错误
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })

    console.error(`${title}:`, error)
  }
}

export const useWishStore = defineStore('wish', {
  state: () => ({
    wishList: [],
    currentGroupId: 'all', // 默认显示全部分组
    isLoading: false,
    lastSyncTime: null,
    isOnline: true, // 网络状态
    
    // 添加一个用于强制触发响应式更新的标记
    listUpdateCounter: 0,

    // 新增同步状态管理
    syncStatus: {
      issyncing: false,
      lastSyncResult: null,
      pendingCount: 0,
      errorCount: 0,
      lastError: null
    },

    // 🚀 增强同步状态管理
    _syncOperations: new Set(), // 正在进行的同步操作
    _lastSyncTime: 0, // 上次同步时间
    _recentlyDeleted: new Map() // 最近删除的心愿ID记录
  }),
  
  getters: {
    // 获取当前分组的心愿列表
    currentGroupWishes: (state) => {
      // 访问listUpdateCounter确保响应式更新
      const updateCounter = state.listUpdateCounter;
      
      // 先过滤出未完成且未被删除的心愿
      const activeWishes = state.wishList.filter(wish => !wish.isCompleted && !wish._deleted);
      
      // 然后根据分组进一步过滤
      if (state.currentGroupId === 'all') {
        return activeWishes;
      } else if (state.currentGroupId === 'friend-visible') {
        // 特殊处理：朋友可见分组，按权限过滤
        return activeWishes.filter(wish => wish.permission === 'friends');
      } else if (state.currentGroupId === 'private') {
        // 特殊处理：私密分组，按权限过滤
        return activeWishes.filter(wish => wish.permission === 'private');
      } else if (state.currentGroupId === 'public') {
        // 特殊处理：公开分组，按权限过滤
        return activeWishes.filter(wish => wish.permission === 'public');
      } else if (state.currentGroupId === 'gift') {
        // 特殊处理：礼物分组，按标签或特殊字段过滤
        return activeWishes.filter(wish => 
          wish.groupIds && wish.groupIds.includes('gift')
        );
      } else {
        // 普通分组：按groupIds过滤
        return activeWishes.filter(wish => 
          wish.groupIds && wish.groupIds.includes(state.currentGroupId)
        );
      }
    },
    
    // 获取单个心愿
    getWishById: (state) => (id) => {
      return state.wishList.find(wish => 
        (wish._id === id || wish.id === id) && !wish._deleted
      ) || null
    },
    
    // 新增同步状态相关getters
    // 获取待同步的心愿数量
    pendingSyncCount: (state) => {
      return state.wishList.filter(wish => wish._needSync).length;
    },
    
    // 检查是否有同步错误
    hasSyncErrors: (state) => {
      return state.syncStatus.errorCount > 0 || state.syncStatus.lastError !== null;
    },
    
    // 获取同步状态描述
    syncStatusText: (state) => {
      if (state.syncStatus.issyncing) {
        return '正在同步...';
      }
      if (state.syncStatus.errorCount > 0) {
        return `同步失败 (${state.syncStatus.errorCount}个错误)`;
      }
      if (state.syncStatus.pendingCount > 0) {
        return `待同步 (${state.syncStatus.pendingCount}项)`;
      }
      if (state.syncStatus.lastSyncResult === 'success') {
        return '同步完成';
      }
      return '未同步';
    },
    
    // 获取详细同步信息
    syncInfo: (state) => {
      return {
        ...state.syncStatus,
        pendingCount: state.wishList.filter(wish => wish._needSync).length,
        lastSyncTime: state.lastSyncTime,
        isOnline: state.isOnline
      };
    }
  },
  
  actions: {
    // 初始化心愿数据
    async initWishList() {
      console.log('[wishStore] Initializing wish list...');
      
      // 先尝试从本地存储加载
      const storedWishes = uni.getStorageSync('wishList')
      if (storedWishes) {
        const parsed = JSON.parse(storedWishes)
        // 确保每个心愿对象都有 id 字段
        this.wishList = parsed.map(wish => ({
          ...wish,
          id: wish.id || wish._id // 如果没有 id 字段，使用 _id
        }))
        console.log('[wishStore] Loaded wishes from storage:', this.wishList.length);
      }
      
      // 初始化网络监听
      this.initNetworkMonitor()
      
      // 尝试从云端同步数据
      await this.syncFromCloud()
      
      console.log('[wishStore] Wish list initialization completed');
    },
    
    // 初始化网络监听
    initNetworkMonitor() {
      // 检查当前网络状态
      this.checkNetworkStatus()
      
      // 监听网络状态变化
      uni.onNetworkStatusChange((res) => {
        const wasOnline = this.isOnline
        this.isOnline = res.isConnected
        
        console.log(`网络状态变化: ${wasOnline ? '在线' : '离线'} -> ${this.isOnline ? '在线' : '离线'}`)
        
        // 如果从离线变为在线，自动同步
        // 网络恢复时只记录状态，不自动同步
        if (!wasOnline && this.isOnline) {
          console.log('网络恢复，但不启用自动同步')
        }
        
        // 如果变为离线，显示提示
        if (wasOnline && !this.isOnline) {
          uni.showToast({
            title: '网络连接已断开，将保存到本地',
            icon: 'none',
            duration: 2000
          })
        }
      })
    },
    
    // 检查网络状态
    async checkNetworkStatus() {
      try {
        const networkType = await uni.getNetworkType()
        this.isOnline = networkType.networkType !== 'none'
        console.log('当前网络状态:', this.isOnline ? '在线' : '离线')
      } catch (error) {
        console.error('检查网络状态失败:', error)
        this.isOnline = false
      }
    },
    
    // 手动同步逻辑（原autoSync方法重命名）
    async _performSync() {
      // 避免重复同步
      if (this.syncStatus.issyncing) {
        console.log('[wishStore] Sync already in progress, skipping...');
        return;
      }
      
      try {
        this._updateSyncStatus({ issyncing: true });
        
        // 检查是否有需要同步的数据
        const pendingWishes = this.wishList.filter(wish => wish._needSync)
        this._updateSyncStatus({ pendingCount: pendingWishes.length });
        
        // 优先处理本地待同步数据
        if (pendingWishes.length > 0) {
          console.log(`[wishStore] 发现${pendingWishes.length}个待同步心愿，开始上传同步...`)
          const result = await this.syncPendingData()
          
          if (result.syncedCount > 0) {
            // 上传同步成功后，再检查是否有云端更新
            await this.performIncrementalSync()
          }
        } else {
          // 没有待同步数据时，执行增量同步检查云端更新
          console.log('[wishStore] 执行增量同步检查云端更新...')
          await this.performIncrementalSync()
        }
        
        // 同步成功
        this._updateSyncStatus({ 
          lastSyncResult: 'success',
          errorCount: 0,
          lastError: null 
        });
        
      } catch (error) {
        console.error('[wishStore] 手动同步失败:', error)
        
        // 更新错误状态
        this._updateSyncStatus({ 
          lastSyncResult: 'failed',
          errorCount: this.syncStatus.errorCount + 1,
          lastError: error.message || '同步失败'
        });
        
        // 只有在非网络错误时才显示错误提示
        if (!ErrorHandler.isNetworkError(error)) {
          console.warn('[wishStore] 手动同步遇到错误')
        }
      } finally {
        this._updateSyncStatus({ issyncing: false });
      }
    },
    
    // 更新同步状态
    _updateSyncStatus(updates) {
      this.syncStatus = {
        ...this.syncStatus,
        ...updates
      };
      console.log('[wishStore] Sync status updated:', this.syncStatus);
    },
    
    // 手动触发同步（供用户主动调用）
    async manualSync(silent = false) {
      // 重置错误状态
      this._updateSyncStatus({
        errorCount: 0,
        lastError: null
      });

      // 使用统一加载管理器
      try {
        await loadingManager.wrap(
          () => this._performSync(),
          {
            title: '正在同步数据...',
            id: 'wish_manual_sync',
            timeout: 15000,
            silent: silent,
            showSuccess: !silent && this.syncStatus.lastSyncResult === 'success',
            successTitle: '同步完成',
            showError: !silent,
            errorTitle: '同步失败'
          }
        )
      } catch (error) {
        console.error('[wishStore] Manual sync failed:', error);
        if (!silent) {
          throw error;
        }
      }
    },
    
    // 重置同步状态
    resetSyncStatus() {
      this._updateSyncStatus({
        issyncing: false,
        lastSyncResult: null,
        pendingCount: 0,
        errorCount: 0,
        lastError: null
      });
    },

    // 🚀 新增：检查同步操作是否可以执行
    _canStartSync(operation) {
      const now = Date.now()

      // 检查是否有相同操作正在进行
      if (this._syncOperations.has(operation)) {
        console.log(`[wishStore] 同步操作 ${operation} 已在进行中`)
        return false
      }

      // 检查是否距离上次同步太近（防抖）
      if (now - this._lastSyncTime < 1000) {
        console.log(`[wishStore] 同步操作过于频繁，跳过`)
        return false
      }

      return true
    },

    // 🚀 新增：开始同步操作
    _startSyncOperation(operation) {
      this._syncOperations.add(operation)
      this._lastSyncTime = Date.now()
      console.log(`[wishStore] 开始同步操作: ${operation}`)
    },

    // 🚀 新增：结束同步操作
    _endSyncOperation(operation) {
      this._syncOperations.delete(operation)
      console.log(`[wishStore] 结束同步操作: ${operation}`)
    },

    // 🔧 新增：记录最近删除的心愿ID
    _recordDeletedWish(wishId) {
      if (!this._recentlyDeleted) {
        this._recentlyDeleted = new Map()
      }
      this._recentlyDeleted.set(wishId, Date.now())

      // 清理5分钟前的记录
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
      for (const [id, timestamp] of this._recentlyDeleted.entries()) {
        if (timestamp < fiveMinutesAgo) {
          this._recentlyDeleted.delete(id)
        }
      }
    },

    // 🔧 新增：获取最近删除的心愿ID列表
    _getRecentlyDeletedWishIds() {
      if (!this._recentlyDeleted) {
        return []
      }

      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
      const recentIds = []

      for (const [id, timestamp] of this._recentlyDeleted.entries()) {
        if (timestamp >= fiveMinutesAgo) {
          recentIds.push(id)
        }
      }

      return recentIds
    },
    
    // 从云端同步数据 - 🚀 增强防重复机制
    async syncFromCloud() {
      const operation = 'syncFromCloud'

      // 🚀 检查是否可以开始同步
      if (!this._canStartSync(operation)) {
        return
      }

      const userStore = useUserStore() // Get user store instance
      if (!userStore.isLogin) {
        console.log('[store/wish.js syncFromCloud] User not logged in, skipping cloud sync.');
        // 如果未登录，可以选择是否加载示例数据或清空列表
        if (this.wishList.length === 0) {
           // this._initExampleData() // 根据产品需求决定是否加载示例
        }
        return; // Do not proceed if not logged in
      }

      try {
        this._startSyncOperation(operation)
        this.isLoading = true
        console.log('[wishStore] Starting intelligent sync...');

        // 第一步：轻量级同步检查
        const syncNeded = await this._checkSyncNeeded();

        if (!syncNeded) {
          console.log('[wishStore] Local data is up to date, no sync needed');
          return;
        }

        // 第二步：如果需要同步，获取完整数据并进行智能合并
        await this._performIntelligentSync();
        
      } catch (error) {
        console.error('[wishStore] 智能同步失败:', error)
        // 如果云端同步失败，且本地没有数据，使用示例数据
        if (this.wishList.length === 0) {
          console.log('[wishStore] Initializing example data due to sync failure...');
          this._initExampleData()
        }
      } finally {
        this.isLoading = false
        this._endSyncOperation(operation)
      }
    },
    
    // 检查是否需要同步（轻量级调用）
    async _checkSyncNeeded() {
      // 临时特性开关：如果云端API未准备好，直接返回需要同步
      const enableLightweightCheck = false; // TODO: 等云端API就绪后设为true
      
      if (!enableLightweightCheck) {
        console.log('[wishStore] Lightweight sync check disabled, performing full sync');
        return true;
      }
      
      try {
        const wishCenter = uniCloud.importObject('wish-center')
        
        // 调用轻量级API，只获取同步摘要信息
        const result = await wishCenter.getSyncSummary({
          lastSyncTime: this.lastSyncTime,
          localDataCount: this.wishList.length,
          localLastModified: this._getLocalLastModified()
        })
        
        if (result.errCode === 0) {
          const { needFullSync, cloudLastModified, cloudDataCount } = result.data;
          
          // 比较时间戳和数据量来判断是否需要同步
          const localLastModified = this._getLocalLastModified();
          const needSync = needFullSync || 
                          !localLastModified || 
                          !cloudLastModified ||
                          new Date(cloudLastModified) > new Date(localLastModified) ||
                          cloudDataCount !== this.wishList.length;
          
          console.log('[wishStore] Sync check result:', {
            needSync,
            localLastModified,
            cloudLastModified,
            localCount: this.wishList.length,
            cloudCount: cloudDataCount
          });
          
          return needSync;
        } else {
          // 如果轻量级检查失败，默认进行同步
          console.warn('[wishStore] Sync check failed, defaulting to sync');
          return true;
        }
      } catch (error) {
        console.error('[wishStore] Sync check error:', error);
        // 网络错误时，不进行同步
        if (ErrorHandler.isNetworkError(error)) {
          return false;
        }
        // 其他错误时，进行同步
        return true;
      }
    },
    
    // 获取本地数据的最后修改时间
    _getLocalLastModified() {
      if (this.wishList.length === 0) return null;
      
      const latestWish = this.wishList.reduce((latest, current) => {
        const currentTime = new Date(current.updateDate || current.createDate);
        const latestTime = new Date(latest.updateDate || latest.createDate);
        return currentTime > latestTime ? current : latest;
      });
      
      return latestWish.updateDate || latestWish.createDate;
    },
    
    // 执行智能同步（合并数据而不是覆盖）
    async _performIntelligentSync() {
      console.log('[wishStore] Performing intelligent data merge...');
      
      const wishCenter = uniCloud.importObject('wish-center')
      const result = await wishCenter.getWishList({
        page: 1,
        pageSize: 1000,
        includeCompleted: true,
        includeDeleted: true // 包含已删除的数据用于冲突解决
      })
      
      if (result.errCode === 0) {
        const cloudWishes = result.data || [];
        console.log('[wishStore] Cloud data received:', cloudWishes.length, 'items');
        
        // 执行智能数据合并
        const mergedData = this._mergeWishData(this.wishList, cloudWishes);
        
        // 更新本地数据
        this.wishList = mergedData.map(wish => ({
          ...wish,
          id: wish._id // 确保有 id 字段供前端组件使用
        }));
        
        this._clearWishesCache() // 清除缓存
        this.lastSyncTime = new Date().toISOString()
        this._saveToStorage()
        
        console.log('[wishStore] Intelligent sync completed, merged data:', this.wishList.length, 'items');
      } else {
        throw new Error(result.errMsg || '获取云端数据失败');
      }
    },
    
    // 智能合并本地和云端数据
    _mergeWishData(localWishes, cloudWishes) {
      console.log('[wishStore] Merging local and cloud data...');

      const mergedMap = new Map();

      // 🔧 获取最近删除的心愿ID列表（5分钟内删除的）
      const recentlyDeletedIds = this._getRecentlyDeletedWishIds();

      // 首先处理云端数据，但排除最近删除的心愿
      cloudWishes.forEach(cloudWish => {
        // 🔧 跳过最近删除的心愿，避免重新加入
        if (recentlyDeletedIds.includes(cloudWish._id)) {
          console.log(`[wishStore] 跳过最近删除的心愿: ${cloudWish.title}`);
          return;
        }

        mergedMap.set(cloudWish._id, {
          ...cloudWish,
          _source: 'cloud'
        });
      });
      
      // 然后处理本地数据，根据时间戳决定是否覆盖
      localWishes.forEach(localWish => {
        const id = localWish._id || localWish.id;
        const existingWish = mergedMap.get(id);
        
        if (!existingWish) {
          // 本地独有的数据（可能是离线创建的）
          mergedMap.set(id, {
            ...localWish,
            _source: 'local',
            _needSync: true // 标记需要同步到云端
          });
        } else {
          // 存在冲突，比较时间戳 - 🔧 修复时间戳比较逻辑
          const localTimeStr = localWish.updateDate || localWish.createDate;
          const cloudTimeStr = existingWish.updateDate || existingWish.createDate;

          // 🔧 安全的时间戳比较
          const localTime = localTimeStr ? new Date(localTimeStr) : new Date(0);
          const cloudTime = cloudTimeStr ? new Date(cloudTimeStr) : new Date(0);

          // 🔧 检查时间戳是否有效
          const localTimeValid = !isNaN(localTime.getTime());
          const cloudTimeValid = !isNaN(cloudTime.getTime());

          if (!localTimeValid && !cloudTimeValid) {
            // 两个时间戳都无效，优先使用云端数据
            console.log('[wishStore] 两个时间戳都无效，使用云端数据:', id);
            // 保持云端数据
          } else if (!localTimeValid) {
            // 本地时间戳无效，使用云端数据
            console.log('[wishStore] 本地时间戳无效，使用云端数据:', id);
            // 保持云端数据
          } else if (!cloudTimeValid) {
            // 云端时间戳无效，使用本地数据
            console.log('[wishStore] 云端时间戳无效，使用本地数据:', id);
            mergedMap.set(id, {
              ...localWish,
              _source: 'local-newer',
              _needSync: true
            });
          } else if (localTime > cloudTime) {
            // 本地数据更新，使用本地数据
            mergedMap.set(id, {
              ...localWish,
              _source: 'local-newer',
              _needSync: true // 需要同步到云端
            });
          } else if (localTime < cloudTime) {
            // 云端数据更新，使用云端数据
            // 保持云端数据，无需额外处理
          } else {
            // 时间戳相同，检查内容是否有差异
            if (this._hasContentDifference(localWish, existingWish)) {
              // 内容有差异但时间戳相同，优先使用云端数据
              console.log('[wishStore] 时间戳相同但内容不同，使用云端数据:', id);
            }
          }
        }
      });
      
      // 转换为数组并过滤已删除的项目
      const mergedArray = Array.from(mergedMap.values())
        .filter(wish => !wish._deleted)
        .sort((a, b) => (a.order || 0) - (b.order || 0));
      
      console.log('[wishStore] Data merge completed:', {
        localCount: localWishes.length,
        cloudCount: cloudWishes.length,
        mergedCount: mergedArray.length
      });
      
      return mergedArray;
    },
    
    // 检查两个心愿对象是否有内容差异
    _hasContentDifference(wish1, wish2) {
      const keys = ['title', 'description', 'image', 'video', 'audio', 'groupIds', 'permission'];
      
      return keys.some(key => {
        const val1 = wish1[key];
        const val2 = wish2[key];
        
        // 对于数组，需要深度比较
        if (Array.isArray(val1) && Array.isArray(val2)) {
          return JSON.stringify(val1.sort()) !== JSON.stringify(val2.sort());
        }
        
        return val1 !== val2;
      });
    },
    
    // 初始化示例数据
    _initExampleData() {
        this.wishList = [
          {
          _id: '1',
          id: '1', // 添加 id 字段以保持兼容性
            title: '买一台新笔记本电脑',
            description: '想要一台性能好的笔记本，用于工作和娱乐',
          image: [],
          video: [],
          audio: [],
            createDate: new Date().toISOString(),
          updateDate: new Date().toISOString(),
            startDate: null,
            completeDate: null,
            isCompleted: false,
            permission: 'friends',
            groupIds: ['all', 'gift', 'friend-visible'],
            order: 1,
          groupOrder: 1
          },
          {
          _id: '2',
          id: '2', // 添加 id 字段以保持兼容性
            title: '去日本旅行',
            description: '计划明年去日本旅行，看樱花',
          image: [],
          video: [],
          audio: [],
            createDate: new Date().toISOString(),
          updateDate: new Date().toISOString(),
            startDate: null,
            completeDate: null,
            isCompleted: false,
            permission: 'private',
            groupIds: ['all'],
            order: 2,
          groupOrder: 1
          },
          {
          _id: '3',
          id: '3', // 添加 id 字段以保持兼容性
            title: '学习一门新语言',
            description: '学习法语，已经完成了初级课程',
          image: [],
          video: [],
          audio: [],
          createDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          updateDate: new Date().toISOString(),
            startDate: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
          completeDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            isCompleted: true,
            permission: 'friends',
            groupIds: ['all', 'friend-visible'],
            order: 3,
          groupOrder: 1
          }
        ]
        this._saveToStorage()
    },
    
    // 从本地存储刷新心愿列表数据
    refreshWishList() {
      const storedWishes = uni.getStorageSync('wishList')
      if (storedWishes) {
        const parsed = JSON.parse(storedWishes)
        // 确保每个心愿对象都有 id 字段
        this.wishList = parsed.map(wish => ({
          ...wish,
          id: wish.id || wish._id // 如果没有 id 字段，使用 _id
        }))
      }
    },
    
    // 设置当前分组
    setCurrentGroup(groupId) {
      this.currentGroupId = groupId
      // 清除缓存，因为分组变化了
      this._clearWishesCache()
    },
    
    // 清除心愿列表缓存（内部方法） - 已废弃，保留以防兼容性问题
    _clearWishesCache() {
      // 缓存机制已移除
    },
    
    // 添加心愿
    async addWish(wishData) {
      try {
        // 添加时间戳
        const timestamp = Date.now();
        const enhancedWishData = {
          ...wishData,
          timestamp,
          deviceId: uni.getSystemInfoSync().deviceId || 'unknown'
        };

        // 检查网络状态
        if (!this.isOnline) {
          console.log('当前离线，心愿将保存到本地')
          return this._addWishOffline(enhancedWishData)
        }

        // 先调用云函数创建
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.createWish(enhancedWishData)

        if (result.errCode === 0) {
          // 云端创建成功，添加到本地列表
          const wishWithId = {
            ...result.data,
            id: result.data._id // 确保有 id 字段供前端组件使用
          }
          this.wishList.push(wishWithId)
          this._clearWishesCache() // 清除缓存
          this._saveToStorage()

          // 立即触发响应式更新
          this._triggerReactiveUpdate('add', wishWithId._id)

          return wishWithId
        } else {
          throw new Error(result.errMsg || '创建心愿失败')
        }
      } catch (error) {
        console.error('添加心愿失败:', error)

        // 网络错误时，保存到本地（离线模式）
        if (ErrorHandler.isNetworkError(error)) {
          this.isOnline = false // 更新网络状态
          return this._addWishOffline(enhancedWishData)
        }

        // 🚀 优化：使用统一的版本冲突处理
        if (ErrorHandler.isVersionConflictError(error)) {
          ConflictManager.handleVersionConflict(error, 'create_wish')
          // 冲突时使用离线模式保存
          return this._addWishOffline(enhancedWishData)
        }

        // 其他错误，显示错误信息并抛出
        ErrorHandler.showError(error, '创建心愿失败', 'create_wish')
        throw error
      }
    },
    
    // 🚀 离线添加心愿 - 支持逻辑时钟
    _addWishOffline(wishData) {
      const wishId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      const wish = {
        _id: wishId,
        id: wishId, // 同时添加 id 字段以保持兼容性
        title: wishData.title || '',
        description: wishData.description || '',
        image: Array.isArray(wishData.image) ? wishData.image : (wishData.image ? [wishData.image] : []),
        video: Array.isArray(wishData.video) ? wishData.video : (wishData.video ? [wishData.video] : []),
        audio: Array.isArray(wishData.audio) ? wishData.audio : (wishData.audio ? [wishData.audio] : []),
        createDate: new Date().toISOString(),
        updateDate: new Date().toISOString(),
        startDate: wishData.startDate || null,
        completeDate: null,
        isCompleted: false,
        permission: wishData.permission || 'private',
        groupIds: wishData.groupIds || ['all'],
        order: this.wishList.length + 1,
        groupOrder: this._getNextGroupOrder(wishData.groupIds || ['all']),
        _needSync: true, // 标记需要同步到云端

        // 时间戳支持
        timestamp: wishData.timestamp || Date.now(),
        lastModifiedDevice: wishData.deviceId || uni.getSystemInfoSync().deviceId || 'unknown',
        version: 1
      }

      // 确保groupIds包含'all'
      if (!wish.groupIds.includes('all')) {
        wish.groupIds.push('all')
      }

      // 根据权限设置分组
      if (wish.permission === 'friends' && !wish.groupIds.includes('friend-visible')) {
          wish.groupIds.push('friend-visible')
      }

      this.wishList.push(wish)
      this._clearWishesCache() // 清除缓存
      this._saveToStorage()

      // 立即触发响应式更新
      this._triggerReactiveUpdate('add', wish._id)

      // 显示离线提示
      uni.showToast({
        title: this.isOnline ? '已保存到本地，稍后将同步到云端' : '当前离线，已保存到本地',
        icon: 'none',
        duration: 2500
      })

      return wish
    },
    
    // 获取分组内下一个排序号
    _getNextGroupOrder(groupIds) {
      if (!groupIds || groupIds.length === 0) return 1
      
      // 获取这些分组中的最大groupOrder
      const maxOrder = this.wishList
        .filter(wish => {
          return groupIds.some(groupId => wish.groupIds && wish.groupIds.includes(groupId))
        })
        .reduce((max, wish) => Math.max(max, wish.groupOrder || 0), 0)
      
      return maxOrder + 1
    },
    
    // 更新心愿
    async updateWish(updatedWish) {
      console.log('[wishStore] updateWish called with:', updatedWish);
      
      try {
        // 验证更新数据的完整性
        if (!updatedWish || (!updatedWish._id && !updatedWish.id)) {
          throw new Error('更新数据缺少必要的ID字段');
        }
        
        // 检查网络状态
        if (!this.isOnline) {
          console.log('当前离线，心愿将保存到本地')
          this._updateLocalWish(updatedWish)

          // 标记需要同步
          const index = this.wishList.findIndex(w => w._id === updatedWish._id || w.id === updatedWish.id)
          if (index !== -1) {
            this.wishList[index]._needSync = true
          }

          // 立即触发响应式更新
          this._triggerReactiveUpdate('update', updatedWish._id || updatedWish.id)

          uni.showToast({
            title: '当前离线，已保存到本地',
            icon: 'none',
            duration: 2500
          })
          return
        }
        
        // 🔧 准备更新数据，确保版本号和时间戳正确
        const updateData = {
          ...updatedWish,
          updateDate: new Date().toISOString(), // 设置当前时间戳
          version: (updatedWish.version || 1) + 1, // 递增版本号
          _forceUpdate: false // 不强制更新，允许冲突检测
        }

        // 先更新云端
        const wishCenter = uniCloud.importObject('wish-center')
        console.log('[wishStore] Calling cloud updateWish with ID:', updatedWish._id || updatedWish.id);
        const result = await wishCenter.updateWish(updatedWish._id || updatedWish.id, updateData)
        
        if (result.errCode === 0) {
          // 云端更新成功，更新本地数据
          console.log('[wishStore] Cloud update successful, updating local data');

          // 🔧 重要：使用云端返回的最新数据，包含正确的版本号
          if (result.data) {
            this._updateLocalWish(result.data)
          } else {
            // 如果云端没有返回数据，手动更新版本号
            updatedWish.version = (updatedWish.version || 1) + 1
            this._updateLocalWish(updatedWish)
          }

          // 立即触发响应式更新
          this._triggerReactiveUpdate('update', updatedWish._id || updatedWish.id)
        } else {
          throw new Error(result.errMsg || '更新心愿失败')
        }
      } catch (error) {
        console.error('更新心愿失败:', error)
        
        // 网络错误时，先更新本地（离线模式）
        if (ErrorHandler.isNetworkError(error)) {
          console.log('[wishStore] Network error, saving locally');
          this.isOnline = false
          this._updateLocalWish(updatedWish)

          // 标记需要同步
          const index = this.wishList.findIndex(w => w._id === updatedWish._id || w.id === updatedWish.id)
          if (index !== -1) {
            this.wishList[index]._needSync = true
          }

          // 立即触发响应式更新
          this._triggerReactiveUpdate('update', updatedWish._id || updatedWish.id)
          
          uni.showToast({
            title: '网络连接失败，已保存到本地',
            icon: 'none',
            duration: 2500
          })
        } else if (error.message && (
          error.message.includes('心愿不存在') || 
          error.message.includes('无权限') ||
          error.message.includes('not found') ||
          error.message.includes('permission')
        )) {
          // 多设备冲突处理 - 心愿可能已被其他设备删除
          console.log(`[wishStore] 多设备更新冲突: 心愿可能已被其他设备删除，移除本地记录`);
          const index = this.wishList.findIndex(w => w._id === updatedWish._id || w.id === updatedWish.id);
          if (index !== -1) {
            this.wishList.splice(index, 1);
            // 重新计算序号
            this._reorderAfterDelete();
            this._saveToStorage();
          }
          
          uni.showToast({
            title: '心愿已不存在',
            icon: 'none',
            duration: 1500
          });
        } else if (ErrorHandler.isVersionConflictError(error)) {
          // 🚀 优化：使用统一的版本冲突处理
          ConflictManager.handleVersionConflict(error, 'update_wish')
          // 冲突时使用本地数据，标记需要同步
          const index = this.wishList.findIndex(w => w._id === updatedWish._id || w.id === updatedWish.id)
          if (index !== -1) {
            this.wishList[index]._needSync = true
          }
        } else {
          // 其他错误，显示错误信息
          ErrorHandler.showError(error, '更新心愿失败', 'update_wish')
          throw error
        }
      }
    },
    
    // 更新本地心愿数据
    _updateLocalWish(updatedWish) {
      console.log('[wishStore] _updateLocalWish called with:', updatedWish);
      
      const index = this.wishList.findIndex(w => w._id === updatedWish._id || w.id === updatedWish.id)
      console.log('[wishStore] Found wish at index:', index);
      
      if (index !== -1) {
        const originalWish = this.wishList[index]
        console.log('[wishStore] Original wish:', originalWish);
        
        // 合并数据，确保使用正确的字段名和默认值
        const mergedWish = {
          ...originalWish,
          ...updatedWish,
          updateDate: new Date().toISOString(),
          // 确保ID字段兼容性
          id: updatedWish.id || updatedWish._id || originalWish.id || originalWish._id,
          _id: updatedWish._id || updatedWish.id || originalWish._id || originalWish.id,
          // 确保必要字段有默认值
          permission: updatedWish.permission || originalWish.permission || 'private',
          groupIds: updatedWish.groupIds || originalWish.groupIds || ['all'],
          tags: updatedWish.tags || originalWish.tags || [],
          title: updatedWish.title || originalWish.title || '',
          description: updatedWish.description || originalWish.description || ''
        }
        
        console.log('[wishStore] Merged wish:', mergedWish);
        
        // 确保多媒体字段为数组
        ['image', 'video', 'audio'].forEach(field => {
          if (mergedWish[field] && !Array.isArray(mergedWish[field])) {
            mergedWish[field] = [mergedWish[field]]
          } else if (!mergedWish[field]) {
            mergedWish[field] = []
          }
        })
          
        // 权限变更时更新分组
        if (updatedWish.hasOwnProperty('permission')) {
          console.log('[wishStore] Permission change detected:', updatedWish.permission);
          
          // 确保 groupIds 是数组
          if (!Array.isArray(mergedWish.groupIds)) {
            console.log('[wishStore] groupIds is not array, setting default');
            mergedWish.groupIds = ['all']
          }
          
          if (mergedWish.permission === 'friends') {
            if (!mergedWish.groupIds.includes('friend-visible')) {
              mergedWish.groupIds.push('friend-visible')
            }
          } else {
            mergedWish.groupIds = mergedWish.groupIds.filter(id => id !== 'friend-visible')
            if (mergedWish.groupIds.length === 0) {
              mergedWish.groupIds.push('all')
            }
          }
        }
        
        console.log('[wishStore] Final merged wish:', mergedWish);
        this.wishList[index] = mergedWish
        this._saveToStorage()
      } else {
        console.error('[wishStore] Could not find wish to update with ID:', updatedWish._id || updatedWish.id);
      }
    },
    
    // 删除心愿
    async deleteWish(id) {
      try {
        // 检查网络状态
        if (!this.isOnline) {
          console.log('当前离线，心愿删除将保存到本地')
          // 先备份心愿数据，以防需要恢复
          const wish = this.wishList.find(w => w._id === id || w.id === id)
          if (wish) {
            wish._deleted = true
            wish._needSync = true
            wish.updateDate = new Date().toISOString()
          }
          
          // 数据已标记为需要同步，等待网络恢复后自动同步
          
          uni.showToast({
            title: '当前离线，删除操作已保存',
            icon: 'none',
            duration: 2500
          })
          return
        }
        
        // 先删除云端
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.deleteWish(id)
        
        if (result.errCode === 0) {
          // 云端删除成功，删除本地数据
          this._deleteLocalWish(id)

          // 🔧 立即更新同步时间，避免下拉刷新时重新获取已删除的数据
          this.lastSyncTime = new Date().toISOString()
          this._saveToStorage()

          // 数据变化通知已通过云函数推送处理
        } else {
          throw new Error(result.errMsg || '删除心愿失败')
        }
      } catch (error) {
        console.error('删除心愿失败:', error)
        
        // 网络错误时，标记为删除（离线模式）
        if (ErrorHandler.isNetworkError(error)) {
          this.isOnline = false
          const wish = this.wishList.find(w => w._id === id || w.id === id)
          if (wish) {
            wish._deleted = true
            wish._needSync = true
            wish.updateDate = new Date().toISOString()
          }
          
          // 数据已标记为需要同步，等待网络恢复后自动同步
          
          uni.showToast({
            title: '网络连接失败，删除操作已保存',
            icon: 'none',
            duration: 2500
          })
        } else {
          // 其他错误，显示错误信息
          ErrorHandler.showError(error, '删除心愿失败')
          throw error
        }
      }
    },
    
    // 删除本地心愿数据
    _deleteLocalWish(id) {
      console.log(`[wishStore] 删除心愿 ${id}`);

      // 🔧 记录删除的心愿ID，避免下次同步时重新加入
      this._recordDeletedWish(id)

      // 找到要删除的项目索引
      const index = this.wishList.findIndex(wish => wish._id === id || wish.id === id)

      if (index !== -1) {
        // 使用splice原地删除，保持数组引用不变，让Vue更好地追踪变化
        this.wishList.splice(index, 1)

        console.log(`[wishStore] 删除后剩余心愿数量: ${this.wishList.length}`);

        // 重新计算序号
        this._reorderAfterDelete()

        // 强制触发响应式更新
        this.listUpdateCounter++

        // 保存到本地存储
        this._saveToStorage()

        // 立即触发响应式更新
        this._triggerReactiveUpdate('delete', id)

        console.log(`[wishStore] 心愿删除完成，序号已更新`);
      } else {
        console.warn(`[wishStore] 未找到要删除的心愿: ${id}`);
      }
    },

    // 删除后重新计算序号的统一方法
    _reorderAfterDelete() {
        // 先按原序号排序，确保顺序正确
        this.wishList.sort((a, b) => (a.order || 0) - (b.order || 0))
        
        // 重新分配连续的序号
        this.wishList.forEach((wish, index) => {
          wish.order = index + 1;
          wish.updateDate = new Date().toISOString();
        })
        
        console.log(`[wishStore] 序号重新计算完成，共${this.wishList.length}个心愿`);
    },
    
    // 完成心愿
    async completeWish(id) {
      try {
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.completeWish(id)
        
        if (result.errCode === 0) {
          // 云端操作成功，更新本地数据
          const wish = this.wishList.find(w => w._id === id || w.id === id)
          if (wish) {
            wish.isCompleted = true
            wish.completeDate = new Date().toISOString()
            wish.updateDate = new Date().toISOString()
            // 🔧 重要：同步云端的版本号，避免后续冲突
            if (result.data && result.data.version) {
              wish.version = result.data.version
            } else {
              // 如果云端没有返回版本号，手动递增
              wish.version = (wish.version || 1) + 1
            }
            this._saveToStorage()

            // 立即触发响应式更新
            this._triggerReactiveUpdate('complete', id)
          }
        } else {
          throw new Error(result.errMsg || '完成心愿失败')
        }
      } catch (error) {
        // 特殊处理：多设备同步冲突（心愿可能已被其他设备删除）
        if (error.message && (
          error.message.includes('心愿不存在') || 
          error.message.includes('无权限') ||
          error.message.includes('not found') ||
          error.message.includes('permission')
        )) {
          // 多设备冲突处理 - 心愿可能已被其他设备删除
          console.log(`[wishStore] 多设备完成冲突: 心愿可能已被其他设备删除，移除本地记录`);
          const index = this.wishList.findIndex(w => w._id === id || w.id === id);
          if (index !== -1) {
            this.wishList.splice(index, 1);
            // 重新计算序号
            this._reorderAfterDelete();
            this._saveToStorage();
          }
          return;
        }
        
        console.error('完成心愿失败:', error)
        
        // 云端失败时，先更新本地
        const wish = this.wishList.find(w => w._id === id || w.id === id)
        if (wish) {
          wish.isCompleted = true
          wish.completeDate = new Date().toISOString()
          wish.updateDate = new Date().toISOString()
          wish._needSync = true
          this._saveToStorage()

          // 立即触发响应式更新
          this._triggerReactiveUpdate('complete', id)
        }
      }
    },
    
    // 恢复心愿
    async restoreWish(id) {
      try {
        const wishCenter = uniCloud.importObject('wish-center')
        const result = await wishCenter.restoreWish(id)
        
        if (result.errCode === 0) {
          // 云端操作成功，更新本地数据
          const wish = this.wishList.find(w => w._id === id || w.id === id)
          if (wish && wish.isCompleted) {
            wish.isCompleted = false
            wish.completeDate = null
            wish.updateDate = new Date().toISOString()
            // 🔧 重要：同步云端的版本号，避免后续冲突
            if (result.data && result.data.version) {
              wish.version = result.data.version
            } else {
              // 如果云端没有返回版本号，手动递增
              wish.version = (wish.version || 1) + 1
            }
            this._saveToStorage()

            // 立即触发响应式更新
            this._triggerReactiveUpdate('restore', id)

            return true
          }
        } else {
          throw new Error(result.errMsg || '恢复心愿失败')
        }
      } catch (error) {
        console.error('恢复心愿失败:', error)
        
        // 云端失败时，先更新本地
        const wish = this.wishList.find(w => w._id === id || w.id === id)
        if (wish && wish.isCompleted) {
          wish.isCompleted = false
          wish.completeDate = null
          wish.updateDate = new Date().toISOString()
          wish._needSync = true
        this._saveToStorage()
          return true
        }
      }
      return false
    },
    
    // 更新心愿排序（支持分组内排序）
    async updateWishOrder(orderedIds, groupId = 'all') {
      if (!orderedIds || orderedIds.length === 0) return
      
      try {
        // 先更新云端
        const wishCenter = uniCloud.importObject('wish-center')
        const sortType = groupId === 'all' ? 'global' : 'group'
        const result = await wishCenter.updateWishOrder(orderedIds, sortType, groupId)
        
        if (result.errCode === 0) {
          // 云端更新成功，更新本地排序
          this._updateLocalOrder(orderedIds, groupId)
          
          // 数据变化通知已通过云函数推送处理
        } else {
          throw new Error(result.errMsg || '更新排序失败')
        }
      } catch (error) {
        console.error('更新排序失败:', error)
        
        // 云端失败时，先更新本地
        this._updateLocalOrder(orderedIds, groupId)
      }
    },
    
    // 更新本地排序
    _updateLocalOrder(orderedIds, groupId) {
      // 更新分组内排序
      orderedIds.forEach((id, index) => {
        const wish = this.wishList.find(w => w._id === id || w.id === id)
        if (wish) {
          if (groupId === 'all') {
            wish.order = index + 1
          } else {
            wish.groupOrder = index + 1
          }
          wish.updateDate = new Date().toISOString()
          wish._needSync = true
        }
      })
      
      // 排序列表
      this.wishList.sort((a, b) => {
        if (a.isCompleted !== b.isCompleted) {
          return a.isCompleted ? 1 : -1
        }
        return (a.order || 0) - (b.order || 0)
      })
      
      this._saveToStorage()
      console.log('心愿排序已更新并保存')
    },
    
    // 临时设置当前列表的顺序，用于拖拽排序过程中的实时显示
    setCurrentList(newList) {
      if (!newList || newList.length === 0) return
      
      // 创建ID映射
      const idToWishMap = new Map()
      this.wishList.forEach(wish => {
        const key = wish._id || wish.id
        idToWishMap.set(key, wish)
      })
      
      if (this.currentGroupId === 'all') {
        // 全部分组：更新全局顺序
        const reorderedWishes = []
        const completedWishes = this.wishList.filter(wish => wish.isCompleted)
        
        newList.forEach(wish => {
          if (idToWishMap.has(wish._id || wish.id)) {
            reorderedWishes.push(idToWishMap.get(wish._id || wish.id))
          }
        })
        
        const unorderedWishes = this.wishList.filter(wish => 
          !wish.isCompleted && !reorderedWishes.some(w => (w._id || w.id) === (wish._id || wish.id))
        )
        
        this.wishList = [...reorderedWishes, ...unorderedWishes, ...completedWishes]
      } else {
        // 特定分组：更新分组内顺序
        const groupWishes = this.wishList.filter(wish => 
          wish.groupIds && wish.groupIds.includes(this.currentGroupId) && !wish.isCompleted
        )
        
        const otherWishes = this.wishList.filter(wish => 
          !wish.groupIds || !wish.groupIds.includes(this.currentGroupId) || wish.isCompleted
        )
        
        const reorderedGroupWishes = []
        newList.forEach(wish => {
          const key = wish._id || wish.id
          if (idToWishMap.has(key)) {
            reorderedGroupWishes.push(idToWishMap.get(key))
          }
        })
        
        const unorderedGroupWishes = groupWishes.filter(wish => 
          !reorderedGroupWishes.some(w => (w._id || w.id) === (wish._id || wish.id))
        )
        
        this.wishList = [...reorderedGroupWishes, ...unorderedGroupWishes, ...otherWishes]
      }
    },
    
    // 同步需要上传的本地数据到云端
    async syncPendingData() {
      const pendingWishes = this.wishList.filter(wish => wish._needSync)
      let syncedCount = 0
      let failedCount = 0
      
      if (pendingWishes.length === 0) {
        console.log('[wishStore] No pending data to sync');
        return { syncedCount: 0, failedCount: 0 };
      }
      
      console.log(`[wishStore] Syncing ${pendingWishes.length} pending wishes...`);
      
      for (const wish of pendingWishes) {
        try {
          const wishCenter = uniCloud.importObject('wish-center')
          
          if (wish._deleted) {
            // 处理删除操作
            const result = await wishCenter.deleteWish(wish._id)
            if (result.errCode === 0) {
              // 删除成功，从本地移除
              this.wishList = this.wishList.filter(w => w._id !== wish._id && w.id !== wish.id)
              syncedCount++
              console.log(`[wishStore] Deleted wish synced: ${wish.title}`);
            } else {
              throw new Error(result.errMsg || '删除心愿失败')
            }
          } else if (wish._id.startsWith('temp_')) {
            // 临时ID，需要创建
            const result = await wishCenter.createWish(wish)
            if (result.errCode === 0) {
              // 更新本地ID
              const oldId = wish._id;
              wish._id = result.data._id;
              wish.id = result.data._id;
              delete wish._needSync;
              syncedCount++;
              console.log(`[wishStore] New wish synced: ${wish.title} (${oldId} -> ${wish._id})`);
            } else if (result.errCode === 409) {
              // 冲突：云端已有相同数据，使用云端数据
              console.log(`[wishStore] Conflict detected for new wish: ${wish.title}, using cloud data`);
              const cloudWish = result.data;
              Object.assign(wish, cloudWish);
              delete wish._needSync;
              syncedCount++;
            } else {
              throw new Error(result.errMsg || '创建心愿失败')
            }
          } else {
            // 已有ID，需要更新
            const result = await wishCenter.updateWish(wish._id, wish)
            if (result.errCode === 0) {
              delete wish._needSync
              syncedCount++
              console.log(`[wishStore] Updated wish synced: ${wish.title}`);
            } else if (result.errCode === 409) {
              // 冲突：云端数据已被其他设备修改 - 静默处理
              console.log(`[wishStore] Update conflict for wish: ${wish.title}, using cloud data silently`);

              // 🔧 直接使用云端数据，不显示冲突弹窗
              if (result.data) {
                Object.assign(wish, result.data);
                delete wish._needSync;
                syncedCount++;
                console.log(`[wishStore] Conflict resolved silently: cloud wins for ${wish.title}`);
              } else {
                // 如果没有云端数据，标记为失败但不显示错误
                console.log(`[wishStore] Conflict resolution failed for ${wish.title}, will retry later`);
                failedCount++;
              }
            } else {
              throw new Error(result.errMsg || '更新心愿失败')
            }
          }
          
          // 每同步一个项目后稍作延迟，避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (error) {
          // 其他类型的错误才输出错误日志
          console.error('同步心愿失败:', wish.title, error)
          failedCount++;
          
          // 如果是网络错误，停止同步
          if (ErrorHandler.isNetworkError(error)) {
            this.isOnline = false
            console.log('网络错误，停止同步')
            break
            }
          }
        }
      }
        
      // 保存到本地存储
      this._saveToStorage()
      
      // 显示同步结果
      if (syncedCount > 0) {
        console.log(`[wishStore] 同步成功: ${syncedCount}个心愿`)
      }
      if (failedCount > 0) {
        console.log(`[wishStore] 同步失败: ${failedCount}个心愿`)
      }
      
      return { syncedCount, failedCount }
    },
    
    // 解决更新冲突
    async _resolveUpdateConflict(localWish, cloudWish) {
      console.log(`[wishStore] Resolving conflict for: ${localWish.title}`);
      
      const localTime = new Date(localWish.updateDate || localWish.createDate);
      const cloudTime = new Date(cloudWish.updateDate || cloudWish.createDate);
      
      if (localTime > cloudTime) {
        // 本地数据更新，强制更新云端
        try {
          const wishCenter = uniCloud.importObject('wish-center');
          const result = await wishCenter.forceUpdateWish(localWish._id, {
            ...localWish,
            _forceUpdate: true,
            _conflictResolution: 'local-wins'
          });
          
          if (result.errCode === 0) {
            delete localWish._needSync;
            console.log(`[wishStore] Conflict resolved: local wins for ${localWish.title}`);
            return true;
          }
        } catch (error) {
          console.error('强制更新失败:', error);
        }
      } else {
        // 云端数据更新，使用云端数据
        console.log(`[wishStore] Conflict resolved: cloud wins for ${cloudWish.title}`);
        Object.assign(localWish, cloudWish);
        delete localWish._needSync;
        return true;
      }
      
      return false;
    },
    
    // 增量同步：只同步变更的数据
    async performIncrementalSync() {
      if (!this.lastSyncTime) {
        console.log('[wishStore] No last sync time, performing full sync');
        return await this.syncFromCloud();
      }
      
      try {
        const wishCenter = uniCloud.importObject('wish-center');
        const result = await wishCenter.getIncrementalChanges({
          since: this.lastSyncTime,
          includeDeleted: true
        });
        
        if (result.errCode === 0) {
          const { additions, updates, deletions } = result.data;
          let changesApplied = 0;
          
          // 处理新增的心愿
          if (additions && additions.length > 0) {
            additions.forEach(cloudWish => {
              const existingIndex = this.wishList.findIndex(w => w._id === cloudWish._id);
              if (existingIndex === -1) {
                this.wishList.push({
                  ...cloudWish,
                  id: cloudWish._id
                });
                changesApplied++;
                console.log(`[wishStore] Added new wish from cloud: ${cloudWish.title}`);
              }
            });
          }
          
          // 处理更新的心愿
          if (updates && updates.length > 0) {
            updates.forEach(cloudWish => {
              const existingIndex = this.wishList.findIndex(w => w._id === cloudWish._id);
              if (existingIndex !== -1) {
                const localWish = this.wishList[existingIndex];
                const localTime = new Date(localWish.updateDate || localWish.createDate);
                const cloudTime = new Date(cloudWish.updateDate || cloudWish.createDate);
                
                // 只有云端数据更新时才应用更改
                if (cloudTime > localTime && !localWish._needSync) {
                  this.wishList[existingIndex] = {
                    ...cloudWish,
                    id: cloudWish._id
                  };
                  changesApplied++;
                  console.log(`[wishStore] Updated wish from cloud: ${cloudWish.title}`);
                } else {
                  console.log(`[wishStore] Skipped cloud update for ${cloudWish.title} (local is newer or has pending changes)`);
                }
              }
            });
          }
          
          // 处理删除的心愿
          if (deletions && deletions.length > 0) {
            deletions.forEach(deletedId => {
              const existingIndex = this.wishList.findIndex(w => w._id === deletedId);
              if (existingIndex !== -1) {
                const removedWish = this.wishList[existingIndex];
                this.wishList.splice(existingIndex, 1);
                changesApplied++;
                console.log(`[wishStore] Removed deleted wish: ${removedWish.title}`);
              }
            });
          }
          
          if (changesApplied > 0) {
            this.lastSyncTime = new Date().toISOString();
            this._saveToStorage();
            console.log(`[wishStore] Incremental sync completed: ${changesApplied} changes applied`);
          } else {
            console.log('[wishStore] No changes to apply from incremental sync');
          }
          
          return { changesApplied };
        } else {
          throw new Error(result.errMsg || '增量同步失败');
        }
      } catch (error) {
        console.error('[wishStore] Incremental sync failed:', error);
        // 增量同步失败时，回退到检查是否需要完整同步
        if (!ErrorHandler.isNetworkError(error)) {
          return await this.syncFromCloud();
        }
        throw error;
      }
    },
    
    // 保存到本地存储
    _saveToStorage() {
      uni.setStorageSync('wishList', JSON.stringify(this.wishList))
      uni.setStorageSync('wishLastSyncTime', this.lastSyncTime)
    },
    
    // 强制重新初始化（用于登录后重新同步）
    async forceInit() {
      console.log('[wishStore] Force initialization...');
      
      // 清空当前数据
      this.wishList = [];
      this.currentGroupId = 'all';
      this.lastSyncTime = null;
      this.isLoading = false;
      
      // 清理本地存储
      uni.removeStorageSync('wishList');
      uni.removeStorageSync('wishLastSyncTime');
      
      // 强制从云端同步
      await this.syncFromCloud();
      
      console.log('[wishStore] Force initialization completed');
    },
    
    // 清理本地数据（用于重新登录时清除旧用户数据）
    clearLocalData() {
      console.log('[wishStore] Clearing user-specific data...');
      
      // 清空用户的心愿数据
      this.wishList = [];
      
      // 重置为默认分组（全部）
      this.currentGroupId = 'all';
      
      this.lastSyncTime = null;
      this.isLoading = false;
      
      // 清除本地存储中的用户数据
      uni.removeStorageSync('wishList');
      uni.removeStorageSync('wishLastSyncTime');
      
      console.log('[wishStore] User-specific data cleared, system functionality preserved');
    },

    // 调试方法：检查心愿数据的ID字段
    debugWishIds() {
      console.log('=== Wish ID Debug Info ===');
      console.log('Total wishes:', this.wishList.length);

      const issues = [];
      this.wishList.forEach((wish, index) => {
        if (!wish.id && !wish._id) {
          issues.push(`Wish ${index} (${wish.title}): Missing both ID fields`);
        } else if (!wish.id) {
          issues.push(`Wish ${index} (${wish.title}): Missing id field`);
        } else if (!wish._id) {
          issues.push(`Wish ${index} (${wish.title}): Missing _id field`);
        }
      });

      if (issues.length > 0) {
        console.warn('ID字段问题:', issues);
      } else {
        console.log('所有心愿ID字段正常');
      }

      console.log('=========================');
    },

    // 修复心愿数据中的ID字段
    fixWishIds() {
      console.log('[wishStore] Fixing wish ID fields...');
      let fixedCount = 0;
      
      this.wishList = this.wishList.map(wish => {
        const hasIdIssue = !wish.id || !wish._id;
        
        if (hasIdIssue) {
          fixedCount++;
          return {
            ...wish,
            id: wish.id || wish._id,
            _id: wish._id || wish.id
          };
        }
        
        return wish;
      });
      
      if (fixedCount > 0) {
        this._saveToStorage();
        console.log(`[wishStore] Fixed ${fixedCount} wishes with ID issues`);
        uni.showToast({
          title: `修复了 ${fixedCount} 个心愿的ID问题`,
          icon: 'success'
        });
      } else {
        console.log('[wishStore] No ID issues found');
        uni.showToast({
          title: '所有心愿ID字段正常',
          icon: 'success'
        });
      }
      
      return fixedCount;
    },

    /**
     * 🚀 处理实时数据更新
     * 当收到实时推送时调用此方法
     */
    async _updateLocalFromRealtimeData(realtimeData) {
      try {
        console.log('[wishStore] 📝 收到实时心愿数据更新:', realtimeData.length, '个心愿');
        
        // 合并实时数据到本地
        const mergedData = this._mergeWishData(this.wishList, realtimeData);
        
        // 更新本地数据
        this.wishList = mergedData;
        
        // 清除缓存
        this._clearWishesCache();
        
        // 保存到本地存储
        this._saveToStorage();
        
        console.log('[wishStore] ✅ 实时心愿数据更新完成');
        
      } catch (error) {
        console.error('[wishStore] 处理实时心愿数据更新失败:', error);
      }
    },

    // 🚀 新增：支持新同步架构的方法

    /**
     * 添加心愿到列表（供实时同步调用）
     */
    addWishToList(wish) {
      const existingIndex = this.wishList.findIndex(w => w._id === wish._id);
      if (existingIndex === -1) {
        this.wishList.push({
          ...wish,
          id: wish._id // 确保有 id 字段
        });
        this._saveToStorage();
        console.log('[wishStore] 🚀 添加心愿到列表:', wish._id);
      }
    },

    /**
     * 更新列表中的心愿（供实时同步调用）
     */
    updateWishInList(wish) {
      const index = this.wishList.findIndex(w => w._id === wish._id);
      if (index !== -1) {
        this.wishList[index] = {
          ...wish,
          id: wish._id // 确保有 id 字段
        };
        this._saveToStorage();
        console.log('[wishStore] 🚀 更新列表中的心愿:', wish._id);
      }
    },

    /**
     * 从列表中移除心愿（供实时同步调用）
     */
    removeWishFromList(wishId) {
      const index = this.wishList.findIndex(w => w._id === wishId);
      if (index !== -1) {
        this.wishList.splice(index, 1);
        this._saveToStorage();
        console.log('[wishStore] 🚀 从列表中移除心愿:', wishId);
      }
    },

    /**
     * 从云端同步数据（供新同步管理器调用）- 优化版本
     */
    async syncFromCloud() {
      try {
        console.log('[wishStore] 🔄 从云端同步心愿数据...');

        const userStore = useUserStore();
        if (!userStore.isLogin) {
          console.warn('[wishStore] 用户未登录，跳过同步');
          return;
        }

        const wishCenter = uniCloud.importObject('wish-center');
        const result = await wishCenter.getWishList({
          page: 1,
          pageSize: 1000,
          includeCompleted: true
        });

        if (result.errCode === 0) {
          // 合并云端数据
          this.wishList = result.data.map(wish => ({
            ...wish,
            id: wish._id // 确保有 id 字段
          }));

          this._saveToStorage();
          console.log('[wishStore] ✅ 云端同步完成，共', result.data.length, '个心愿');
        } else {
          throw new Error(result.errMsg || '同步失败');
        }
      } catch (error) {
        console.error('[wishStore] 云端同步失败:', error);
        throw error;
      }
    },

    // ==================== 新增同步优化方法 ====================

    /**
     * 从同步中添加心愿（避免触发推送）
     */
    addWishFromSync(wish) {
      const existingIndex = this.wishList.findIndex(w => w.id === wish.id || w._id === wish._id)
      if (existingIndex === -1) {
        this.wishList.push({
          ...wish,
          id: wish._id || wish.id
        })
        this._saveToStorage()
        console.log(`[wishStore] 从同步添加心愿: ${wish.title}`)
      }
    },

    /**
     * 从同步中更新心愿（避免触发推送）
     */
    updateWishFromSync(wish) {
      const index = this.wishList.findIndex(w => w.id === wish.id || w._id === wish._id)
      if (index !== -1) {
        this.wishList[index] = {
          ...wish,
          id: wish._id || wish.id
        }
        this._saveToStorage()
        console.log(`[wishStore] 从同步更新心愿: ${wish.title}`)
      }
    },

    /**
     * 从同步中删除心愿（避免触发推送）
     */
    removeWishById(wishId) {
      const index = this.wishList.findIndex(w => w.id === wishId || w._id === wishId)
      if (index !== -1) {
        const removedWish = this.wishList.splice(index, 1)[0]
        this._saveToStorage()
        console.log(`[wishStore] 从同步删除心愿: ${removedWish.title}`)
      }
    },

    // 智能同步 - 只同步有差异的数据（用于下拉刷新）
    async smartSync() {
      console.log('[wishStore] Starting smart sync...');
      
      if (!this.isOnline) {
        console.warn('[wishStore] Network unavailable, skipping smart sync');
        return { hasUpdates: false, updatedCount: 0, reason: 'offline' };
      }

      try {
        // 1. 先上传本地待同步的数据
        const pendingWishes = this.wishList.filter(wish => wish._needSync);
        if (pendingWishes.length > 0) {
          console.log(`[wishStore] Uploading ${pendingWishes.length} pending wishes...`);
          await this.syncPendingData();
        }

        // 2. 获取云端数据摘要信息
        const wishCenter = uniCloud.importObject('wish-center');
        const summaryResult = await wishCenter.getSyncSummary();
        
        if (summaryResult.errCode !== 0) {
          throw new Error(summaryResult.errMsg || '获取云端心愿摘要失败');
        }

        const cloudSummary = summaryResult.data || {};
        console.log('[wishStore] Cloud summary received:', cloudSummary);

        // 3. 对比本地数据摘要
        const localSummary = this._generateLocalWishSummary();
        console.log('[wishStore] Local summary:', localSummary);

        // 4. 判断是否需要同步
        const needsSync = this._compareWishDataSummaries(localSummary, cloudSummary);
        
        if (!needsSync) {
          console.log('[wishStore] Data is up to date, no sync needed');
          return { hasUpdates: false, updatedCount: 0, reason: 'up_to_date' };
        }

        // 5. 执行增量同步
        console.log('[wishStore] Data differences detected, performing incremental sync...');
        const syncResult = await this._performWishIncrementalSync(cloudSummary);
        
        // 6. 清理可能的无效同步标记
        this.cleanupInvalidSyncMarkers();
        
        console.log('[wishStore] Smart sync completed:', syncResult);
        return syncResult;

      } catch (error) {
        console.error('[wishStore] Smart sync failed:', error);
        throw error;
      }
    },

    // 生成本地心愿数据摘要
    _generateLocalWishSummary() {
      return {
        count: this.wishList.length,
        lastModified: this._getLocalLastModified(),
        checksum: this._calculateWishesChecksum(this.wishList)
      };
    },

    // 计算心愿数据校验和
    _calculateWishesChecksum(wishes) {
      const dataStr = wishes
        .map(w => `${w._id}:${w.title}:${w.updateDate || w.createDate}:${w.isCompleted}`)
        .sort()
        .join('|');
      
      // 简单的字符串哈希（用于快速对比）
      let hash = 0;
      for (let i = 0; i < dataStr.length; i++) {
        const char = dataStr.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }
      return hash.toString(36);
    },

    // 对比心愿数据摘要
    _compareWishDataSummaries(localSummary, cloudSummary) {
      // 如果数量不同，需要同步
      if (localSummary.count !== cloudSummary.count) {
        console.log('[wishStore] Count difference detected:', localSummary.count, 'vs', cloudSummary.count);
        return true;
      }

      // 如果校验和不同，需要同步
      if (localSummary.checksum !== cloudSummary.checksum) {
        console.log('[wishStore] Checksum difference detected:', localSummary.checksum, 'vs', cloudSummary.checksum);
        return true;
      }

      // 如果云端最后修改时间更新，需要同步
      if (cloudSummary.lastModified && localSummary.lastModified) {
        const cloudTime = new Date(cloudSummary.lastModified).getTime();
        const localTime = new Date(localSummary.lastModified).getTime();
        
        if (cloudTime > localTime) {
          console.log('[wishStore] Cloud data is newer:', cloudSummary.lastModified, 'vs', localSummary.lastModified);
          return true;
        }
      }

      return false;
    },

    // 执行心愿增量同步
    async _performWishIncrementalSync(cloudSummary) {
      // 获取完整的云端数据
      const wishCenter = uniCloud.importObject('wish-center');
      const result = await wishCenter.getWishList({
        page: 1,
        pageSize: 1000,
        includeCompleted: true,
        includeDeleted: true
      });
      
      if (result.errCode !== 0) {
        throw new Error(result.errMsg || '获取云端心愿数据失败');
      }

      const cloudWishes = result.data || [];
      const beforeCount = this.wishList.length;

      // 使用现有的智能合并逻辑
      const mergedData = this._mergeWishData(this.wishList, cloudWishes);
      
      // 安全更新数据
      await this._safeUpdateWishes(mergedData);
      
      const afterCount = this.wishList.length;
      const updatedCount = Math.abs(afterCount - beforeCount);

      this.lastSyncTime = new Date().toISOString();
      this._saveToStorage();

      return {
        hasUpdates: true,
        updatedCount: updatedCount,
        beforeCount: beforeCount,
        afterCount: afterCount,
        reason: 'incremental_sync'
      };
    },

    // 安全更新心愿数据
    async _safeUpdateWishes(newData) {
      try {
        // 使用 nextTick 确保DOM更新时机合适
        await new Promise((resolve) => {
          if (this.$nextTick) {
            this.$nextTick(() => {
              this.wishList = newData.map(wish => ({
                ...wish,
                id: wish._id // 确保有 id 字段供前端组件使用
              }));
              resolve();
            });
          } else {
            setTimeout(() => {
              this.wishList = newData.map(wish => ({
                ...wish,
                id: wish._id // 确保有 id 字段供前端组件使用
              }));
              resolve();
            }, 0);
          }
        });
      } catch (error) {
        console.error('[wishStore] Error in safe update, falling back to direct assignment:', error);
        this.wishList = newData.map(wish => ({
          ...wish,
          id: wish._id // 确保有 id 字段供前端组件使用
        }));
      }
    },

    /**
     * 统一的响应式更新触发器
     * @param {string} action - 操作类型 ('complete', 'delete', 'add', 'update')
     * @param {string} wishId - 心愿ID
     */
    _triggerReactiveUpdate(action, wishId) {
      console.log(`[wishStore] 触发响应式更新: ${action} - ${wishId}`);

      // 1. 强制触发 getter 重新计算
      this.listUpdateCounter++

      // 2. 发送全局事件通知UI组件
      uni.$emit('wish-list-updated', {
        timestamp: Date.now(),
        action: action,
        wishId: wishId,
        updateCounter: this.listUpdateCounter
      })

      // 3. 使用 nextTick 确保DOM更新
      this.$nextTick && this.$nextTick(() => {
        console.log(`[wishStore] 响应式更新完成: ${action} - ${wishId}`)
      })

      console.log(`[wishStore] 响应式更新已触发，当前计数器: ${this.listUpdateCounter}`)
    },

    // 清理无效的同步标记（多设备冲突后的清理）
    cleanupInvalidSyncMarkers() {
      let cleanedCount = 0;
      
      this.wishList.forEach(wish => {
        // 如果心愿有同步标记但缺少必要字段，清除标记
        if (wish._needSync && (!wish._id || !wish.title)) {
          console.log(`[wishStore] 清理无效的同步标记: ${wish.title || '未知心愿'}`);
          delete wish._needSync;
          cleanedCount++;
        }
      });
      
      if (cleanedCount > 0) {
        console.log(`[wishStore] 已清理 ${cleanedCount} 个无效的同步标记`);
        this._saveToStorage();
      }
      
      return cleanedCount;
    }
  }
})