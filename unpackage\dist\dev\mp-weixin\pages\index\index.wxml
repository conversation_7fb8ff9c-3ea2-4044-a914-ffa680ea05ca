<view ref="containerRef" bindtap="{{h}}" bindtouchstart="{{i}}" bindtouchmove="{{j}}" bindtouchend="{{k}}" class="{{['wish-container', l && 'drag-mode', m && 'pull-refresh-disabled', n && 'gesture-disabled']}}"><group-selector bindgroupChange="{{a}}" u-i="77241304-0" bind:__l="__l"/><view class="wish-list" id="wishListScrollView" bindtouchmove="{{f}}"><view class="wish-list-content"><transition-group wx:if="{{d}}" u-s="{{['d']}}" class="list-transition-group" key="{{c}}" u-i="77241304-1" bind:__l="__l" u-p="{{d}}"><wish-card wx:for="{{b}}" wx:for-item="wish" wx:key="b" class="r-i-f" bindcardSwipeStart="{{wish.c}}" bindcardOpen="{{wish.d}}" bindcardClose="{{wish.e}}" bindcardScrollDetected="{{wish.f}}" bindcomplete="{{wish.g}}" binddelete="{{wish.h}}" bindshare="{{wish.i}}" binddragStart="{{wish.j}}" binddragMove="{{wish.k}}" binddragEnd="{{wish.l}}" u-r="wishCards" u-i="{{wish.m}}" bind:__l="__l" u-p="{{wish.n}}"/></transition-group><view wx:if="{{e}}" class="empty-list"><view class="empty-text">暂无心愿，点击下方按钮添加</view></view></view></view><view class="add-wish-btn" catchtap="{{g}}"><view class="plus-icon"><view class="h-line"></view><view class="v-line"></view></view></view></view>