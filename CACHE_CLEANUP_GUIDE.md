# 缓存清理指南

## 问题描述
如果遇到以下错误：
```
Error: ENOENT: no such file or directory, open 'C:/Users/<USER>/OneDrive/wishlist-cloud-T/wishlist-uniapp/unpackage/dist/dev/mp-weixin/utils/toastInterceptor.js'
```

这是因为编译缓存中还保留了对已删除文件的引用。

## 解决方案

### 方法1：HBuilderX 清理缓存
1. 关闭 HBuilderX
2. 删除项目根目录下的 `unpackage` 文件夹
3. 删除项目根目录下的 `.hbuilderx` 文件夹（如果存在）
4. 重新打开 HBuilderX
5. 重新编译项目

### 方法2：手动清理
1. 关闭所有相关的开发工具（HBuilderX、微信开发者工具等）
2. 在项目根目录执行以下命令：
   ```powershell
   Remove-Item -Recurse -Force "unpackage" -ErrorAction SilentlyContinue
   Remove-Item -Recurse -Force ".hbuilderx" -ErrorAction SilentlyContinue
   ```
3. 重新编译项目

### 方法3：重启开发环境
如果上述方法无效：
1. 关闭所有开发工具
2. 重启电脑
3. 重新打开项目并编译

## 预防措施
- 删除文件后及时清理编译缓存
- 定期清理 `unpackage` 目录
- 使用版本控制时忽略 `unpackage` 和 `.hbuilderx` 目录

## 已删除的文件
以下文件已被删除，不应再被引用：
- `utils/toastInterceptor.js` - 已删除（过于复杂的解决方案）
- `test/` 目录下的测试文件 - 已删除（简化项目）

## 当前状态
✅ 所有代码中已清理对 `toastInterceptor` 的引用  
✅ 版本冲突问题已通过更简洁的方案解决  
✅ 弹窗问题已通过直接删除相关代码解决  

如果仍然遇到问题，请确保：
1. HBuilderX 已完全关闭
2. 微信开发者工具已关闭
3. 编译缓存已清理
4. 重新编译项目
