{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"wish-container\"\n\t\t  ref=\"containerRef\"\n\t\t  @click=\"closeAllSwipeActions\"\n\t\t  @touchstart=\"handleContainerTouchStart\"\n\t\t  @touchmove=\"handleContainerTouchMove\"\n\t\t  @touchend=\"handleContainerTouchEnd\"\n\t\t  :class=\"{'drag-mode': isDragging, 'pull-refresh-disabled': isDragging || isPullRefreshDisabled, 'gesture-disabled': isPullRefreshDisabled}\">\n\t\t<!-- 分组选择器 -->\n\t\t<GroupSelector @group-change=\"onGroupChange\" />\n\n\t\t<!-- 心愿列表 -->\n\t\t<view class=\"wish-list\" id=\"wishListScrollView\" @touchmove=\"onCardListTouch\">\n\t\t\t<view class=\"wish-list-content\">\n\t\t\t\t<!-- 心愿卡片 -->\n\t\t\t\t<transition-group name=\"list-item\" tag=\"view\" class=\"list-transition-group\" :key=\"forceUpdateKey\">\n\t\t\t\t\t<WishCard\n\t\t\t\t\t\tv-for=\"(wish, index) in wishStore.currentGroupWishes\"\n\t\t\t\t\t\t:key=\"`${wish.id || wish._id}-${forceUpdateKey}`\"\n\t\t\t\t\t\t:wish=\"wish\"\n\t\t\t\t\t\t:index=\"index\"\n\t\t\t\t\t\t:active-card-id=\"activeCardId\"\n\t\t\t\t\t\t:is-draggable=\"true\"\n\t\t\t\t\t\t:is-dragging-element=\"isDragging && dragCurrentIndex === index\"\n\t\t\t\t\t\t@card-swipe-start=\"handleCardSwipeStart\"\n\t\t\t\t\t\t@card-open=\"handleCardOpen\"\n\t\t\t\t\t\t@card-close=\"handleCardClose\"\n\t\t\t\t\t\t@card-scroll-detected=\"handleCardScrollDetected\"\n\t\t\t\t\t\t@complete=\"completeWish\"\n\t\t\t\t\t\t@delete=\"deleteWish\"\n\t\t\t\t\t\t@share=\"shareWish\"\n\t\t\t\t\t\t@drag-start=\"handleDragStart\"\n\t\t\t\t\t\t@drag-move=\"handleDragMove\"\n\t\t\t\t\t\t@drag-end=\"handleDragEnd\"\n\t\t\t\t\t\tref=\"wishCards\"\n\t\t\t\t\t/>\n\t\t\t\t</transition-group>\n\t\t\t\t\n\t\t\t\t<!-- 空列表状态 -->\n\t\t\t\t<view class=\"empty-list\" v-if=\"wishStore.currentGroupWishes.length === 0\">\n\t\t\t\t\t<view class=\"empty-text\">暂无心愿，点击下方按钮添加</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 添加按钮 -->\n\t\t<view class=\"add-wish-btn\" @click.stop=\"goToAddWish\">\n\t\t\t<view class=\"plus-icon\">\n\t\t\t\t<view class=\"h-line\"></view>\n\t\t\t\t<view class=\"v-line\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\n\t</view>\n</template>\n\n<script>\n\timport { useWishStore } from '@/store/wish.js'\n\timport { useGroupStore } from '@/store/group.js'\n\timport { useUserStore } from '@/store/user.js'\n\timport { useMessageStore } from '@/store/message.js'\n\timport { useCommentStore } from '@/store/comment.js'  // 确保主包使用 comment store\n\timport { ref, reactive, onMounted, nextTick, onUnmounted, watchEffect, computed, getCurrentInstance } from 'vue'\n\timport syncManager from '@/utils/syncManager.js'\n\timport animationMonitor, { AnimationUtils } from '@/utils/animationPerformance.js'\n\timport gestureManager from '@/utils/gestureManager.js'\n\timport { devLog } from '@/utils/envUtils.js'\n\n\timport WishCard from '@/components/WishCard/WishCard.vue'\n\timport GroupSelector from '@/components/GroupSelector/GroupSelector.vue'\n\t\n\texport default {\n\t\tcomponents: {\n\t\t\tWishCard,\n\t\t\tGroupSelector\n\t\t},\n\t\t// 添加页面滚动处理函数\n\t\tonPageScroll(e) {\n\t\t\t// 页面滚动时关闭打开的卡片\n\t\t\tif (e && e.scrollTop > 10) {\n\t\t\t\t// 发送事件给组件内部处理\n\t\t\t\tuni.$emit('page-scroll-close-cards', {\n\t\t\t\t\tscrollTop: e.scrollTop,\n\t\t\t\t\ttimestamp: Date.now()\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t// 新增的方法：强制重置卡片状态\n\t\tforceResetCard(card) {\n\t\t\ttry {\n\t\t\t\tif (card.moveX !== undefined && card.startX !== undefined) {\n\t\t\t\t\tcard.moveX = card.startX;\n\t\t\t\t}\n\t\t\t\tif (card.isOpen !== undefined) {\n\t\t\t\t\tcard.isOpen = false;\n\t\t\t\t}\n\t\t\t\tif (card.resetSwipe) {\n\t\t\t\t\tcard.resetSwipe();\n\t\t\t\t}\n\t\t\t\tif (card.swipeRef && card.swipeRef.style) {\n\t\t\t\t\tcard.swipeRef.style.transform = \"translateX(0px)\";\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tdevLog.error(\"重置卡片状态失败\", e);\n\t\t\t}\n\t\t},\n\t\t// 新增的方法：通知所有卡片组件滚动事件发生\n\t\tnotifyCardsOfScroll() {\n\t\t\tuni.$emit('page-scroll-event', {\n\t\t\t\ttimestamp: Date.now(),\n\t\t\t\tshouldClose: true\n\t\t\t});\n\t\t},\n\t\t// 添加页面触底处理\n\t\tonReachBottom() {\n\t\t\t// 发送事件关闭左滑状态\n\t\t\tuni.$emit('page-reach-bottom-close-cards', {\n\t\t\t\ttimestamp: Date.now()\n\t\t\t});\n\t\t},\n\t\t// 添加页面下拉刷新 - 改为组合式API外部版本\n\t\tasync onPullDownRefresh() {\n\t\t\t// 通过事件通知 setup() 函数内的逻辑处理\n\t\t\tuni.$emit('page-pull-down-refresh-start', {\n\t\t\t\ttimestamp: Date.now()\n\t\t\t});\n\t\t},\n\t\t// 微信小程序分享给朋友\n\t\tonShareAppMessage(res) {\n\t\t\t// 判断是否是从分享按钮触发 - WishCard组件\n\t\t\tif (res.from === 'button' && res.target && res.target.dataset && res.target.dataset.index !== undefined) {\n\t\t\t\t// 获取当前分享的心愿对象\n\t\t\t\tconst index = Number(res.target.dataset.index || 0);\n\t\t\t\tconst wish = this.wishList[index] || {};\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\ttitle: wish.title || '分享我的心愿',\n\t\t\t\t\tpath: `/pages/wishDetail/wishDetail?id=${wish.id}`,\n\t\t\t\t\timageUrl: Array.isArray(wish.image) && wish.image.length > 0 ? \n\t\t\t\t\t\twish.image[0] : (wish.image || '/static/images/share-image.png')\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 判断是否是从分组分享按钮触发 - GroupSelector组件\n\t\t\tif (res.from === 'button' && res.target && res.target.dataset && \n\t\t\t   (res.target.dataset.groupId || res.target.dataset.shareType === 'group')) {\n\t\t\t\tconst groupId = res.target.dataset.groupId;\n\t\t\t\tconst groupName = res.target.dataset.groupName || '分组';\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\ttitle: `分享\"${groupName}\"分组的心愿清单`,\n\t\t\t\t\tpath: `/pages/index/index?groupId=${groupId}`,\n\t\t\t\t\timageUrl: '/static/images/share-image.png'\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 尝试从本地存储获取分享信息\n\t\t\tconst shareGroupInfo = uni.getStorageSync('current_share_group');\n\t\t\tif (shareGroupInfo && Date.now() - shareGroupInfo.timestamp < 3000) { // 3秒内有效\n\t\t\t\tconst groupId = shareGroupInfo.groupId;\n\t\t\t\tconst groupName = shareGroupInfo.groupName;\n\t\t\t\t\n\t\t\t\t// 清除临时存储\n\t\t\t\tuni.removeStorageSync('current_share_group');\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\ttitle: `分享\"${groupName}\"分组的心愿清单`,\n\t\t\t\t\tpath: `/pages/index/index?groupId=${groupId}`,\n\t\t\t\t\timageUrl: '/static/images/share-image.png'\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 检查是否有从分组分享按钮触发的分享\n\t\t\tconst currentGroupId = this.wishStore.currentGroupId;\n\t\t\tconst groupStore = this.groupStore;\n\t\t\tconst currentGroup = groupStore.getGroupById(currentGroupId);\n\t\t\t\n\t\t\t// 如果当前在特定分组且不是全部分组，分享该分组\n\t\t\tif (currentGroup && currentGroupId !== 'all') {\n\t\t\t\treturn {\n\t\t\t\t\ttitle: `分享\"${currentGroup.name}\"分组的心愿清单`,\n\t\t\t\t\tpath: `/pages/index/index?groupId=${currentGroupId}`,\n\t\t\t\t\timageUrl: '/static/images/share-image.png'\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 默认分享内容\n\t\t\treturn {\n\t\t\t\ttitle: '心愿清单 - 记录你的美好心愿',\n\t\t\t\tpath: '/pages/index/index',\n\t\t\t\timageUrl: '/static/images/share-image.png'\n\t\t\t}\n\t\t},\n\t\t// 微信小程序分享到朋友圈\n\t\tonShareTimeline() {\n\t\t\t// 尝试从本地存储获取分享信息\n\t\t\tconst shareGroupInfo = uni.getStorageSync('current_share_group');\n\t\t\tif (shareGroupInfo && Date.now() - shareGroupInfo.timestamp < 3000) { // 3秒内有效\n\t\t\t\tconst groupId = shareGroupInfo.groupId;\n\t\t\t\tconst groupName = shareGroupInfo.groupName;\n\t\t\t\t\n\t\t\t\t// 不清除临时存储，因为可能还需要在onShareAppMessage中使用\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\ttitle: `分享\"${groupName}\"分组的心愿清单`,\n\t\t\t\t\tquery: `groupId=${groupId}`,\n\t\t\t\t\timageUrl: '/static/images/share-group-image.png'\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 检查当前分组\n\t\t\tconst currentGroupId = this.wishStore.currentGroupId;\n\t\t\tconst groupStore = this.groupStore;\n\t\t\tconst currentGroup = groupStore.getGroupById(currentGroupId);\n\t\t\t\n\t\t\t// 如果当前在特定分组且不是全部分组，分享该分组\n\t\t\tif (currentGroup && currentGroupId !== 'all') {\n\t\t\t\treturn {\n\t\t\t\t\ttitle: `分享\"${currentGroup.name}\"分组的心愿清单`,\n\t\t\t\t\tquery: `groupId=${currentGroupId}`,\n\t\t\t\t\timageUrl: '/static/images/share-group-image.png'\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 默认分享内容\n\t\t\treturn {\n\t\t\t\ttitle: '心愿清单 - 记录你的美好心愿',\n\t\t\t\tquery: '',\n\t\t\t\timageUrl: '/static/images/share-image.png'\n\t\t\t}\n\t\t},\n\t\tsetup() {\n\t\t\tconst wishStore = useWishStore()\n\t\t\tconst groupStore = useGroupStore()\n\t\t\tconst userStore = useUserStore()\n\t\t\tconst messageStore = useMessageStore()\n\t\t\tconst commentStore = useCommentStore()  // 确保主包使用 comment store\n\t\t\tconst wishCards = ref([])\n\t\t\tconst containerRef = ref(null)\n\t\t\tconst contentHeight = ref(0)\n\t\t\tconst windowHeight = ref(0)\n\t\t\tconst activeCardId = ref(null)\n\n\t\t\t// 动画性能监控初始化\n\t\t\tonMounted(() => {\n\t\t\t\t// 启动FPS监控（仅开发环境）\n\t\t\t\tif (process.env.NODE_ENV === 'development') {\n\t\t\t\t\tanimationMonitor.startFPSMonitoring();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 设置手势管理器的页面实例\n\t\t\t\tgestureManager.setPageInstance(getCurrentInstance());\n\t\t\t})\n\t\t\tconst lastTouchY = ref(undefined)\n\t\t\tconst isSyncing = ref(false)\n\t\t\tconst isPullRefreshDisabled = ref(false)\n\t\t\t\n\t\t\t// 拖拽状态\n\t\t\tconst isDragging = ref(false)\n\t\t\tconst dragStartIndex = ref(-1)\n\t\t\tconst dragCurrentIndex = ref(-1)\n\t\t\tconst initialOrder = ref([])\n\t\t\tconst dragStartY = ref(0)\n\t\t\tconst dragThrottled = ref(false)\n\t\t\tconst cardHeight = ref(160) // 默认估计高度，单位rpx转px\n\t\t\t\n\t\t\t// 计算属性：当前分组的心愿列表\n\t\t\tconst wishList = computed(() => wishStore.currentGroupWishes)\n\t\t\t\n\t\t\t// 添加强制更新标记\n\t\t\tconst forceUpdateKey = ref(0)\n\t\t\t\n\t\t\t// 是否已登录\n\t\t\tconst isLogin = computed(() => userStore.isLoggedIn)\n\t\t\t\n\t\t\t// 获取窗口高度\n\t\t\tuni.getSystemInfo({\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\twindowHeight.value = res.windowHeight\n\t\t\t\t}\n\t\t\t})\n\t\t\t\n\t\t\t// 监听页面显示事件\n\t\t\tuni.$on('page-show', () => {\n\t\t\t\t// 页面显示时的处理\n\t\t\t})\n\n\t\t\t// 监听页面滚动关闭卡片事件\n\t\t\tuni.$on('page-scroll-close-cards', (data) => {\n\t\t\t\tdevLog.log('[Index] 收到页面滚动关闭卡片事件:', data);\n\t\t\t\tif (activeCardId.value !== null) {\n\t\t\t\t\tcloseAllSwipeActions();\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t// 监听页面触底关闭卡片事件\n\t\t\tuni.$on('page-reach-bottom-close-cards', (data) => {\n\t\t\t\tdevLog.log('[Index] 收到页面触底关闭卡片事件:', data);\n\t\t\t\tcloseAllSwipeActions();\n\t\t\t})\n\n\t\t\t// 监听下拉刷新开始事件（从外部onPullDownRefresh触发）\n\t\t\tuni.$on('page-pull-down-refresh-start', async (data) => {\n\t\t\t\tdevLog.log('[Index] 收到下拉刷新开始事件:', data);\n\n\t\t\t\t// 多重检查：手势管理器 + 本地状态\n\t\t\t\tif (!gestureManager.canStartGesture('pullRefresh') || isPullRefreshDisabled.value) {\n\t\t\t\t\tdevLog.log('[Index] 拖拽进行中或已被禁用，忽略下拉刷新');\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 开始下拉刷新\n\t\t\t\tif (!gestureManager.startPullRefresh()) {\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 关闭左滑状态\n\t\t\t\tcloseAllSwipeActions();\n\n\t\t\t\t// 执行智能同步\n\t\t\t\ttry {\n\t\t\t\t\tawait forceSyncData();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('[Index] 下拉刷新同步失败:', error);\n\t\t\t\t} finally {\n\t\t\t\t\t// 结束下拉刷新状态\n\t\t\t\t\tgestureManager.endPullRefresh();\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t// 监听下拉刷新事件（保留兼容性）\n\t\t\tuni.$on('page-pull-down-refresh', async (data) => {\n\t\t\t\tdevLog.log('[Index] 收到下拉刷新事件:', data);\n\t\t\t\t// 关闭左滑状态\n\t\t\t\tcloseAllSwipeActions();\n\n\t\t\t\t// 执行手动同步\n\t\t\t\ttry {\n\t\t\t\t\tawait forceSyncData();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('[Index] 下拉刷新同步失败:', error);\n\t\t\t\t} finally {\n\t\t\t\t\t// 结束下拉刷新状态\n\t\t\t\t\tgestureManager.endPullRefresh();\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t}\n\t\t\t})\n\t\t\t\n\t\t\t// 监听手势管理器的下拉刷新禁用事件\n\t\t\tuni.$on('gesture-disable-pull-refresh', (data) => {\n\t\t\t\tdevLog.log('[Index] 手势管理器禁用下拉刷新:', data);\n\t\t\t\t// 这里可以添加额外的禁用逻辑\n\t\t\t\tisPullRefreshDisabled.value = true;\n\t\t\t})\n\n\t\t\t// 监听手势管理器的下拉刷新启用事件\n\t\t\tuni.$on('gesture-enable-pull-refresh', (data) => {\n\t\t\t\tdevLog.log('[Index] 手势管理器启用下拉刷新:', data);\n\t\t\t\t// 延迟恢复，确保拖拽完全结束\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tisPullRefreshDisabled.value = false;\n\t\t\t\t}, 200);\n\t\t\t})\n\t\t\t\n\t\t\t// 监听心愿列表更新事件（统一处理所有操作）\n\t\t\tuni.$on('wish-list-updated', async (data) => {\n\t\t\t\tconsole.log('[Index] 收到心愿列表更新事件:', data);\n\n\t\t\t\t// 强制刷新页面显示\n\t\t\t\tawait nextTick();\n\n\t\t\t\t// 统一处理所有操作类型：delete, complete, restore, add, update\n\t\t\t\tconsole.log(`[Index] 处理${data.action}操作，强制刷新界面`);\n\n\t\t\t\t// 确保计数器同步\n\t\t\t\tif (data.updateCounter && data.updateCounter > wishStore.listUpdateCounter) {\n\t\t\t\t\twishStore.listUpdateCounter = data.updateCounter;\n\t\t\t\t}\n\n\t\t\t\t// 强制组件重新渲染\n\t\t\t\tforceUpdateKey.value++;\n\n\t\t\t\t// 再次确保响应式更新\n\t\t\t\tawait nextTick();\n\n\t\t\t\tconsole.log(`[Index] ${data.action}操作界面更新完成，forceUpdateKey: ${forceUpdateKey.value}`);\n\t\t\t})\n\n\t\t\t// 监听用户登录成功事件\n\t\t\tuni.$on('user-login-success', async (data) => {\n\t\t\t\tconsole.log('[Index] User login success event received:', data);\n\t\t\t\t\n\t\t\t\t// 确保在重新初始化期间不触发手势冲突\n\t\t\t\tconst wasDisabled = isPullRefreshDisabled.value;\n\t\t\t\tisPullRefreshDisabled.value = true;\n\t\t\t\t\n\t\t\t\t// 重新初始化stores\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('[Index] Reinitializing stores after login...');\n\t\t\t\t\t\n\t\t\t\t\t// 温和地清空数据，避免DOM错误\n\t\t\t\t\tawait new Promise(resolve => {\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t// 使用store提供的清空方法而不是直接赋值\n\t\t\t\t\t\t\tgroupStore.resetGroups();\n\t\t\t\t\t\t\twishStore.clearLocalData();\n\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t}, 100);\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 按顺序重新初始化\n\t\t\t\t\t// 1. 先初始化分组数据\n\t\t\t\t\tawait groupStore.initGroups();\n\t\t\t\t\tconsole.log('[Index] Group store reinitialized');\n\t\t\t\t\t\n\t\t\t\t\t// 2. 再初始化心愿数据\n\t\t\t\t\tawait wishStore.initWishList();\n\t\t\t\t\tconsole.log('[Index] Wish store reinitialized');\n\t\t\t\t\t\n\t\t\t\t\t// 3. 最后初始化消息数据\n\t\t\t\t\tawait messageStore.initMessages();\n\t\t\t\t\tconsole.log('[Index] Message store reinitialized');\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('[Index] Stores reinitialized successfully');\n\t\t\t\t\t\n\t\t\t\t\t// 刷新页面数据完成\n\t\t\t\t\tconsole.log('[Index] Page data refreshed after login');\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('[Index] Error reinitializing stores:', error);\n\t\t\t\t} finally {\n\t\t\t\t\t// 恢复下拉刷新状态，延迟确保数据初始化完成\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tif (!isDragging.value) {\n\t\t\t\t\t\t\tisPullRefreshDisabled.value = wasDisabled;\n\t\t\t\t\t\t}\n\t\t\t\t\t}, 500);\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t// 监听页面加载事件\n\t\t\tuni.$on('page-load', async (data) => {\n\t\t\t\tconsole.log('[Index] 收到页面加载事件:', data);\n\t\t\t\tconst options = data.options;\n\n\t\t\t\t// 如果有groupId参数，设置当前分组\n\t\t\t\tif (options && options.groupId) {\n\t\t\t\t\tconsole.log('[Index] Setting group from URL params:', options.groupId);\n\t\t\t\t\twishStore.setCurrentGroup(options.groupId);\n\t\t\t\t}\n\n\t\t\t\t// 初始化数据（按顺序）\n\t\t\t\ttry {\n\t\t\t\t\t// 1. 先初始化分组数据\n\t\t\t\t\tconsole.log('[Index] Initializing groups...');\n\t\t\t\t\tawait groupStore.initGroups();\n\n\t\t\t\t\t// 2. 再初始化心愿数据\n\t\t\t\t\tconsole.log('[Index] Initializing wishes...');\n\t\t\t\t\tawait wishStore.initWishList();\n\n\t\t\t\t\tconsole.log('[Index] Data initialization completed');\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('[Index] Data initialization failed:', error);\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t// 监听页面显示生命周期事件\n\t\t\tuni.$on('page-show-lifecycle', (data) => {\n\t\t\t\tconsole.log('[Index] 收到页面显示生命周期事件:', data);\n\n\t\t\t\t// 广播页面显示事件，用于通知子组件（例如GroupSelector）\n\t\t\t\tuni.$emit('page-show');\n\n\t\t\t\t// 如果用户已登录，检查并刷新数据\n\t\t\t\tif (userStore.isLoggedIn) {\n\t\t\t\t\tconsole.log('[Index] User is logged in, refreshing data...');\n\n\t\t\t\t\t// 从本地存储刷新最新的心愿数据\n\t\t\t\t\twishStore.refreshWishList();\n\n\t\t\t\t\t// 检查未读消息并更新徽标\n\t\t\t\t\tcheckUnreadMessages();\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('[Index] User not logged in');\n\n\t\t\t\t\t// 用户未登录时，确保有默认分组可用\n\t\t\t\t\tif (groupStore.getAllGroups.length === 0) {\n\t\t\t\t\t\tconsole.log('[Index] Ensuring default groups are available');\n\t\t\t\t\t\tgroupStore.ensureDefaultGroups();\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 页面显示完成\n\t\t\t\tconsole.log('[Index] Page shown');\n\t\t\t})\n\n\t\t\t// 监听页面隐藏生命周期事件\n\t\t\tuni.$on('page-hide-lifecycle', (data) => {\n\t\t\t\tconsole.log('[Index] 收到页面隐藏生命周期事件:', data);\n\t\t\t\tonPageHide();\n\t\t\t})\n\n\t\t\t// 🚀 监听静默刷新事件（多设备冲突时触发）\n\t\t\tuni.$on('silent-refresh-data', async (data) => {\n\t\t\t\tconsole.log('[Index] 收到静默刷新事件:', data);\n\n\t\t\t\ttry {\n\t\t\t\t\t// 静默刷新数据，不显示任何加载提示\n\t\t\t\t\tawait forceSyncData();\n\t\t\t\t\tconsole.log('[Index] 静默刷新完成');\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('[Index] 静默刷新失败:', error);\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t// 简化的拖拽状态管理\n\t\t\tuni.$on('lock-page-scroll', (data) => {\n\t\t\t\tconsole.log('锁定页面滚动', data);\n\t\t\t\t// 发送事件给其他组件\n\t\t\t\tuni.$emit('page-scroll-locked', {\n\t\t\t\t\ttimestamp: Date.now(),\n\t\t\t\t\tsource: data.source || 'unknown'\n\t\t\t\t});\n\t\t\t})\n\t\t\t\n\t\t\t// 在组件卸载时取消事件监听\n\t\t\tonUnmounted(() => {\n\t\t\t\tuni.$off('page-show')\n\t\t\t\tuni.$off('page-scroll-close-cards')\n\t\t\t\tuni.$off('page-reach-bottom-close-cards')\n\t\t\t\tuni.$off('page-pull-down-refresh')\n\t\t\t\tuni.$off('page-pull-down-refresh-start')\n\t\t\t\tuni.$off('page-load')\n\t\t\t\tuni.$off('page-show-lifecycle')\n\t\t\t\tuni.$off('page-hide-lifecycle')\n\t\t\t\tuni.$off('lock-page-scroll')\n\t\t\t\tuni.$off('user-login-success')\n\t\t\t\tuni.$off('gesture-disable-pull-refresh')\n\t\t\t\tuni.$off('gesture-enable-pull-refresh')\n\t\t\t\tuni.$off('wish-list-updated')\n\t\t\t})\n\n\t\t\t// 页面隐藏时执行\n\t\t\tconst onPageHide = () => {\n\t\t\t\tconsole.log('[onPageHide] 页面隐藏');\n\t\t\t\t// 关闭任何可能打开的卡片\n\t\t\t\tcloseAllSwipeActions();\n\t\t\t\t// 重置手势状态，确保页面隐藏时清理所有手势状态\n\t\t\t\tgestureManager.resetAll();\n\t\t\t}\n\t\t\t\n\t\t\t// 简化的页面初始化\n\t\t\tconst initializePage = () => {\n\t\t\t\t// 页面初始化逻辑\n\t\t\t\tconsole.log('页面初始化完成')\n\t\t\t}\n\t\t\t\n\t\t\t// 处理开始拖拽\n\t\t\tconst handleDragStart = (data) => {\n\t\t\t\tconsole.log('开始拖拽，数据:', data);\n\t\t\t\t\n\t\t\t\t// 检查手势管理器是否允许拖拽\n\t\t\t\tif (!gestureManager.canStartGesture('drag')) {\n\t\t\t\t\tconsole.log('[Index] 下拉刷新进行中，忽略拖拽开始');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 立即禁用下拉刷新\n\t\t\t\tisPullRefreshDisabled.value = true;\n\t\t\t\t\n\t\t\t\t// 开始拖拽\n\t\t\t\tif (!gestureManager.startDrag({ cardId: data.wishId, index: data.index })) {\n\t\t\t\t\t// 如果拖拽开始失败，恢复下拉刷新\n\t\t\t\t\tisPullRefreshDisabled.value = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 关闭所有已打开的卡片\n\t\t\t\tcloseAllSwipeActions();\n\t\t\t\t\n\t\t\t\t// 设置拖拽状态\n\t\t\t\tisDragging.value = true;\n\t\t\t\tdragStartIndex.value = data.index;\n\t\t\t\tdragCurrentIndex.value = data.index;\n\t\t\t\tdragStartY.value = data.clientY;\n\t\t\t\t\n\t\t\t\t// 阻止页面滚动\n\t\t\t\tuni.$emit('lock-page-scroll', { source: 'drag' });\n\t\t\t\t\n\t\t\t\t// 记录当前心愿列表顺序，用于重排序\n\t\t\t\tinitialOrder.value = wishList.value.map(wish => wish.id);\n\t\t\t\tconsole.log('初始心愿顺序:', initialOrder.value);\n\t\t\t\tconsole.log('当前心愿列表数量:', wishList.value.length);\n\t\t\t\t\n\t\t\t\t// 获取卡片实际高度，用于后续拖拽计算\n\t\t\t\ttry {\n\t\t\t\t\tconst query = uni.createSelectorQuery();\n\t\t\t\t\tquery.select('.wish-card-container').boundingClientRect(data => {\n\t\t\t\t\t\tif (data && data.height) {\n\t\t\t\t\t\t\t// 保存卡片实际高度以供拖拽计算使用\n\t\t\t\t\t\t\tcardHeight.value = data.height;\n\t\t\t\t\t\t\tconsole.log('获取到卡片实际高度:', cardHeight.value);\n\t\t\t\t\t\t}\n\t\t\t\t\t}).exec();\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取卡片高度失败:', e);\n\t\t\t\t\t// 使用默认高度\n\t\t\t\t\tcardHeight.value = 160; // 默认估计高度，单位rpx转px\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 静默振动反馈\n\t\t\t\ttry {\n\t\t\t\t\tuni.vibrateShort();\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('振动失败:', e);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log('拖拽模式初始化完成，当前拖拽索引:', dragCurrentIndex.value);\n\t\t\t}\n\t\t\t\n\t\t\t// 处理拖拽移动\n\t\t\tconst handleDragMove = (data) => {\n\t\t\t\tif (!isDragging.value) {\n\t\t\t\t\tconsole.log('非拖拽状态，忽略移动事件');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 计算移动距离\n\t\t\t\tconst moveDistance = data.clientY - dragStartY.value;\n\t\t\t\tconsole.log('拖拽移动:', {\n\t\t\t\t\tcurrentY: data.clientY,\n\t\t\t\t\tstartY: dragStartY.value,\n\t\t\t\t\tmoveDistance: moveDistance,\n\t\t\t\t\tcurrentIndex: dragCurrentIndex.value\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 如果移动距离太小，忽略这次移动\n\t\t\t\tif (Math.abs(moveDistance) < 5) { // 降低阈值，提高灵敏度\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 防止短时间内多次触发\n\t\t\t\tif (dragThrottled.value) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 设置节流标志\n\t\t\t\tdragThrottled.value = true;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tdragThrottled.value = false;\n\t\t\t\t}, 50); // 降低节流时间，提高响应性\n\t\t\t\t\n\t\t\t\t// 确定移动方向\n\t\t\t\tconst direction = moveDistance < 0 ? 'up' : 'down';\n\t\t\t\tconst currentIndex = dragCurrentIndex.value;\n\t\t\t\tlet targetIndex = -1;\n\t\t\t\t\n\t\t\t\t// 计算移动距离与卡片高度的比例\n\t\t\t\t// 估算卡片高度为100px，也可以从DOM获取实际高度\n\t\t\t\tconst moveRatio = Math.abs(moveDistance) / cardHeight.value;\n\t\t\t\t\n\t\t\t\t// 根据移动比例计算应该移动的卡片数量\n\t\t\t\t// 移动超过半个卡片高度时交换位置\n\t\t\t\tconst shouldMove = moveRatio >= 0.5;\n\t\t\t\t\n\t\t\t\tif (direction === 'up' && currentIndex > 0 && shouldMove) {\n\t\t\t\t\t// 向上拖动\n\t\t\t\t\ttargetIndex = currentIndex - 1;\n\t\t\t\t\tconsole.log('向上拖动，目标索引:', targetIndex);\n\t\t\t\t\t\n\t\t\t\t\t// 检查目标卡片是否在可视区域内，如果不在则滚动到可见\n\t\t\t\t\tcheckTargetVisibility(targetIndex);\n\t\t\t\t} else if (direction === 'down' && currentIndex < wishList.value.length - 1 && shouldMove) {\n\t\t\t\t\t// 向下拖动\n\t\t\t\t\ttargetIndex = currentIndex + 1;\n\t\t\t\t\tconsole.log('向下拖动，目标索引:', targetIndex);\n\t\t\t\t\t\n\t\t\t\t\t// 检查目标卡片是否在可视区域内，如果不在则滚动到可见\n\t\t\t\t\tcheckTargetVisibility(targetIndex);\n\t\t\t\t} else {\n\t\t\t\t\t// 如果移动比例不足，可以考虑使用CSS transform实现跟随手指的视觉效果\n\t\t\t\t\t// 此处不做位置交换，但可以在WishCard组件中添加视觉反馈\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 执行交换操作\n\t\t\t\tconsole.log('交换位置:', currentIndex, '->', targetIndex);\n\t\t\t\t\n\t\t\t\t// 执行交换\n\t\t\t\tswapItems(currentIndex, targetIndex);\n\t\t\t\t\n\t\t\t\t// 更新当前拖拽索引\n\t\t\t\tdragCurrentIndex.value = targetIndex;\n\t\t\t\t\n\t\t\t\t// 更新开始拖拽位置，但保留部分位移差距，使移动更连贯\n\t\t\t\t// 保留30%的位移差距，避免完全重置造成的不连贯感\n\t\t\t\tdragStartY.value = data.clientY - (moveDistance * 0.3);\n\t\t\t\t\n\t\t\t\t// 触发振动反馈\n\t\t\t\ttry {\n\t\t\t\t\tuni.vibrateShort({\n\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\tconsole.log('交换位置振动触发');\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('震动API调用失败:', e);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 检查目标卡片是否在可视区域内，如果不在则自动滚动到可见\n\t\t\tconst checkTargetVisibility = (targetIndex) => {\n\t\t\t\tif (!wishCards.value || wishCards.value.length <= targetIndex) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 创建查询选择器\n\t\t\t\t\tconst query = uni.createSelectorQuery();\n\t\t\t\t\t\n\t\t\t\t\t// 先获取列表的滚动位置和边界\n\t\t\t\t\tquery.select('.wish-list').boundingRect(listRect => {\n\t\t\t\t\t\tif (!listRect) return;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 获取所有卡片位置信息\n\t\t\t\t\t\tquery.selectAll('.wish-card-container').boundingRect(cardsRect => {\n\t\t\t\t\t\t\tif (!cardsRect || !cardsRect[targetIndex]) return;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取目标卡片信息\n\t\t\t\t\t\t\tconst cardRect = cardsRect[targetIndex];\n\t\t\t\t\t\t\tconst listTop = listRect.top;\n\t\t\t\t\t\t\tconst listBottom = listRect.bottom;\n\t\t\t\t\t\t\tconst cardTop = cardRect.top;\n\t\t\t\t\t\t\tconst cardBottom = cardRect.bottom;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tconsole.log('卡片可见性检查:', {\n\t\t\t\t\t\t\t\ttargetIndex,\n\t\t\t\t\t\t\t\tlistTop,\n\t\t\t\t\t\t\t\tlistBottom,\n\t\t\t\t\t\t\t\tcardTop,\n\t\t\t\t\t\t\t\tcardBottom\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取当前滚动位置\n\t\t\t\t\t\t\tquery.select('.wish-list').scrollOffset(scrollData => {\n\t\t\t\t\t\t\t\tif (!scrollData) return;\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tconst currentScrollTop = scrollData.scrollTop;\n\t\t\t\t\t\t\t\tlet newScrollTop = currentScrollTop;\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 如果卡片顶部在可视区域上方且不在页面顶部，向上滚动\n\t\t\t\t\t\t\t\tif (cardTop < listTop + 100 && currentScrollTop > 0) {\n\t\t\t\t\t\t\t\t\t// 向上滚动一个卡片高度的距离\n\t\t\t\t\t\t\t\t\tnewScrollTop = Math.max(0, currentScrollTop - (cardRect.height + 20));\n\t\t\t\t\t\t\t\t\tconsole.log('向上滚动到:', newScrollTop);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// 如果卡片底部在可视区域下方，向下滚动\n\t\t\t\t\t\t\t\telse if (cardBottom > listBottom - 100) {\n\t\t\t\t\t\t\t\t\t// 向下滚动一个卡片高度的距离\n\t\t\t\t\t\t\t\t\tnewScrollTop = currentScrollTop + (cardRect.height + 20);\n\t\t\t\t\t\t\t\t\tconsole.log('向下滚动到:', newScrollTop);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t// 卡片在可视区域内，不需要滚动\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 使用uni.pageScrollTo滚动到指定位置\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\t\t\t\tscrollTop: newScrollTop,\n\t\t\t\t\t\t\t\t\t\tduration: 300\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tconsole.log('已滚动到位置:', newScrollTop);\n\t\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t\tconsole.warn('滚动失败:', e);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}).exec();\n\t\t\t\t\t\t}).exec();\n\t\t\t\t\t}).exec();\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('检查卡片可见性失败:', e);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 滚动列表到指定位置的函数（小程序兼容版本）\n\t\t\tconst scrollListTo = (targetScrollTop) => {\n\t\t\t\ttry {\n\t\t\t\t\t// 在微信小程序环境中使用选择器获取ScrollView组件的上下文\n\t\t\t\t\tconst scrollViewId = 'wishListScrollView'; // 确保在模板中给scroll-view添加这个id\n\n\t\t\t\t\t// 使用uni.pageScrollTo滚动\n\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\tscrollTop: targetScrollTop,\n\t\t\t\t\t\tduration: 300\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log('设置scrollTop值为:', targetScrollTop);\n\t\t\t\t\t\n\t\t\t\t\t// 尝试使用小程序原生方法\n\t\t\t\t\tif (uni.createSelectorQuery) {\n\t\t\t\t\t\tuni.createSelectorQuery()\n\t\t\t\t\t\t\t.select(`#${scrollViewId}`)\n\t\t\t\t\t\t\t.context((res) => {\n\t\t\t\t\t\t\t\t// 如果成功获取了上下文\n\t\t\t\t\t\t\t\tif (res && res.context && typeof res.context.scrollTo === 'function') {\n\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\tres.context.scrollTo({\n\t\t\t\t\t\t\t\t\t\t\ttop: targetScrollTop,\n\t\t\t\t\t\t\t\t\t\t\tduration: 300\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\tconsole.log('成功使用context.scrollTo方法滚动');\n\t\t\t\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t\t\tconsole.error('scrollTo方法调用失败:', e);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.exec();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\treturn false;\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('滚动列表失败:', e);\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 处理拖拽结束\n\t\t\tconst handleDragEnd = () => {\n\t\t\t\tconsole.log('结束拖拽');\n\t\t\t\t\n\t\t\t\tif (isDragging.value) {\n\t\t\t\t\t// 结束拖拽状态\n\t\t\t\t\tisDragging.value = false;\n\t\t\t\t\t\n\t\t\t\t\t// 允许所有卡片添加过渡效果，使恢复更平滑\n\t\t\t\t\t// 可以通过CSS类来实现\n\t\t\t\t\t\n\t\t\t\t\t// 恢复页面滚动\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t// 发送滚动恢复通知\n\t\t\t\t\t\tuni.$emit('page-scroll-enabled', { timestamp: Date.now() });\n\t\t\t\t\t}, 50);\n\t\t\t\t\t\n\t\t\t\t\t// 保存新的排序\n\t\t\t\t\tconst newOrder = wishList.value.map(wish => wish.id);\n\t\t\t\t\t\n\t\t\t\t\t// 检查顺序是否发生变化\n\t\t\t\t\tconst hasOrderChanged = initialOrder.value.some((id, index) => id !== newOrder[index]);\n\t\t\t\t\t\n\t\t\t\t\tif (hasOrderChanged) {\n\t\t\t\t\t\t// 更新心愿排序\n\t\t\t\t\t\twishStore.updateWishOrder(newOrder);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 静默振动反馈表示排序完成\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tuni.vibrateShort();\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error('振动失败:', e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 通知手势管理器拖拽结束\n\t\t\t\tgestureManager.endDrag({ hasOrderChanged });\n\t\t\t\t\n\t\t\t\t// 延迟恢复下拉刷新，确保拖拽完全结束\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tisPullRefreshDisabled.value = false;\n\t\t\t\t}, 300);\n\t\t\t\t\t\n\t\t\t\t\t// 重置拖拽相关状态\n\t\t\t\t\tdragStartIndex.value = -1;\n\t\t\t\t\tdragCurrentIndex.value = -1;\n\t\t\t\t\tinitialOrder.value = [];\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\n\t\t\t\n\t\t\t// 交换两个项目的位置\n\t\t\tconst swapItems = (fromIndex, toIndex) => {\n\t\t\t\tif (fromIndex === toIndex) return;\n\t\t\t\t\n\t\t\t\tconsole.log('执行交换:', fromIndex, '->', toIndex);\n\t\t\t\t\n\t\t\t\t// 创建新数组用于重排序\n\t\t\t\tconst newWishList = [...wishList.value];\n\t\t\t\t\n\t\t\t\t// 从数组中取出要移动的元素\n\t\t\t\tconst itemToMove = newWishList[fromIndex];\n\t\t\t\t\n\t\t\t\t// 从数组中删除该元素\n\t\t\t\tnewWishList.splice(fromIndex, 1);\n\t\t\t\t\n\t\t\t\t// 在目标位置插入该元素\n\t\t\t\tnewWishList.splice(toIndex, 0, itemToMove);\n\t\t\t\t\n\t\t\t\t// 更新wishList\n\t\t\t\twishStore.setCurrentList(newWishList);\n\t\t\t\t\n\t\t\t\t// 静默振动反馈 - 轻微振动表示位置交换\n\t\t\t\ttry {\n\t\t\t\t\tuni.vibrateShort({\n\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\tconsole.log('交换位置振动触发');\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('震动API调用失败:', e);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 添加或提取之前缺少的方法\n\t\t\tconst closeAllSwipeActions = () => {\n\t\t\t\t// 关闭所有打开的左滑卡片\n\t\t\t\tif (wishCards.value && wishCards.value.length) {\n\t\t\t\t\twishCards.value.forEach(card => {\n\t\t\t\t\t\tif (card && card.resetSwipe) {\n\t\t\t\t\t\t\tcard.resetSwipe();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t// 重置活动卡片ID\n\t\t\t\tactiveCardId.value = null;\n\t\t\t}\n\t\t\t\n\t\t\t// 前往添加心愿页面\n\t\t\tconst goToAddWish = () => {\n\t\t\t\t// 获取当前选中的分组ID\n\t\t\t\tconst currentGroupId = wishStore.currentGroupId;\n\t\t\t\t\n\t\t\t\t// 如果当前选中的是\"全部\"分组，则不传递分组ID\n\t\t\t\tconst url = currentGroupId !== 'all' ?\n\t\t\t\t\t`/subpkg-wish/pages/editWish/editWish?groupId=${currentGroupId}` :\n\t\t\t\t\t'/subpkg-wish/pages/editWish/editWish';\n\t\t\t\t\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: url\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 处理分组变化\n\t\t\tconst onGroupChange = (groupId) => {\n\t\t\t\tconsole.log('[onGroupChange] 开始处理分组变化:', groupId);\n\t\t\t\ttry {\n\t\t\t\t\twishStore.setCurrentGroup(groupId);\n\t\t\t\t\tconsole.log('[onGroupChange] 分组设置完成');\n\n\t\t\t\t\t// 关闭所有卡片\n\t\t\t\t\tcloseAllSwipeActions();\n\t\t\t\t\tconsole.log('[onGroupChange] 卡片关闭完成');\n\n\t\t\t\t\t// 分组变化后滚动到顶部\n\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\tscrollTop: 0,\n\t\t\t\t\t\tduration: 300\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log('[onGroupChange] 滚动到顶部完成');\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('[onGroupChange] 处理分组变化时发生错误:', e);\n\t\t\t\t\tconsole.error('[onGroupChange] 错误堆栈:', e.stack);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 处理卡片打开\n\t\t\tconst handleCardOpen = (cardId) => {\n\t\t\t\tactiveCardId.value = cardId;\n\t\t\t}\n\t\t\t\n\t\t\t// 处理卡片关闭\n\t\t\tconst handleCardClose = () => {\n\t\t\t\tactiveCardId.value = null;\n\t\t\t}\n\t\t\t\n\t\t\t// 处理卡片开始滑动\n\t\t\tconst handleCardSwipeStart = (cardId) => {\n\t\t\t\t// 如果有其他卡片已打开，先关闭它\n\t\t\t\tif (activeCardId.value && activeCardId.value !== cardId) {\n\t\t\t\t\tcloseAllSwipeActions();\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 处理卡片滚动检测\n\t\t\tconst handleCardScrollDetected = (data) => {\n\t\t\t\t// 当检测到卡片滚动时的处理\n\t\t\t\tconsole.log('检测到卡片滚动', data);\n\t\t\t\t// 大幅提高阈值到50px，避免轻微滚动就关闭左滑状态\n\t\t\t\t// 让用户可以在左滑状态下进行一定的垂直操作\n\t\t\t\tif (data && data.deltaY > 50) {\n\t\t\t\t\tcloseAllSwipeActions();\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 处理页面触摸\n\t\t\tconst handlePageTouch = (e) => {\n\t\t\t\t\n\t\t\t\t// 如果处于拖拽状态，完全阻止页面滚动\n\t\t\t\tif (isDragging.value) {\n\t\t\t\t\t// 使用更强力的方式阻止事件传播和默认行为\n\t\t\t\t\te.preventDefault && e.preventDefault();\n\t\t\t\t\te.stopPropagation && e.stopPropagation();\n\t\t\t\t\t\n\t\t\t\t\t// 阻止事件传播\n\t\t\t\t\tif (e.cancelable) {\n\t\t\t\t\t\te.cancelable && e.preventDefault();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 立即返回，不处理任何触摸逻辑\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 页面触摸时的处理逻辑\n\t\t\t\tconst currentY = e.touches[0].clientY;\n\t\t\t\t\n\t\t\t\t// 如果有上一次触摸位置记录，计算移动距离\n\t\t\t\tif (lastTouchY.value !== undefined) {\n\t\t\t\t\tconst deltaY = Math.abs(currentY - lastTouchY.value);\n\t\t\t\t\t\n\t\t\t\t\t// 大幅提高阈值到60px，只有非常明显的滑动才关闭左滑卡片\n\t\t\t\t\t// 避免用户在左滑卡片上的正常操作被误判为页面滑动\n\t\t\t\t\tif (activeCardId.value !== null && deltaY > 60) {\n\t\t\t\t\t\tcloseAllSwipeActions();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tlastTouchY.value = currentY;\n\t\t\t}\n\t\t\t\n\t\t\t// 处理页面触摸结束\n\t\t\tconst handlePageTouchEnd = (e) => {\n\t\t\t\t// 页面触摸结束时的处理逻辑\n\t\t\t\tlastTouchY.value = undefined;\n\t\t\t}\n\t\t\t\n\t\t\t// 页面滚动处理 - 简化版本\n\t\t\tconst onPageScroll = (e) => {\n\t\t\t\t// 页面滚动时关闭打开的卡片\n\t\t\t\tif (activeCardId.value && e && e.scrollTop > 10) {\n\t\t\t\t\tcloseAllSwipeActions();\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 卡片列表触摸处理\n\t\t\tconst onCardListTouch = (e) => {\n\n\t\t\t\t// 卡片列表触摸时处理逻辑\n\t\t\t\t// 如果有卡片处于左滑状态，且不是在卡片内部触摸（触摸了空白区域），关闭左滑状态\n\t\t\t\tif (activeCardId.value !== null) {\n\t\t\t\t\tconst currentTouch = e.touches[0];\n\n\t\t\t\t\t// 尝试判断触摸是否在卡片区域外\n\t\t\t\t\t// 由于无法直接确定触摸点是否在卡片区域内，我们使用移动距离判断\n\t\t\t\t\tconst currentY = currentTouch.clientY;\n\n\t\t\t\t\tif (lastTouchY.value !== undefined) {\n\t\t\t\t\t\tconst deltaY = Math.abs(currentY - lastTouchY.value);\n\n\t\t\t\t\t\t// 大幅提高阈值到50px，减少列表滚动时的误触发\n\t\t\t\t\t\t// 让用户可以在卡片周围进行一定的触摸操作而不会立即关闭左滑状态\n\t\t\t\t\t\tif (deltaY > 50) {\n\t\t\t\t\t\t\tcloseAllSwipeActions();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tlastTouchY.value = currentY;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 容器触摸开始 - 阻止水平滑动\n\t\t\tconst containerTouchStartX = ref(0);\n\t\t\tconst containerTouchStartY = ref(0);\n\n\t\t\tconst handleContainerTouchStart = (e) => {\n\t\t\t\tif (e.touches && e.touches.length > 0) {\n\t\t\t\t\tcontainerTouchStartX.value = e.touches[0].clientX;\n\t\t\t\t\tcontainerTouchStartY.value = e.touches[0].clientY;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果下拉刷新被禁用，阻止所有可能的触摸行为\n\t\t\t\tif (isPullRefreshDisabled.value || isDragging.value) {\n\t\t\t\t\te.preventDefault && e.preventDefault();\n\t\t\t\t\te.stopPropagation && e.stopPropagation();\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 容器触摸移动 - 阻止水平滑动和下拉刷新冲突\n\t\t\tconst handleContainerTouchMove = (e) => {\n\t\t\t\t// 优先检查：如果下拉刷新被禁用或正在拖拽，直接阻止\n\t\t\t\tif (isPullRefreshDisabled.value || isDragging.value) {\n\t\t\t\t\te.preventDefault && e.preventDefault();\n\t\t\t\t\te.stopPropagation && e.stopPropagation();\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (e.touches && e.touches.length > 0) {\n\t\t\t\t\tconst currentX = e.touches[0].clientX;\n\t\t\t\t\tconst currentY = e.touches[0].clientY;\n\n\t\t\t\t\tconst deltaX = Math.abs(currentX - containerTouchStartX.value);\n\t\t\t\t\tconst deltaY = Math.abs(currentY - containerTouchStartY.value);\n\n\t\t\t\t\t// 如果水平移动大于垂直移动，阻止默认行为（阻止水平滑动）\n\t\t\t\t\tif (deltaX > deltaY && deltaX > 10) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\te.stopPropagation();\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 容器触摸结束 - 重置状态\n\t\t\tconst handleContainerTouchEnd = (e) => {\n\t\t\t\tcontainerTouchStartX.value = 0;\n\t\t\t\tcontainerTouchStartY.value = 0;\n\t\t\t}\n\t\t\t\n\t\t\t// 完成心愿\n\t\t\tconst completeWish = async (id) => {\n\t\t\t\t// 检查用户是否已登录\n\t\t\t\tif (!userStore.checkLoginAndRedirect()) {\n\t\t\t\t\tconsole.log('用户未登录，跳转到登录页面')\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tawait wishStore.completeWish(id);\n\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '心愿已完成',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('完成心愿失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '操作失败',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 删除心愿\n\t\t\tconst deleteWish = async (id) => {\n\t\t\t\t// 检查用户是否已登录\n\t\t\t\tif (!userStore.checkLoginAndRedirect()) {\n\t\t\t\t\tconsole.log('用户未登录，跳转到登录页面')\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 先关闭所有左滑菜单\n\t\t\t\t\tcloseAllSwipeActions();\n\t\t\t\t\t\n\t\t\t\t\t// 记录当前分组ID，避免删除后分组选择器不必要的刷新\n\t\t\t\t\tconst currentGroupBeforeDelete = wishStore.currentGroupId;\n\t\t\t\t\t\n\t\t\t\t\t// 执行删除操作\n\t\t\t\t\tawait wishStore.deleteWish(id);\n\t\t\t\t\t\n\t\t\t\t\t// 强制刷新当前页面数据，确保界面立即更新\n\t\t\t\t\tawait nextTick();\n\t\t\t\t\t\n\t\t\t\t\t// 手机端特殊处理：强制触发组件重新渲染\n\t\t\t\t\tuni.$emit('wish-list-updated', {\n\t\t\t\t\t\ttimestamp: Date.now(),\n\t\t\t\t\t\taction: 'delete',\n\t\t\t\t\t\tdeletedId: id\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 确保分组选择器状态保持一致，避免不必要的重新渲染\n\t\t\t\t\tif (wishStore.currentGroupId !== currentGroupBeforeDelete) {\n\t\t\t\t\t\t// 只有当分组真的改变时才更新\n\t\t\t\t\t\tconsole.log('分组已变更，触发必要的更新');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 显示成功提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '心愿已删除',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('心愿删除成功，界面已更新');\n\t\t\t\t} catch (error) {\n\t\t\t\t\t// 删除失败时的处理已在store中完成\n\t\t\t\t\tconsole.error('删除心愿失败:', error);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 分享心愿\n\t\t\tconst shareWish = (id) => {\n\t\t\t\t// 检查用户是否已登录\n\t\t\t\tif (!userStore.checkLoginAndRedirect()) {\n\t\t\t\t\tconsole.log('用户未登录，跳转到登录页面')\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 显示加载提示\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '生成海报中...',\n\t\t\t\t\tmask: true\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\t// 模拟生成海报或分享操作的延迟\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t// 隐藏加载提示\n\t\t\t\t\tuni.hideLoading().catch(() => {})\n\n\t\t\t\t\t// 显示分享成功提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '分享成功',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\n\t\t\t\t\t// 触发振动反馈\n\t\t\t\t\tuni.vibrateShort()\n\t\t\t\t}, 1500)\n\t\t\t}\n\t\t\t\n\t\t\t// 检查登录状态\n\t\t\tconst checkLoginStatus = async () => {\n\t\t\t\tconst isLoggedIn = userStore.isLoggedIn;\n\t\t\t\t\n\t\t\t\t// 不再自动跳转到登录页面，无论登录状态如何都加载数据\n\t\t\t\t// 已登录或跳过登录检查，按顺序初始化数据\n\t\t\t\ttry {\n\t\t\t\t\t// 1. 先初始化分组数据\n\t\t\t\t\tawait groupStore.initGroups();\n\t\t\t\t\t\n\t\t\t\t\t// 2. 再初始化心愿数据\n\t\t\t\t\tawait wishStore.initWishList();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('[checkLoginStatus] Data initialization error:', error);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 初始化完成\n\t\t\t\tconsole.log('[checkLoginStatus] Data initialization completed');\n\t\t\t}\n\t\t\t\n\t\t\t// 强制禁用页面滚动的函数\n\t\t\tconst forceDisableScroll = () => {\n\t\t\t\tconsole.log('尝试禁用页面滚动');\n\n\t\t\t\t// 使用各种兼容方法禁止滚动\n\t\t\t\ttry {\n\t\t\t\t\t// 针对不同环境采取不同措施\n\t\t\t\t\tif (typeof uni.disablePageScroll === 'function') {\n\t\t\t\t\t\t// 如果uni-app提供了原生的禁用滚动方法，使用它\n\t\t\t\t\t\tuni.disablePageScroll();\n\t\t\t\t\t} else if (typeof uni.pageScrollTo === 'function') {\n\t\t\t\t\t\t// 尝试使用pageScrollTo固定当前位置\n\t\t\t\t\t\t// 获取当前滚动位置并固定\n\t\t\t\t\t\tuni.createSelectorQuery().selectViewport().scrollOffset((res) => {\n\t\t\t\t\t\t\tif (res) {\n\t\t\t\t\t\t\t\tuni.pageScrollTo({\n\t\t\t\t\t\t\t\t\tscrollTop: res.scrollTop,\n\t\t\t\t\t\t\t\t\tduration: 0\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}).exec();\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('强制禁用滚动失败:', e);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 检查未读消息\n\t\t\tconst checkUnreadMessages = () => {\n\t\t\t\t// 确保消息store已初始化\n\t\t\t\tmessageStore.initMessages();\n\t\t\t\t\n\t\t\t\t// 获取未读消息数量\n\t\t\t\tconst unreadCount = messageStore.getUnreadCount;\n\t\t\t\t\n\t\t\t\t// 更新tabbar徽标\n\t\t\t\tif (unreadCount > 0) {\n\t\t\t\t\tuni.setTabBarBadge({\n\t\t\t\t\t\tindex: 1, // 消息选项卡索引\n\t\t\t\t\t\ttext: unreadCount.toString()\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 尝试移除徽标\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.removeTabBarBadge({\n\t\t\t\t\t\t\tindex: 1\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.log('移除徽标失败，可能没有显示徽标');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\n\t\t\t\n\n\t\t\t\n\n\t\t\t\n\t\t\t// 手动同步数据（下拉刷新时调用）\n\t\t\tconst forceSyncData = async () => {\n\t\t\t\tconsole.log('[Index] Manual sync data triggered');\n\n\t\t\t\tif (isSyncing.value) {\n\t\t\t\t\tconsole.log('[Index] Sync already in progress');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tisSyncing.value = true;\n\n\t\t\t\t\t// 使用新的手动同步方法，静默模式（不显示额外弹窗）\n\t\t\t\t\tawait userStore.manualSyncAllData(true);\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('[Index] Manual sync failed:', error);\n\t\t\t\t} finally {\n\t\t\t\t\tisSyncing.value = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\treturn {\n\t\t\t\twishStore,\n\t\t\t\tgroupStore,\n\t\t\t\tuserStore,\n\t\t\t\tmessageStore,\n\t\t\t\twishCards,\n\t\t\t\tcontainerRef,\n\t\t\t\tactiveCardId,\n\t\t\t\tlastTouchY,\n\t\t\t\tisPullRefreshDisabled,\n\t\t\t\tcloseAllSwipeActions,\n\t\t\t\thandleCardOpen,\n\t\t\t\thandleCardClose,\n\t\t\t\thandleCardSwipeStart,\n\t\t\t\thandleCardScrollDetected,\n\t\t\t\thandlePageTouch,\n\t\t\t\thandlePageTouchEnd,\n\t\t\t\tonPageScroll,\n\t\t\t\tonPageHide,\n\t\t\t\thandleContainerTouchStart,\n\t\t\t\thandleContainerTouchMove,\n\t\t\t\thandleContainerTouchEnd,\n\t\t\t\t// 添加拖拽相关方法\n\t\t\t\tisDragging,\n\t\t\t\tdragStartIndex,\n\t\t\t\tdragCurrentIndex,\n\t\t\t\tdragStartY,\n\t\t\t\tdragThrottled,\n\t\t\t\tcardHeight,\n\t\t\t\thandleDragStart,\n\t\t\t\thandleDragMove,\n\t\t\t\thandleDragEnd,\n\t\t\t\tswapItems,\n\t\t\t\t// 其他方法\n\t\t\t\tonGroupChange,\n\t\t\t\tcompleteWish,\n\t\t\t\tdeleteWish,\n\t\t\t\tshareWish,\n\t\t\t\tgoToAddWish,\n\t\t\t\twishList,\n\t\t\t\tisLogin,\n\t\t\t\tcheckLoginStatus,\n\t\t\t\tcheckUnreadMessages,\n\t\t\t\tforceSyncData,\n\t\t\t\tisSyncing,\n\t\t\t\t// 强制更新key\n\t\t\t\tforceUpdateKey\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisSyncing: false\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\twishList() {\n\t\t\t\treturn this.wishStore.currentGroupWishes\n\t\t\t},\n\t\t\tisLogin() {\n\t\t\t\treturn this.userStore.isLoggedIn\n\t\t\t}\n\t\t},\n\n\t\t// 页面加载时执行\n\t\tasync onLoad(options) {\n\t\t\tconsole.log('[Index onLoad] 页面加载，初始化数据...');\n\n\t\t\t// 发送事件到 setup() 函数处理\n\t\t\tuni.$emit('page-load', {\n\t\t\t\toptions: options,\n\t\t\t\ttimestamp: Date.now()\n\t\t\t});\n\t\t},\n\n\t\t// 页面每次显示时执行\n\t\tonShow() {\n\t\t\tconsole.log('[Index onShow] 页面显示，检查数据状态...');\n\n\t\t\t// 发送事件到 setup() 函数处理\n\t\t\tuni.$emit('page-show-lifecycle', {\n\t\t\t\ttimestamp: Date.now()\n\t\t\t});\n\t\t},\n\t\t// 页面隐藏时执行\n\t\tonHide() {\n\t\t\tconsole.log('[Index onHide] 页面隐藏');\n\t\t\t// 发送事件到 setup() 函数处理\n\t\t\tuni.$emit('page-hide-lifecycle', {\n\t\t\t\ttimestamp: Date.now()\n\t\t\t});\n\t\t},\n\t\t// 监听数据变化\n\t\twatch: {\n\t\t\twishList: {\n\t\t\t\thandler() {\n\t\t\t\t\t// 心愿列表变化时的处理\n\t\t\t\t\tconsole.log('心愿列表已更新')\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 清理所有心愿中的\"已还原\"标记按钮已移除\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* 全局禁用水平滚动 - 最强规则 */\n\tpage {\n\t\toverflow-x: hidden !important;\n\t\toverflow-y: auto !important;\n\t\twidth: 100vw !important;\n\t\tmax-width: 100vw !important;\n\t\tmin-width: 100vw !important;\n\t\tbox-sizing: border-box !important;\n\t\tposition: relative !important;\n\t}\n\n\t/* 确保body也不会水平滚动 */\n\tbody {\n\t\toverflow-x: hidden !important;\n\t\toverflow-y: auto !important;\n\t\twidth: 100vw !important;\n\t\tmax-width: 100vw !important;\n\t\tmin-width: 100vw !important;\n\t\tbox-sizing: border-box !important;\n\t\tposition: relative !important;\n\t}\n\n\t/* 确保主要元素都不会导致水平滚动 */\n\tview, text, image, button, input, textarea {\n\t\tbox-sizing: border-box;\n\t}\n\n\t/* 主容器样式 - 禁用水平滚动 */\n\t.wish-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t\tposition: relative;\n\t\tpadding-bottom: 120rpx; /* 调整底部填充，为tabbar预留空间 */\n\t\tbox-sizing: border-box;\n\t\toverflow-x: hidden !important; /* 强制禁止水平滚动 */\n\t\toverflow-y: auto !important; /* 允许垂直滚动 */\n\t\twidth: 100vw !important; /* 确保容器宽度等于视口 */\n\t\tmax-width: 100vw !important; /* 防止内容溢出 */\n\t\tmin-width: 100vw !important; /* 确保最小宽度 */\n\t\tleft: 0 !important; /* 确保容器从左边开始 */\n\t\tright: 0 !important; /* 确保容器到右边结束 */\n\t\ttouch-action: pan-y; /* 只允许垂直滑动 */\n\t\t-webkit-user-select: none;\n\t\tuser-select: none;\n\t\t\n\t\t/* 全局下拉刷新禁用样式 */\n\t\t&.pull-refresh-disabled {\n\t\t\t/* 完全禁用下拉刷新的各种可能触发方式 */\n\t\t\toverflow-y: hidden !important;\n\t\t\t-webkit-overflow-scrolling: touch !important;\n\t\t\toverscroll-behavior: contain !important;\n\t\t\toverscroll-behavior-y: contain !important;\n\t\t\t\n\t\t\t/* 禁用所有可能的滚动反弹效果 */\n\t\t\t-webkit-overflow-scrolling: auto !important;\n\t\t\t\n\t\t\t/* 在某些平台上阻止下拉刷新的额外CSS */\n\t\t\tposition: relative !important;\n\t\t\t\n\t\t\t&::before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: -100px;\n\t\t\t\tleft: 0;\n\t\t\t\tright: 0;\n\t\t\t\theight: 100px;\n\t\t\t\tbackground: transparent;\n\t\t\t\tpointer-events: none;\n\t\t\t\tz-index: 9999;\n\t\t\t}\n\t\t}\n\t\t\n\t\t/* 手势禁用状态的强化保护 */\n\t\t&.gesture-disabled {\n\t\t\t/* 最强的下拉刷新禁用策略 */\n\t\t\tposition: fixed !important;\n\t\t\ttop: 0 !important;\n\t\t\tleft: 0 !important;\n\t\t\tright: 0 !important;\n\t\t\tbottom: 0 !important;\n\t\t\toverflow: hidden !important;\n\t\t\ttouch-action: none !important;\n\t\t\t-webkit-overflow-scrolling: auto !important;\n\t\t\toverscroll-behavior: none !important;\n\t\t\toverscroll-behavior-y: none !important;\n\t\t\t-webkit-touch-callout: none !important;\n\t\t\t-webkit-user-select: none !important;\n\t\t\tuser-select: none !important;\n\t\t\t\n\t\t\t/* 阻止所有可能的下拉触发 */\n\t\t\t&::before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: -200px;\n\t\t\t\tleft: 0;\n\t\t\t\tright: 0;\n\t\t\t\theight: 200px;\n\t\t\t\tbackground: transparent;\n\t\t\t\tpointer-events: none !important;\n\t\t\t\tz-index: 99999;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.drag-mode {\n\t\t\t/* 拖拽模式样式 */\n\t\t\t/* 完全禁止滚动 */\n\t\t\toverflow: hidden !important;\n\t\t\ttouch-action: none !important; /* 禁止所有触摸操作 */\n\t\t\toverscroll-behavior: none !important; /* 禁止所有过度滚动行为 */\n\t\t\t\n\t\t\t/* 额外的下拉刷新禁用 */\n\t\t\t-webkit-overflow-scrolling: auto !important; /* 禁用iOS弹性滚动 */\n\t\t\toverscroll-behavior-y: contain !important; /* 防止Y轴过度滚动 */\n\t\t\t\n\t\t\t/* 防止任何可能触发下拉刷新的行为 */\n\t\t\t&::before, &::after {\n\t\t\t\tpointer-events: none !important;\n\t\t\t}\n\n\t\t\t.add-wish-btn {\n\t\t\t\topacity: 0.5;\n\t\t\t}\n\n\t\t\t.wish-list {\n\t\t\t\tscroll-behavior: smooth;\n\t\t\t\ttransition: scroll-behavior 0.3s;\n\t\t\t\tpointer-events: none; /* 在拖拽时禁用滚动容器的指针事件 */\n\t\t\t\t\n\t\t\t\t/* 强化禁用下拉刷新 */\n\t\t\t\toverflow-y: hidden !important;\n\t\t\t\ttouch-action: none !important;\n\t\t\t\toverscroll-behavior-y: none !important;\n\n\t\t\t\t/* 但允许卡片本身的指针事件 */\n\t\t\t\t.wish-card-container {\n\t\t\t\t\tpointer-events: auto;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.wish-card-container {\n\t\t\t\t/* 性能优化：避免使用 transition: all，只对需要的属性设置过渡 */\n\t\t\t\ttransition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),\n\t\t\t\t           opacity 0.3s ease-out;\n\n\t\t\t\t/* 启用硬件加速 */\n\t\t\t\ttransform: translateZ(0);\n\t\t\t\twill-change: auto;\n\t\t\t\tbackface-visibility: hidden;\n\n\t\t\t\t&:not(.is-dragging) {\n\t\t\t\t\topacity: 0.8;\n\t\t\t\t\ttransform: scale(0.98) translateZ(0);\n\t\t\t\t}\n\n\t\t\t\t&.is-dragging {\n\t\t\t\t\ttransform: scale(1.05) translateZ(0);\n\t\t\t\t\topacity: 1;\n\t\t\t\t\tz-index: 100;\n\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\twill-change: transform, opacity;\n\n\t\t\t\t\t/* 阴影单独处理，避免影响主动画 */\n\t\t\t\t\tbox-shadow: 0 10rpx 30rpx rgba(138, 43, 226, 0.5);\n\t\t\t\t\ttransition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),\n\t\t\t\t\t           opacity 0.2s ease-out,\n\t\t\t\t\t           box-shadow 0.3s ease-out;\n\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\tposition: relative;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/* 隐藏滚动条但保留滚动功能 */\n\t::-webkit-scrollbar {\n\t\tdisplay: none;\n\t\twidth: 0 !important;\n\t\theight: 0 !important;\n\t\t-webkit-appearance: none;\n\t\tbackground: transparent;\n\t}\n\t\n\t/* 心愿列表容器 */\n\t.wish-list {\n\t\tpadding: 0; /* 移除内边距，让卡片控制自己的边距 */\n\t\tposition: relative;\n\t\tpadding-bottom: 150rpx; /* 增加底部内边距，确保最后一个卡片不被tabbar遮挡 */\n\t\toverflow-x: hidden; /* 禁止水平滚动 */\n\t\twidth: 100%; /* 确保宽度不超过父容器 */\n\t\tmax-width: 100%; /* 防止内容溢出 */\n\t}\n\t\n\t/* 卡片列表内容容器 */\n\t.wish-list-content {\n\t\twidth: 100%;\n\t\tmax-width: 100%; /* 防止内容溢出 */\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\toverflow-x: hidden; /* 禁止水平滚动 */\n\t\t/* 移除居中对齐，让卡片根据自己的边距自然排列 */\n\t}\n\t\n\t.empty-list {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding-top: 100rpx;\n\t\tpadding-bottom: 100rpx;\n\t\t\n\t\t.empty-text {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #999;\n\t\t\ttext-align: center;\n\t\t}\n\t}\n\t\n\t.add-wish-btn {\n\t\tposition: fixed;\n\t\tright: 40rpx;\n\t\tbottom: 160rpx; /* 调整底部位置，确保不被tabbar遮挡 */\n\t\twidth: 100rpx;\n\t\theight: 100rpx;\n\t\tborder-radius: 50%;\n\t\tbackground-color: #8a2be2;\n\t\tbox-shadow: 0 6rpx 16rpx rgba(138, 43, 226, 0.4);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 99;\n\t\t\n\t\t.plus-icon {\n\t\t\twidth: 50rpx;\n\t\t\theight: 50rpx;\n\t\t\tposition: relative;\n\t\t\t\n\t\t\t.h-line, .v-line {\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tposition: absolute;\n\t\t\t\tborder-radius: 4rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.h-line {\n\t\t\t\twidth: 50rpx;\n\t\t\t\theight: 6rpx;\n\t\t\t\ttop: 22rpx; /* 居中位置 (50-6)/2 */\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t\t\n\t\t\t.v-line {\n\t\t\t\theight: 50rpx;\n\t\t\t\twidth: 6rpx;\n\t\t\t\tleft: 22rpx; /* 居中位置 (50-6)/2 */\n\t\t\t\ttop: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* Vue transition-group 动画样式 */\n\t.list-transition-group {\n\t\tposition: relative;\n\t}\n\n\t/* 优化后的删除动画 - 分阶段执行 */\n\t.list-item-leave-active {\n\t\t/* 第一阶段：快速淡出和移动 */\n\t\ttransition: opacity 0.2s ease-out,\n\t\t           transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n\t\ttransform-origin: center;\n\t\twill-change: opacity, transform;\n\t}\n\n\t.list-item-leave-to {\n\t\topacity: 0;\n\t\t/* 使用 translate3d 启用硬件加速 */\n\t\ttransform: translate3d(-30px, 0, 0) scale(0.9);\n\t}\n\n\t/* 高度收缩动画 - 延迟执行 */\n\t.list-item-height-collapse {\n\t\ttransition: height 0.2s ease-out 0.2s,\n\t\t           margin 0.2s ease-out 0.2s,\n\t\t           padding 0.2s ease-out 0.2s;\n\t\theight: 0;\n\t\tmargin-bottom: 0;\n\t\tpadding-top: 0;\n\t\tpadding-bottom: 0;\n\t\toverflow: hidden;\n\t}\n\n\t/* 其他元素位置调整动画 - 优化性能 */\n\t.list-item-move {\n\t\ttransition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\t\twill-change: transform;\n\t}\n\n\t/* 优化后的进入动画 */\n\t.list-item-enter-active {\n\t\ttransition: opacity 0.3s ease-out,\n\t\t           transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\t\twill-change: opacity, transform;\n\t}\n\n\t.list-item-enter-from {\n\t\topacity: 0;\n\t\t/* 使用 translate3d 启用硬件加速 */\n\t\ttransform: translate3d(30px, 0, 0) scale(0.9);\n\t}\n\n\t/* 响应式动画优化 */\n\n\t/* 尊重用户的动画偏好设置 */\n\t@media (prefers-reduced-motion: reduce) {\n\t\t.wish-card-container,\n\t\t.list-item-leave-active,\n\t\t.list-item-enter-active,\n\t\t.list-item-move {\n\t\t\ttransition: none !important;\n\t\t\tanimation: none !important;\n\t\t}\n\n\t\t/* 保持基本的视觉反馈，但移除动画 */\n\t\t.wish-card-container.is-dragging {\n\t\t\ttransform: scale(1.02) translateZ(0);\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(138, 43, 226, 0.3);\n\t\t}\n\t}\n\n\t/* 低端设备优化 */\n\t@media (max-device-width: 768px) and (-webkit-max-device-pixel-ratio: 1) {\n\t\t.wish-card-container {\n\t\t\ttransition-duration: 0.2s; /* 缩短动画时间 */\n\t\t}\n\n\t\t.list-item-leave-active,\n\t\t.list-item-enter-active {\n\t\t\ttransition-duration: 0.25s; /* 缩短列表动画时间 */\n\t\t}\n\n\t\t/* 移除复杂的阴影效果 */\n\t\t.wish-card-container.is-dragging {\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(138, 43, 226, 0.2);\n\t\t}\n\t}\n\n\t/* 高刷新率屏幕优化 */\n\t@media (min-resolution: 120dpi) {\n\t\t.wish-card-container,\n\t\t.list-item-move {\n\t\t\ttransition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);\n\t\t}\n\t}\n\n\t/* 按钮点击反馈动画优化 */\n\t.add-wish-btn {\n\t\ttransition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1),\n\t\t           box-shadow 0.1s ease-out;\n\t\ttransform: translateZ(0);\n\t\twill-change: auto;\n\t\tbackface-visibility: hidden;\n\t}\n\n\t.add-wish-btn:active {\n\t\ttransform: translate3d(0, 2px, 0) scale(0.98);\n\t\tbox-shadow: 0 2px 8px rgba(138, 43, 226, 0.3);\n\t\twill-change: transform, box-shadow;\n\t}\n\n\t/* 支持 hover 的设备上的悬停效果 */\n\t@media (hover: hover) and (pointer: fine) {\n\t\t.add-wish-btn:hover {\n\t\t\ttransform: translate3d(0, -1px, 0);\n\t\t\tbox-shadow: 0 4px 12px rgba(138, 43, 226, 0.2);\n\t\t\ttransition: transform 0.2s ease-out, box-shadow 0.2s ease-out;\n\t\t}\n\t}\n\n\n\n\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/OneDrive/wishlist-cloud-T/wishlist-uniapp/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "devLog", "useWishStore", "useGroupStore", "useUserStore", "useMessageStore", "useCommentStore", "ref", "onMounted", "animationMonitor", "gestureManager", "getCurrentInstance", "computed", "nextTick", "onUnmounted"], "mappings": ";;;;;;;;;;;AAqEC,MAAA,WAAA,MAAA;AACA,MAAA,gBAAA,MAAA;AAEA,MAAA,YAAA;AAAA,EAAe,YAAA;AAAA,IACF;AAAA,IACX;AAAA,EACA;AAAA;AAAA,EACD,aAAA,GAAA;AAIC,QAAA,KAAA,EAAA,YAAA,IAAA;AAECA,oBAAA,MAAA,MAAA,2BAAA;AAAA,QAAqC,WAAA,EAAA;AAAA,QACvB,WAAA,KAAA,IAAA;AAAA,MACO,CAAA;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EACD,eAAA,MAAA;AAGC,QAAA;AACC,UAAA,KAAA,UAAA,UAAA,KAAA,WAAA,QAAA;AACC,aAAA,QAAA,KAAA;AAAA,MAAkB;AAEnB,UAAA,KAAA,WAAA,QAAA;AACC,aAAA,SAAA;AAAA,MAAc;AAEf,UAAA,KAAA,YAAA;AACC,aAAA,WAAA;AAAA,MAAgB;AAEjB,UAAA,KAAA,YAAA,KAAA,SAAA,OAAA;AACC,aAAA,SAAA,MAAA,YAAA;AAAA,MAAgC;AAAA,IACjC,SAAA,GAAA;AAEAC,qBAAAA,OAAA,MAAA,YAAA,CAAA;AAAA,IAA0B;AAAA,EAC3B;AAAA;AAAA,EACD,sBAAA;AAGCD,kBAAA,MAAA,MAAA,qBAAA;AAAA,MAA+B,WAAA,KAAA,IAAA;AAAA,MACV,aAAA;AAAA,IACP,CAAA;AAAA,EACb;AAAA;AAAA,EACF,gBAAA;AAICA,kBAAA,MAAA,MAAA,iCAAA;AAAA,MAA2C,WAAA,KAAA,IAAA;AAAA,IACtB,CAAA;AAAA,EACpB;AAAA;AAAA,EACF,MAAA,oBAAA;AAICA,kBAAA,MAAA,MAAA,gCAAA;AAAA,MAA0C,WAAA,KAAA,IAAA;AAAA,IACrB,CAAA;AAAA,EACpB;AAAA;AAAA,EACF,kBAAA,KAAA;AAIC,QAAA,IAAA,SAAA,YAAA,IAAA,UAAA,IAAA,OAAA,WAAA,IAAA,OAAA,QAAA,UAAA,QAAA;AAEC,YAAA,QAAA,OAAA,IAAA,OAAA,QAAA,SAAA,CAAA;AACA,YAAA,OAAA,KAAA,SAAA,KAAA,KAAA,CAAA;AAEA,aAAA;AAAA,QAAO,OAAA,KAAA,SAAA;AAAA,QACe,MAAA,mCAAA,KAAA,EAAA;AAAA,QAC2B,UAAA,MAAA,QAAA,KAAA,KAAA,KAAA,KAAA,MAAA,SAAA,IAAA,KAAA,MAAA,CAAA,IAAA,KAAA,SAAA;AAAA,MAEhB;AAAA,IACjC;AAID,QAAA,IAAA,SAAA,YAAA,IAAA,UAAA,IAAA,OAAA,YAAA,IAAA,OAAA,QAAA,WAAA,IAAA,OAAA,QAAA,cAAA,UAAA;AAEC,YAAA,UAAA,IAAA,OAAA,QAAA;AACA,YAAA,YAAA,IAAA,OAAA,QAAA,aAAA;AAEA,aAAA;AAAA,QAAO,OAAA,MAAA,SAAA;AAAA,QACgB,MAAA,8BAAA,OAAA;AAAA,QACqB,UAAA;AAAA,MACjC;AAAA,IACX;AAID,UAAA,iBAAAA,cAAAA,MAAA,eAAA,qBAAA;AACA,QAAA,kBAAA,KAAA,IAAA,IAAA,eAAA,YAAA,KAAA;AACC,YAAA,UAAA,eAAA;AACA,YAAA,YAAA,eAAA;AAGAA,0BAAA,kBAAA,qBAAA;AAEA,aAAA;AAAA,QAAO,OAAA,MAAA,SAAA;AAAA,QACgB,MAAA,8BAAA,OAAA;AAAA,QACqB,UAAA;AAAA,MACjC;AAAA,IACX;AAID,UAAA,iBAAA,KAAA,UAAA;AACA,UAAA,aAAA,KAAA;AACA,UAAA,eAAA,WAAA,aAAA,cAAA;AAGA,QAAA,gBAAA,mBAAA,OAAA;AACC,aAAA;AAAA,QAAO,OAAA,MAAA,aAAA,IAAA;AAAA,QACwB,MAAA,8BAAA,cAAA;AAAA,QACoB,UAAA;AAAA,MACxC;AAAA,IACX;AAID,WAAA;AAAA,MAAO,OAAA;AAAA,MACC,MAAA;AAAA,MACD,UAAA;AAAA,IACI;AAAA,EACX;AAAA;AAAA,EACD,kBAAA;AAIC,UAAA,iBAAAA,cAAAA,MAAA,eAAA,qBAAA;AACA,QAAA,kBAAA,KAAA,IAAA,IAAA,eAAA,YAAA,KAAA;AACC,YAAA,UAAA,eAAA;AACA,YAAA,YAAA,eAAA;AAIA,aAAA;AAAA,QAAO,OAAA,MAAA,SAAA;AAAA,QACgB,OAAA,WAAA,OAAA;AAAA,QACG,UAAA;AAAA,MACf;AAAA,IACX;AAID,UAAA,iBAAA,KAAA,UAAA;AACA,UAAA,aAAA,KAAA;AACA,UAAA,eAAA,WAAA,aAAA,cAAA;AAGA,QAAA,gBAAA,mBAAA,OAAA;AACC,aAAA;AAAA,QAAO,OAAA,MAAA,aAAA,IAAA;AAAA,QACwB,OAAA,WAAA,cAAA;AAAA,QACE,UAAA;AAAA,MACtB;AAAA,IACX;AAID,WAAA;AAAA,MAAO,OAAA;AAAA,MACC,OAAA;AAAA,MACA,UAAA;AAAA,IACG;AAAA,EACX;AAAA,EACD,QAAA;AAEC,UAAA,YAAAE,WAAAA;AACA,UAAA,aAAAC,YAAAA;AACA,UAAA,YAAAC,WAAAA;AACA,UAAA,eAAAC,cAAAA;AACAC,kCAAA;AACA,UAAA,YAAAC,kBAAA,CAAA,CAAA;AACA,UAAA,eAAAA,kBAAA,IAAA;AACAA,kBAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,kBAAA,CAAA;AACA,UAAA,eAAAA,kBAAA,IAAA;AAGAC,kBAAAA,UAAA,MAAA;AAEC;AACCC,mCAAA,iBAAA,mBAAA;AAAA,MAAoC;AAIrCC,0CAAA,gBAAAC,cAAA,mBAAA,CAAA;AAAA,IAAmD,CAAA;AAEpD,UAAA,aAAAJ,cAAAA,IAAA,MAAA;AACA,UAAA,YAAAA,kBAAA,KAAA;AACA,UAAA,wBAAAA,kBAAA,KAAA;AAGA,UAAA,aAAAA,kBAAA,KAAA;AACA,UAAA,iBAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,mBAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,eAAAA,kBAAA,CAAA,CAAA;AACA,UAAA,aAAAA,kBAAA,CAAA;AACA,UAAA,gBAAAA,kBAAA,KAAA;AACA,UAAA,aAAAA,kBAAA,GAAA;AAGA,UAAA,WAAAK,cAAA,SAAA,MAAA,UAAA,kBAAA;AAGA,UAAA,iBAAAL,kBAAA,CAAA;AAGA,UAAA,UAAAK,cAAA,SAAA,MAAA,UAAA,UAAA;AAGAZ,kBAAAA,MAAA,cAAA;AAAA,MAAkB,SAAA,CAAA,QAAA;AAEhB,qBAAA,QAAA,IAAA;AAAA,MAAyB;AAAA,IAC1B,CAAA;AAIDA,wBAAA,IAAA,aAAA,MAAA;AAAA,IAA2B,CAAA;AAK3BA,kBAAAA,MAAA,IAAA,2BAAA,CAAA,SAAA;AACCC,qBAAAA,OAAA,IAAA,yBAAA,IAAA;AACA,UAAA,aAAA,UAAA,MAAA;AACC;MAAqB;AAAA,IACtB,CAAA;AAIDD,kBAAAA,MAAA,IAAA,iCAAA,CAAA,SAAA;AACCC,qBAAAA,OAAA,IAAA,yBAAA,IAAA;AACA;IAAqB,CAAA;AAItBD,kBAAAA,MAAA,IAAA,gCAAA,OAAA,SAAA;AACCC,qBAAAA,OAAA,IAAA,uBAAA,IAAA;AAGA,UAAA,CAAAS,qBAAA,eAAA,gBAAA,aAAA,KAAA,sBAAA,OAAA;AACCT,8BAAA,IAAA,2BAAA;AACAD,sBAAA,MAAA,oBAAA;AACA;AAAA,MAAA;AAID,UAAA,CAAAU,qBAAAA,eAAA,oBAAA;AACCV,sBAAA,MAAA,oBAAA;AACA;AAAA,MAAA;AAID;AAGA,UAAA;AACC,cAAA,cAAA;AAAA,MAAoB,SAAA,OAAA;AAEpBA,sBAAA,MAAA,MAAA,SAAA,gCAAA,qBAAA,KAAA;AAAA,MAAwC,UAAA;AAGxCU,6BAAA,eAAA,eAAA;AACAV,sBAAA,MAAA,oBAAA;AAAA,MAAwB;AAAA,IACzB,CAAA;AAIDA,kBAAAA,MAAA,IAAA,0BAAA,OAAA,SAAA;AACCC,qBAAAA,OAAA,IAAA,qBAAA,IAAA;AAEA;AAGA,UAAA;AACC,cAAA,cAAA;AAAA,MAAoB,SAAA,OAAA;AAEpBD,sBAAA,MAAA,MAAA,SAAA,gCAAA,qBAAA,KAAA;AAAA,MAAwC,UAAA;AAGxCU,6BAAA,eAAA,eAAA;AACAV,sBAAA,MAAA,oBAAA;AAAA,MAAwB;AAAA,IACzB,CAAA;AAIDA,kBAAAA,MAAA,IAAA,gCAAA,CAAA,SAAA;AACCC,qBAAAA,OAAA,IAAA,wBAAA,IAAA;AAEA,4BAAA,QAAA;AAAA,IAA8B,CAAA;AAI/BD,kBAAAA,MAAA,IAAA,+BAAA,CAAA,SAAA;AACCC,qBAAAA,OAAA,IAAA,wBAAA,IAAA;AAEA,iBAAA,MAAA;AACC,8BAAA,QAAA;AAAA,MAA8B,GAAA,GAAA;AAAA,IACzB,CAAA;AAIPD,kBAAAA,MAAA,IAAA,qBAAA,OAAA,SAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,gCAAA,uBAAA,IAAA;AAGA,YAAAa,cAAA,WAAA;AAGAb,0BAAA,MAAA,OAAA,gCAAA,aAAA,KAAA,MAAA,WAAA;AAGA,UAAA,KAAA,iBAAA,KAAA,gBAAA,UAAA,mBAAA;AACC,kBAAA,oBAAA,KAAA;AAAA,MAAmC;AAIpC,qBAAA;AAGA,YAAAa,cAAA,WAAA;AAEAb,oBAAAA,MAAA,MAAA,OAAA,gCAAA,WAAA,KAAA,MAAA,4BAAA,eAAA,KAAA,EAAA;AAAA,IAAoF,CAAA;AAIrFA,kBAAAA,MAAA,IAAA,sBAAA,OAAA,SAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,gCAAA,8CAAA,IAAA;AAGA,YAAA,cAAA,sBAAA;AACA,4BAAA,QAAA;AAGA,UAAA;AACCA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,8CAAA;AAGA,cAAA,IAAA,QAAA,CAAA,YAAA;AACC,qBAAA,MAAA;AAEC,uBAAA,YAAA;AACA,sBAAA,eAAA;AACA;UAAQ,GAAA,GAAA;AAAA,QACH,CAAA;AAKP,cAAA,WAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,mCAAA;AAGA,cAAA,UAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,kCAAA;AAGA,cAAA,aAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,qCAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,2CAAA;AAGAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,yCAAA;AAAA,MAAqD,SAAA,OAAA;AAErDA,sBAAA,MAAA,MAAA,SAAA,gCAAA,wCAAA,KAAA;AAAA,MAA2D,UAAA;AAG3D,mBAAA,MAAA;AACC,cAAA,CAAA,WAAA,OAAA;AACC,kCAAA,QAAA;AAAA,UAA8B;AAAA,QAC/B,GAAA,GAAA;AAAA,MACK;AAAA,IACP,CAAA;AAIDA,kBAAAA,MAAA,IAAA,aAAA,OAAA,SAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,gCAAA,qBAAA,IAAA;AACA,YAAA,UAAA,KAAA;AAGA,UAAA,WAAA,QAAA,SAAA;AACCA,sBAAA,MAAA,MAAA,OAAA,gCAAA,0CAAA,QAAA,OAAA;AACA,kBAAA,gBAAA,QAAA,OAAA;AAAA,MAAyC;AAI1C,UAAA;AAECA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,gCAAA;AACA,cAAA,WAAA;AAGAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,gCAAA;AACA,cAAA,UAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,uCAAA;AAAA,MAAmD,SAAA,OAAA;AAEnDA,sBAAA,MAAA,MAAA,SAAA,gCAAA,uCAAA,KAAA;AAAA,MAA0D;AAAA,IAC3D,CAAA;AAIDA,kBAAAA,MAAA,IAAA,uBAAA,CAAA,SAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,gCAAA,yBAAA,IAAA;AAGAA,0BAAA,MAAA,WAAA;AAGA,UAAA,UAAA,YAAA;AACCA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,+CAAA;AAGA,kBAAA,gBAAA;AAGA;MAAoB,OAAA;AAEpBA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,4BAAA;AAGA,YAAA,WAAA,aAAA,WAAA,GAAA;AACCA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,+CAAA;AACA,qBAAA,oBAAA;AAAA,QAA+B;AAAA,MAChC;AAIDA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,oBAAA;AAAA,IAAgC,CAAA;AAIjCA,kBAAAA,MAAA,IAAA,uBAAA,CAAA,SAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,gCAAA,yBAAA,IAAA;AACA;IAAW,CAAA;AAIZA,kBAAAA,MAAA,IAAA,uBAAA,OAAA,SAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,gCAAA,qBAAA,IAAA;AAEA,UAAA;AAEC,cAAA,cAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,gBAAA;AAAA,MAA4B,SAAA,OAAA;AAE5BA,sBAAA,MAAA,MAAA,SAAA,gCAAA,mBAAA,KAAA;AAAA,MAAsC;AAAA,IACvC,CAAA;AAIDA,kBAAAA,MAAA,IAAA,oBAAA,CAAA,SAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,gCAAA,UAAA,IAAA;AAEAA,oBAAA,MAAA,MAAA,sBAAA;AAAA,QAAgC,WAAA,KAAA,IAAA;AAAA,QACX,QAAA,KAAA,UAAA;AAAA,MACG,CAAA;AAAA,IACvB,CAAA;AAIFc,kBAAAA,YAAA,MAAA;AACCd,0BAAA,KAAA,WAAA;AACAA,0BAAA,KAAA,yBAAA;AACAA,0BAAA,KAAA,+BAAA;AACAA,0BAAA,KAAA,wBAAA;AACAA,0BAAA,KAAA,8BAAA;AACAA,0BAAA,KAAA,WAAA;AACAA,0BAAA,KAAA,qBAAA;AACAA,0BAAA,KAAA,qBAAA;AACAA,0BAAA,KAAA,kBAAA;AACAA,0BAAA,KAAA,oBAAA;AACAA,0BAAA,KAAA,8BAAA;AACAA,0BAAA,KAAA,6BAAA;AACAA,0BAAA,KAAA,mBAAA;AAAA,IAA4B,CAAA;AAI7B,UAAA,aAAA,MAAA;AACCA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,mBAAA;AAEA;AAEAU,2BAAA,eAAA,SAAA;AAAA,IAAwB;AAUzB,UAAA,kBAAA,CAAA,SAAA;AACCV,oBAAA,MAAA,MAAA,OAAA,gCAAA,YAAA,IAAA;AAGA,UAAA,CAAAU,qBAAA,eAAA,gBAAA,MAAA,GAAA;AACCV,sBAAAA,MAAA,MAAA,OAAA,gCAAA,wBAAA;AACA;AAAA,MAAA;AAID,4BAAA,QAAA;AAGA,UAAA,CAAAU,qBAAA,eAAA,UAAA,EAAA,QAAA,KAAA,QAAA,OAAA,KAAA,MAAA,CAAA,GAAA;AAEC,8BAAA,QAAA;AACA;AAAA,MAAA;AAID;AAGA,iBAAA,QAAA;AACA,qBAAA,QAAA,KAAA;AACA,uBAAA,QAAA,KAAA;AACA,iBAAA,QAAA,KAAA;AAGAV,oBAAA,MAAA,MAAA,oBAAA,EAAA,QAAA,OAAA,CAAA;AAGA,mBAAA,QAAA,SAAA,MAAA,IAAA,CAAA,SAAA,KAAA,EAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,gCAAA,WAAA,aAAA,KAAA;AACAA,0BAAA,MAAA,OAAA,gCAAA,aAAA,SAAA,MAAA,MAAA;AAGA,UAAA;AACC,cAAA,QAAAA,oBAAA;AACA,cAAA,OAAA,sBAAA,EAAA,mBAAA,CAAA,UAAA;AACC,cAAA,SAAA,MAAA,QAAA;AAEC,uBAAA,QAAA,MAAA;AACAA,0BAAA,MAAA,MAAA,OAAA,gCAAA,cAAA,WAAA,KAAA;AAAA,UAA0C;AAAA,QAC3C,CAAA,EAAA,KAAA;AAAA,MACO,SAAA,GAAA;AAERA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,CAAA;AAEA,mBAAA,QAAA;AAAA,MAAmB;AAIpB,UAAA;AACCA,sBAAA,MAAA,aAAA;AAAA,MAAiB,SAAA,GAAA;AAEjBA,sBAAA,MAAA,MAAA,SAAA,gCAAA,SAAA,CAAA;AAAA,MAAwB;AAGzBA,oBAAA,MAAA,MAAA,OAAA,gCAAA,qBAAA,iBAAA,KAAA;AAAA,IAAuD;AAIxD,UAAA,iBAAA,CAAA,SAAA;AACC,UAAA,CAAA,WAAA,OAAA;AACCA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,cAAA;AACA;AAAA,MAAA;AAID,YAAA,eAAA,KAAA,UAAA,WAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,SAAA;AAAA,QAAqB,UAAA,KAAA;AAAA,QACL,QAAA,WAAA;AAAA,QACI;AAAA,QACnB,cAAA,iBAAA;AAAA,MAC+B,CAAA;AAIhC,UAAA,KAAA,IAAA,YAAA,IAAA,GAAA;AACC;AAAA,MAAA;AAID,UAAA,cAAA,OAAA;AACC;AAAA,MAAA;AAID,oBAAA,QAAA;AACA,iBAAA,MAAA;AACC,sBAAA,QAAA;AAAA,MAAsB,GAAA,EAAA;AAIvB,YAAA,YAAA,eAAA,IAAA,OAAA;AACA,YAAA,eAAA,iBAAA;AACA,UAAA,cAAA;AAIA,YAAA,YAAA,KAAA,IAAA,YAAA,IAAA,WAAA;AAIA,YAAA,aAAA,aAAA;AAEA,UAAA,cAAA,QAAA,eAAA,KAAA,YAAA;AAEC,sBAAA,eAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,gCAAA,cAAA,WAAA;AAGA,8BAAA,WAAA;AAAA,MAAiC,WAAA,cAAA,UAAA,eAAA,SAAA,MAAA,SAAA,KAAA,YAAA;AAGjC,sBAAA,eAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,gCAAA,cAAA,WAAA;AAGA,8BAAA,WAAA;AAAA,MAAiC,OAAA;AAIjC;AAAA,MAAA;AAIDA,0BAAA,MAAA,OAAA,gCAAA,SAAA,cAAA,MAAA,WAAA;AAGA,gBAAA,cAAA,WAAA;AAGA,uBAAA,QAAA;AAIA,iBAAA,QAAA,KAAA,UAAA,eAAA;AAGA,UAAA;AACCA,sBAAAA,MAAA,aAAA;AAAA,UAAiB,SAAA,WAAA;AAEfA,0BAAAA,MAAA,MAAA,OAAA,gCAAA,UAAA;AAAA,UAAsB;AAAA,QACvB,CAAA;AAAA,MACA,SAAA,GAAA;AAEDA,sBAAA,MAAA,MAAA,SAAA,gCAAA,cAAA,CAAA;AAAA,MAA6B;AAAA,IAC9B;AAID,UAAA,wBAAA,CAAA,gBAAA;AACC,UAAA,CAAA,UAAA,SAAA,UAAA,MAAA,UAAA,aAAA;AACC;AAAA,MAAA;AAGD,UAAA;AAEC,cAAA,QAAAA,oBAAA;AAGA,cAAA,OAAA,YAAA,EAAA,aAAA,CAAA,aAAA;AACC,cAAA,CAAA;AAAe;AAGf,gBAAA,UAAA,sBAAA,EAAA,aAAA,CAAA,cAAA;AACC,gBAAA,CAAA,aAAA,CAAA,UAAA,WAAA;AAA2C;AAG3C,kBAAA,WAAA,UAAA,WAAA;AACA,kBAAA,UAAA,SAAA;AACA,kBAAA,aAAA,SAAA;AACA,kBAAA,UAAA,SAAA;AACA,kBAAA,aAAA,SAAA;AAEAA,0BAAAA,MAAA,MAAA,OAAA,gCAAA,YAAA;AAAA,cAAwB;AAAA,cACvB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACA,CAAA;AAID,kBAAA,OAAA,YAAA,EAAA,aAAA,CAAA,eAAA;AACC,kBAAA,CAAA;AAAiB;AAEjB,oBAAA,mBAAA,WAAA;AACA,kBAAA,eAAA;AAGA,kBAAA,UAAA,UAAA,OAAA,mBAAA,GAAA;AAEC,+BAAA,KAAA,IAAA,GAAA,oBAAA,SAAA,SAAA,GAAA;AACAA,8BAAA,MAAA,MAAA,OAAA,gCAAA,UAAA,YAAA;AAAA,cAAkC,WAAA,aAAA,aAAA,KAAA;AAKlC,+BAAA,oBAAA,SAAA,SAAA;AACAA,8BAAA,MAAA,MAAA,OAAA,gCAAA,UAAA,YAAA;AAAA,cAAkC,OAAA;AAGlC;AAAA,cAAA;AAID,kBAAA;AACCA,8BAAAA,MAAA,aAAA;AAAA,kBAAiB,WAAA;AAAA,kBACL,UAAA;AAAA,gBACD,CAAA;AAEXA,8BAAA,MAAA,MAAA,OAAA,gCAAA,WAAA,YAAA;AAAA,cAAmC,SAAA,GAAA;AAEnCA,8BAAA,MAAA,MAAA,QAAA,gCAAA,SAAA,CAAA;AAAA,cAAuB;AAAA,YACxB,CAAA,EAAA,KAAA;AAAA,UACO,CAAA,EAAA,KAAA;AAAA,QACD,CAAA,EAAA,KAAA;AAAA,MACD,SAAA,GAAA;AAERA,sBAAA,MAAA,MAAA,SAAA,gCAAA,cAAA,CAAA;AAAA,MAA6B;AAAA,IAC9B;AA8CD,UAAA,gBAAA,MAAA;AACCA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,MAAA;AAEA,UAAA,WAAA,OAAA;AAEC,mBAAA,QAAA;AAMA,mBAAA,MAAA;AAECA,wBAAA,MAAA,MAAA,uBAAA,EAAA,WAAA,KAAA,IAAA,EAAA,CAAA;AAAA,QAA0D,GAAA,EAAA;AAI3D,cAAA,WAAA,SAAA,MAAA,IAAA,CAAA,SAAA,KAAA,EAAA;AAGA,cAAA,kBAAA,aAAA,MAAA,KAAA,CAAA,IAAA,UAAA,OAAA,SAAA,KAAA,CAAA;AAEA,YAAA,iBAAA;AAEC,oBAAA,gBAAA,QAAA;AAGA,cAAA;AACCA,0BAAA,MAAA,aAAA;AAAA,UAAiB,SAAA,GAAA;AAEjBA,0BAAA,MAAA,MAAA,SAAA,gCAAA,SAAA,CAAA;AAAA,UAAwB;AAAA,QACzB;AAIFU,6BAAAA,eAAA,QAAA,EAAA,gBAAA,CAAA;AAGA,mBAAA,MAAA;AACC,gCAAA,QAAA;AAAA,QAA8B,GAAA,GAAA;AAI9B,uBAAA,QAAA;AACA,yBAAA,QAAA;AACA,qBAAA,QAAA;MAAsB;AAAA,IACvB;AAMD,UAAA,YAAA,CAAA,WAAA,YAAA;AACC,UAAA,cAAA;AAA2B;AAE3BV,0BAAA,MAAA,OAAA,gCAAA,SAAA,WAAA,MAAA,OAAA;AAGA,YAAA,cAAA,CAAA,GAAA,SAAA,KAAA;AAGA,YAAA,aAAA,YAAA,SAAA;AAGA,kBAAA,OAAA,WAAA,CAAA;AAGA,kBAAA,OAAA,SAAA,GAAA,UAAA;AAGA,gBAAA,eAAA,WAAA;AAGA,UAAA;AACCA,sBAAAA,MAAA,aAAA;AAAA,UAAiB,SAAA,WAAA;AAEfA,0BAAAA,MAAA,MAAA,OAAA,gCAAA,UAAA;AAAA,UAAsB;AAAA,QACvB,CAAA;AAAA,MACA,SAAA,GAAA;AAEDA,sBAAA,MAAA,MAAA,SAAA,gCAAA,cAAA,CAAA;AAAA,MAA6B;AAAA,IAC9B;AAID,UAAA,uBAAA,MAAA;AAEC,UAAA,UAAA,SAAA,UAAA,MAAA,QAAA;AACC,kBAAA,MAAA,QAAA,CAAA,SAAA;AACC,cAAA,QAAA,KAAA,YAAA;AACC,iBAAA,WAAA;AAAA,UAAgB;AAAA,QACjB,CAAA;AAAA,MACA;AAGF,mBAAA,QAAA;AAAA,IAAqB;AAItB,UAAA,cAAA,MAAA;AAEC,YAAA,iBAAA,UAAA;AAGA,YAAA,MAAA,mBAAA,QAAA,gDAAA,cAAA,KAAA;AAIAA,oBAAAA,MAAA,WAAA;AAAA,QAAe;AAAA,MACd,CAAA;AAAA,IACA;AAIF,UAAA,gBAAA,CAAA,YAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,gCAAA,6BAAA,OAAA;AACA,UAAA;AACC,kBAAA,gBAAA,OAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,wBAAA;AAGA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,wBAAA;AAGAA,sBAAAA,MAAA,aAAA;AAAA,UAAiB,WAAA;AAAA,UACL,UAAA;AAAA,QACD,CAAA;AAEXA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,yBAAA;AAAA,MAAqC,SAAA,GAAA;AAErCA,sBAAA,MAAA,MAAA,SAAA,gCAAA,gCAAA,CAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,yBAAA,EAAA,KAAA;AAAA,MAA8C;AAAA,IAC/C;AAID,UAAA,iBAAA,CAAA,WAAA;AACC,mBAAA,QAAA;AAAA,IAAqB;AAItB,UAAA,kBAAA,MAAA;AACC,mBAAA,QAAA;AAAA,IAAqB;AAItB,UAAA,uBAAA,CAAA,WAAA;AAEC,UAAA,aAAA,SAAA,aAAA,UAAA,QAAA;AACC;MAAqB;AAAA,IACtB;AAID,UAAA,2BAAA,CAAA,SAAA;AAECA,oBAAA,MAAA,MAAA,OAAA,gCAAA,WAAA,IAAA;AAGA,UAAA,QAAA,KAAA,SAAA,IAAA;AACC;MAAqB;AAAA,IACtB;AAID,UAAA,kBAAA,CAAA,MAAA;AAGC,UAAA,WAAA,OAAA;AAEC,UAAA,kBAAA,EAAA;AACA,UAAA,mBAAA,EAAA;AAGA,YAAA,EAAA,YAAA;AACC,YAAA,cAAA,EAAA;QAAiC;AAIlC,eAAA;AAAA,MAAO;AAIR,YAAA,WAAA,EAAA,QAAA,CAAA,EAAA;AAGA,UAAA,WAAA,UAAA,QAAA;AACC,cAAA,SAAA,KAAA,IAAA,WAAA,WAAA,KAAA;AAIA,YAAA,aAAA,UAAA,QAAA,SAAA,IAAA;AACC;QAAqB;AAAA,MACtB;AAGD,iBAAA,QAAA;AAAA,IAAmB;AAIpB,UAAA,qBAAA,CAAA,MAAA;AAEC,iBAAA,QAAA;AAAA,IAAmB;AAIpB,UAAA,eAAA,CAAA,MAAA;AAEC,UAAA,aAAA,SAAA,KAAA,EAAA,YAAA,IAAA;AACC;MAAqB;AAAA,IACtB;AA8BD,UAAA,uBAAAO,kBAAA,CAAA;AACA,UAAA,uBAAAA,kBAAA,CAAA;AAEA,UAAA,4BAAA,CAAA,MAAA;AACC,UAAA,EAAA,WAAA,EAAA,QAAA,SAAA,GAAA;AACC,6BAAA,QAAA,EAAA,QAAA,CAAA,EAAA;AACA,6BAAA,QAAA,EAAA,QAAA,CAAA,EAAA;AAAA,MAA0C;AAI3C,UAAA,sBAAA,SAAA,WAAA,OAAA;AACC,UAAA,kBAAA,EAAA;AACA,UAAA,mBAAA,EAAA;AACA,eAAA;AAAA,MAAO;AAAA,IACR;AAID,UAAA,2BAAA,CAAA,MAAA;AAEC,UAAA,sBAAA,SAAA,WAAA,OAAA;AACC,UAAA,kBAAA,EAAA;AACA,UAAA,mBAAA,EAAA;AACA,eAAA;AAAA,MAAO;AAGR,UAAA,EAAA,WAAA,EAAA,QAAA,SAAA,GAAA;AACC,cAAA,WAAA,EAAA,QAAA,CAAA,EAAA;AACA,cAAA,WAAA,EAAA,QAAA,CAAA,EAAA;AAEA,cAAA,SAAA,KAAA,IAAA,WAAA,qBAAA,KAAA;AACA,cAAA,SAAA,KAAA,IAAA,WAAA,qBAAA,KAAA;AAGA,YAAA,SAAA,UAAA,SAAA,IAAA;AACC,YAAA,eAAA;AACA,YAAA,gBAAA;AACA,iBAAA;AAAA,QAAO;AAAA,MACR;AAAA,IACD;AAID,UAAA,0BAAA,CAAA,MAAA;AACC,2BAAA,QAAA;AACA,2BAAA,QAAA;AAAA,IAA6B;AAI9B,UAAA,eAAA,OAAA,OAAA;AAEC,UAAA,CAAA,UAAA,yBAAA;AACCP,sBAAAA,MAAA,MAAA,OAAA,iCAAA,eAAA;AACA;AAAA,MAAA;AAGD,UAAA;AACC,cAAA,UAAA,aAAA,EAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UAAc,OAAA;AAAA,UACN,MAAA;AAAA,UACD,UAAA;AAAA,QACI,CAAA;AAAA,MACV,SAAA,OAAA;AAEDA,sBAAA,MAAA,MAAA,SAAA,iCAAA,WAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UAAc,OAAA;AAAA,UACN,MAAA;AAAA,UACD,UAAA;AAAA,QACI,CAAA;AAAA,MACV;AAAA,IACF;AAID,UAAA,aAAA,OAAA,OAAA;AAEC,UAAA,CAAA,UAAA,yBAAA;AACCA,sBAAAA,MAAA,MAAA,OAAA,iCAAA,eAAA;AACA;AAAA,MAAA;AAGD,UAAA;AAEC;AAGA,cAAA,2BAAA,UAAA;AAGA,cAAA,UAAA,WAAA,EAAA;AAGA,cAAAa,cAAA,WAAA;AAGAb,sBAAA,MAAA,MAAA,qBAAA;AAAA,UAA+B,WAAA,KAAA,IAAA;AAAA,UACV,QAAA;AAAA,UACZ,WAAA;AAAA,QACG,CAAA;AAIZ,YAAA,UAAA,mBAAA,0BAAA;AAECA,wBAAAA,MAAA,MAAA,OAAA,iCAAA,eAAA;AAAA,QAA2B;AAI5BA,sBAAAA,MAAA,UAAA;AAAA,UAAc,OAAA;AAAA,UACN,MAAA;AAAA,UACD,UAAA;AAAA,QACI,CAAA;AAGXA,sBAAAA,MAAA,MAAA,OAAA,iCAAA,cAAA;AAAA,MAA0B,SAAA,OAAA;AAG1BA,sBAAA,MAAA,MAAA,SAAA,iCAAA,WAAA,KAAA;AAAA,MAA8B;AAAA,IAC/B;AAID,UAAA,YAAA,CAAA,OAAA;AAEC,UAAA,CAAA,UAAA,yBAAA;AACCA,sBAAAA,MAAA,MAAA,OAAA,iCAAA,eAAA;AACA;AAAA,MAAA;AAIDA,oBAAAA,MAAA,YAAA;AAAA,QAAgB,OAAA;AAAA,QACR,MAAA;AAAA,MACD,CAAA;AAIP,iBAAA,MAAA;AAECA,4BAAA,cAAA,MAAA,MAAA;AAAA,QAA8B,CAAA;AAG9BA,sBAAAA,MAAA,UAAA;AAAA,UAAc,OAAA;AAAA,UACN,MAAA;AAAA,UACD,UAAA;AAAA,QACI,CAAA;AAIXA,sBAAA,MAAA,aAAA;AAAA,MAAiB,GAAA,IAAA;AAAA,IACX;AAIR,UAAA,mBAAA,YAAA;AACC,gBAAA;AAIA,UAAA;AAEC,cAAA,WAAA;AAGA,cAAA,UAAA;MAA6B,SAAA,OAAA;AAE7BA,sBAAA,MAAA,MAAA,SAAA,iCAAA,iDAAA,KAAA;AAAA,MAAoE;AAIrEA,oBAAAA,MAAA,MAAA,OAAA,iCAAA,kDAAA;AAAA,IAA8D;AA+B/D,UAAA,sBAAA,MAAA;AAEC,mBAAA,aAAA;AAGA,YAAA,cAAA,aAAA;AAGA,UAAA,cAAA,GAAA;AACCA,sBAAAA,MAAA,eAAA;AAAA,UAAmB,OAAA;AAAA;AAAA,UACX,MAAA,YAAA,SAAA;AAAA,QACoB,CAAA;AAAA,MAC3B,OAAA;AAGD,YAAA;AACCA,wBAAAA,MAAA,kBAAA;AAAA,YAAsB,OAAA;AAAA,UACd,CAAA;AAAA,QACP,SAAA,GAAA;AAEDA,wBAAAA,MAAA,MAAA,OAAA,iCAAA,iBAAA;AAAA,QAA6B;AAAA,MAC9B;AAAA,IACD;AAUD,UAAA,gBAAA,YAAA;AACCA,oBAAAA,MAAA,MAAA,OAAA,iCAAA,oCAAA;AAEA,UAAA,UAAA,OAAA;AACCA,sBAAAA,MAAA,MAAA,OAAA,iCAAA,kCAAA;AACA;AAAA,MAAA;AAGD,UAAA;AACC,kBAAA,QAAA;AAGA,cAAA,UAAA,kBAAA,IAAA;AAAA,MAAsC,SAAA,OAAA;AAGtCA,sBAAA,MAAA,MAAA,SAAA,iCAAA,+BAAA,KAAA;AAAA,MAAkD,UAAA;AAElD,kBAAA,QAAA;AAAA,MAAkB;AAAA,IACnB;AAGD,WAAA;AAAA,MAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,IAEA;AAAA,EACD;AAAA,EACD,OAAA;AAEC,WAAA;AAAA,MAAO,WAAA;AAAA,IACK;AAAA,EACZ;AAAA,EACD,UAAA;AAAA,IACU,WAAA;AAER,aAAA,KAAA,UAAA;AAAA,IAAsB;AAAA,IACvB,UAAA;AAEC,aAAA,KAAA,UAAA;AAAA,IAAsB;AAAA,EACvB;AAAA;AAAA,EACD,MAAA,OAAA,SAAA;AAICA,kBAAAA,MAAA,MAAA,OAAA,iCAAA,8BAAA;AAGAA,kBAAA,MAAA,MAAA,aAAA;AAAA,MAAuB;AAAA,MACtB,WAAA,KAAA,IAAA;AAAA,IACoB,CAAA;AAAA,EACpB;AAAA;AAAA,EACF,SAAA;AAICA,kBAAAA,MAAA,MAAA,OAAA,iCAAA,+BAAA;AAGAA,kBAAA,MAAA,MAAA,uBAAA;AAAA,MAAiC,WAAA,KAAA,IAAA;AAAA,IACZ,CAAA;AAAA,EACpB;AAAA;AAAA,EACF,SAAA;AAGCA,kBAAAA,MAAA,MAAA,OAAA,iCAAA,qBAAA;AAEAA,kBAAA,MAAA,MAAA,uBAAA;AAAA,MAAiC,WAAA,KAAA,IAAA;AAAA,IACZ,CAAA;AAAA,EACpB;AAAA;AAAA,EACF,OAAA;AAAA,IAEO,UAAA;AAAA,MACI,UAAA;AAGRA,sBAAAA,MAAA,MAAA,OAAA,iCAAA,SAAA;AAAA,MAAqB;AAAA,MACtB,MAAA;AAAA,IACM;AAAA,EACP;AAAA,EACD,SAAA;AAAA;AAAA,EACS;AAGV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACr5CD,GAAG,WAAW,eAAe;"}