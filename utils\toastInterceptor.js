/**
 * 全局Toast拦截器
 * 用于拦截和过滤版本冲突相关的弹窗
 */

class ToastInterceptor {
  constructor() {
    this.originalShowToast = null
    this.originalShowModal = null
    this.isInitialized = false
    
    // 版本冲突相关的关键词
    this.conflictKeywords = [
      '版本冲突', '数据冲突', '逻辑时钟冲突', 'version conflict', 'conflict',
      '数据版本冲突', '冲突', 'conflict detected', 'conflict resolution',
      '同步冲突', 'sync conflict', '更新冲突', 'update conflict'
    ]
    
    // 拦截统计
    this.interceptCount = 0
    this.lastInterceptTime = 0
  }

  // 初始化拦截器
  init() {
    if (this.isInitialized) return
    
    try {
      // 保存原始方法
      this.originalShowToast = uni.showToast
      this.originalShowModal = uni.showModal
      
      // 替换uni.showToast
      uni.showToast = (options) => {
        return this.interceptShowToast(options)
      }
      
      // 替换uni.showModal
      uni.showModal = (options) => {
        return this.interceptShowModal(options)
      }
      
      this.isInitialized = true
      console.log('[ToastInterceptor] 🚀 全局弹窗拦截器已启动')
    } catch (error) {
      console.error('[ToastInterceptor] 初始化失败:', error)
    }
  }

  // 检查是否是冲突相关的消息
  isConflictMessage(message) {
    if (!message || typeof message !== 'string') return false
    
    const lowerMessage = message.toLowerCase()
    return this.conflictKeywords.some(keyword => 
      lowerMessage.includes(keyword.toLowerCase())
    )
  }

  // 拦截showToast
  interceptShowToast(options) {
    if (!options || !options.title) {
      return this.originalShowToast.call(uni, options)
    }
    
    if (this.isConflictMessage(options.title)) {
      this.interceptCount++
      this.lastInterceptTime = Date.now()
      
      console.log(`[ToastInterceptor] 🛡️ 拦截版本冲突Toast: "${options.title}"`)
      console.log(`[ToastInterceptor] 📊 累计拦截次数: ${this.interceptCount}`)
      
      // 不显示弹窗，直接返回成功
      return Promise.resolve()
    }
    
    // 非冲突消息正常显示
    return this.originalShowToast.call(uni, options)
  }

  // 拦截showModal
  interceptShowModal(options) {
    if (!options) {
      return this.originalShowModal.call(uni, options)
    }
    
    const title = options.title || ''
    const content = options.content || ''
    
    if (this.isConflictMessage(title) || this.isConflictMessage(content)) {
      this.interceptCount++
      this.lastInterceptTime = Date.now()
      
      console.log(`[ToastInterceptor] 🛡️ 拦截版本冲突Modal: "${title}" - "${content}"`)
      console.log(`[ToastInterceptor] 📊 累计拦截次数: ${this.interceptCount}`)
      
      // 不显示弹窗，模拟用户点击确认
      if (options.success) {
        setTimeout(() => {
          options.success({ confirm: true, cancel: false })
        }, 0)
      }
      
      return Promise.resolve()
    }
    
    // 非冲突消息正常显示
    return this.originalShowModal.call(uni, options)
  }

  // 恢复原始方法
  restore() {
    if (!this.isInitialized) return
    
    try {
      if (this.originalShowToast) {
        uni.showToast = this.originalShowToast
      }
      
      if (this.originalShowModal) {
        uni.showModal = this.originalShowModal
      }
      
      this.isInitialized = false
      console.log('[ToastInterceptor] 🔄 全局弹窗拦截器已恢复')
    } catch (error) {
      console.error('[ToastInterceptor] 恢复失败:', error)
    }
  }

  // 获取拦截统计
  getStats() {
    return {
      interceptCount: this.interceptCount,
      lastInterceptTime: this.lastInterceptTime,
      isActive: this.isInitialized
    }
  }

  // 重置统计
  resetStats() {
    this.interceptCount = 0
    this.lastInterceptTime = 0
    console.log('[ToastInterceptor] 📊 统计已重置')
  }

  // 添加自定义关键词
  addConflictKeyword(keyword) {
    if (keyword && !this.conflictKeywords.includes(keyword)) {
      this.conflictKeywords.push(keyword)
      console.log(`[ToastInterceptor] ➕ 添加冲突关键词: "${keyword}"`)
    }
  }

  // 移除关键词
  removeConflictKeyword(keyword) {
    const index = this.conflictKeywords.indexOf(keyword)
    if (index > -1) {
      this.conflictKeywords.splice(index, 1)
      console.log(`[ToastInterceptor] ➖ 移除冲突关键词: "${keyword}"`)
    }
  }
}

// 创建全局实例
const toastInterceptor = new ToastInterceptor()

// 导出实例和类
export default toastInterceptor
export { ToastInterceptor }
