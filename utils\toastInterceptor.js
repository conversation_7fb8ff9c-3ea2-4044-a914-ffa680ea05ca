/**
 * 临时调试文件 - 找出谁在引用这个文件
 * 这个文件应该被删除，但目前用于调试
 */

// 打印调用栈，找出是谁在引用这个文件
console.error('[toastInterceptor.js] ⚠️ 这个文件被引用了！调用栈：', new Error().stack)

// 空的导出，防止编译错误
const emptyObject = {}

export default emptyObject

// 兼容可能的引用
export const ToastInterceptor = emptyObject
export const toastInterceptor = emptyObject

// 如果有人尝试使用这些对象，记录下来
const handler = {
  get(target, prop) {
    console.error(`[toastInterceptor.js] ⚠️ 有代码尝试访问 ${prop}，调用栈：`, new Error().stack)
    return undefined
  }
}

export const DebugToastInterceptor = new Proxy(emptyObject, handler)
