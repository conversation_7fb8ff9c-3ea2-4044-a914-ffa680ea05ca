"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const store_user = require("./store/user.js");
const store_wish = require("./store/wish.js");
const store_group = require("./store/group.js");
const store_message = require("./store/message.js");
const store_comment = require("./store/comment.js");
const services_authService = require("./services/authService.js");
const utils_syncManager = require("./utils/syncManager.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/login/login.js";
  "./pages/message/message.js";
  "./pages/profile/profile.js";
  "./subpkg-profile/pages/userEdit/userEdit.js";
  "./subpkg-profile/pages/addressManage/addressManage.js";
  "./subpkg-profile/pages/addressEdit/addressEdit.js";
  "./subpkg-profile/pages/historyWish/historyWish.js";
  "./subpkg-profile/pages/about/about.js";
  "./subpkg-wish/pages/wishDetail/wishDetail.js";
  "./subpkg-wish/pages/editWish/editWish.js";
  "./subpkg-wish/pages/groupManage/groupManage.js";
  "./subpkg-wish/pages/groupSort/groupSort.js";
}
const _sfc_main = {
  async onLaunch() {
    common_vendor.index.__f__("log", "at App.vue:12", "App Launch");
    services_authService.authService.setupRequestInterceptor();
    const userStore = store_user.useUserStore();
    store_wish.useWishStore();
    store_group.useGroupStore();
    store_message.useMessageStore();
    store_comment.initCommentStore();
    await userStore.loadUserFromStorage();
    common_vendor.index.__f__("log", "at App.vue:30", "[App.vue onLaunch] Initial login status after server verification: ", userStore.isLogin);
    if (userStore.isLogin) {
      try {
        common_vendor.index.__f__("log", "at App.vue:35", "[App.vue onLaunch] 🚀 Initializing uni-push 2.0 sync system...");
        await utils_syncManager.syncManager.init();
        common_vendor.index.__f__("log", "at App.vue:39", "[App.vue onLaunch] ✅ uni-push 2.0 sync system initialized successfully");
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:42", "[App.vue onLaunch] ❌ uni-push 2.0 sync system initialization failed:", error);
      }
    }
    common_vendor.index.__f__("log", "at App.vue:47", "[App.vue] 设置 uni-push 消息监听");
    common_vendor.index.onPushMessage((res) => {
      common_vendor.index.__f__("log", "at App.vue:49", "[App.vue] 收到推送消息:", res);
      if (utils_syncManager.syncManager && utils_syncManager.syncManager.handlePushMessage) {
        utils_syncManager.syncManager.handlePushMessage(res);
      }
    });
    Promise.resolve().then(async () => {
      try {
        common_vendor.index.__f__("log", "at App.vue:59", "[App.vue onLaunch] Initializing database collections in background...");
        const commentCenter = common_vendor.nr.importObject("comment-center");
        const initResult = await commentCenter.ensureCollectionsExist();
        common_vendor.index.__f__("log", "at App.vue:62", "[App.vue onLaunch] Database collections initialized:", initResult);
      } catch (error) {
        common_vendor.index.__f__("warn", "at App.vue:64", "[App.vue onLaunch] Database initialization failed (non-critical):", error);
      }
    });
    common_vendor.index.__f__("log", "at App.vue:70", "[App.vue onLaunch] User login status checked. Store initialization will be handled by pages or loginSuccess.");
    const strictlyAuthRequiredPages = [
      "/pages/profile/profile",
      // 用户中心主页
      "/pages/message/message",
      // 消息中心
      "/subpkg-profile/pages/addressManage/addressManage",
      // 地址管理
      "/subpkg-profile/pages/addressEdit/addressEdit",
      // 地址编辑
      "/subpkg-profile/pages/historyWish/historyWish"
      // 历史心愿
      // 添加其他必须先登录才能进入的页面
    ];
    const handleNavigation = (args, navigationType) => {
      const targetUrl = args.url.split("?")[0];
      let absoluteTargetUrl = targetUrl.startsWith("/") ? targetUrl : `/${targetUrl}`;
      if (navigationType === "switchTab") {
        absoluteTargetUrl = args.url;
      }
      common_vendor.index.__f__("log", "at App.vue:91", `[App NavInterceptor (${navigationType})] Attempting to navigate to: ${absoluteTargetUrl}`);
      if (strictlyAuthRequiredPages.includes(absoluteTargetUrl)) {
        if (!userStore.checkLoginStatus()) {
          common_vendor.index.__f__("log", "at App.vue:101", `[App NavInterceptor (${navigationType})] Strictly required page ${absoluteTargetUrl} and user not logged in. Redirecting.`);
          common_vendor.index.navigateTo({
            url: "/pages/login/login?redirect=" + encodeURIComponent(args.url)
          });
          return false;
        }
      }
      return true;
    };
    common_vendor.index.addInterceptor("navigateTo", {
      invoke(args) {
        return handleNavigation(args, "navigateTo");
      },
      fail(err) {
        common_vendor.index.__f__("error", "at App.vue:120", "[Interceptor navigateTo fail]:", err.errMsg);
      }
    });
    common_vendor.index.addInterceptor("redirectTo", {
      invoke(args) {
        return handleNavigation(args, "redirectTo");
      },
      fail(err) {
        common_vendor.index.__f__("error", "at App.vue:129", "[Interceptor redirectTo fail]:", err.errMsg);
      }
    });
    common_vendor.index.addInterceptor("reLaunch", {
      invoke(args) {
        return handleNavigation(args, "reLaunch");
      },
      fail(err) {
        common_vendor.index.__f__("error", "at App.vue:138", "[Interceptor reLaunch fail]:", err.errMsg);
      }
    });
    common_vendor.index.addInterceptor("switchTab", {
      async invoke(args) {
        store_user.useUserStore();
        return handleNavigation(args, "switchTab");
      },
      fail(err) {
        common_vendor.index.__f__("error", "at App.vue:151", "[Interceptor switchTab fail]:", err.errMsg);
      }
    });
    common_vendor.index.__f__("log", "at App.vue:155", "[App.vue onLaunch] App launch sequence finished.");
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:160", "App Show");
    if (utils_syncManager.syncManager && utils_syncManager.syncManager.onAppShow) {
      utils_syncManager.syncManager.onAppShow();
    }
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:167", "App Hide");
    if (utils_syncManager.syncManager && utils_syncManager.syncManager.onAppHide) {
      utils_syncManager.syncManager.onAppHide();
    }
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  const pinia = common_vendor.createPinia();
  app.use(pinia);
  app.config.errorHandler = (err, instance, info) => {
    if (err && err.message && (err.message.includes("数据版本冲突") || err.message.includes("版本冲突") || err.message.includes("数据冲突") || err.message.includes("version conflict") || err.message.includes("conflict"))) {
      common_vendor.index.__f__("log", "at main.js:37", "[Global Error Handler] 静默处理版本冲突错误:", err.message);
      return;
    }
    common_vendor.index.__f__("error", "at main.js:42", "[Global Error Handler] 应用错误:", err, info);
  };
  setTimeout(async () => {
    if (typeof common_vendor.index !== "undefined") {
      common_vendor.index.addInterceptor("showModal", {
        invoke(args) {
          common_vendor.index.__f__("log", "at main.js:52", "[Modal Interceptor] 检测到弹窗:", {
            title: args == null ? void 0 : args.title,
            content: args == null ? void 0 : args.content,
            timestamp: (/* @__PURE__ */ new Date()).toISOString()
          });
          if (args && (args.title || args.content)) {
            const title = args.title || "";
            const content = args.content || "";
            const text = (title + " " + content).toLowerCase();
            if (text.includes("数据版本冲突") || text.includes("版本冲突") || text.includes("数据冲突") || text.includes("version conflict") || text.includes("conflict")) {
              common_vendor.index.__f__("log", "at main.js:69", "[Modal Interceptor] 🚫 拦截版本冲突弹窗:", { title, content });
              console.trace("[Modal Interceptor] 版本冲突弹窗调用栈:");
              return false;
            }
          }
          return args;
        }
      });
      if (typeof common_vendor.nr !== "undefined" && common_vendor.nr.onError) {
        common_vendor.nr.onError((error) => {
          if (error && error.message && (error.message.includes("数据版本冲突") || error.message.includes("版本冲突") || error.message.includes("数据冲突") || error.message.includes("version conflict") || error.message.includes("conflict"))) {
            common_vendor.index.__f__("log", "at main.js:91", "[uniCloud Error Interceptor] 拦截版本冲突错误:", error.message);
            return false;
          }
          return true;
        });
      }
      try {
        await utils_syncManager.syncManager.init();
        common_vendor.index.__f__("log", "at main.js:100", "[main.js] 🚀 实时推送同步系统启动成功");
      } catch (error) {
        common_vendor.index.__f__("error", "at main.js:102", "[main.js] 同步管理器初始化失败:", error);
      }
    } else {
      common_vendor.index.__f__("warn", "at main.js:105", "[main.js] uni 对象不可用，跳过同步管理器初始化");
    }
  }, 100);
  return {
    app
  };
}
if (typeof common_vendor.nr !== "undefined") {
  try {
    common_vendor.index.__f__("log", "at main.js:119", "[main.js] uniCloud 对象存在，开始初始化...");
    const spaces = common_vendor.nr.getSpaces && common_vendor.nr.getSpaces();
    common_vendor.index.__f__("log", "at main.js:123", "[main.js] 服务空间配置:", spaces);
    if (spaces && spaces.length > 1) {
      common_vendor.index.__f__("log", "at main.js:128", "[main.js] 检测到多个服务空间，使用第一个:", spaces[0]);
      common_vendor.nr.init({
        provider: spaces[0].provider,
        spaceId: spaces[0].spaceId,
        clientSecret: spaces[0].clientSecret
      });
    } else if (spaces && spaces.length === 1) {
      common_vendor.index.__f__("log", "at main.js:135", "[main.js] 检测到单个服务空间，自动使用:", spaces[0]);
    } else {
      common_vendor.index.__f__("warn", "at main.js:137", "[main.js] 未检测到服务空间配置");
    }
    common_vendor.index.__f__("log", "at main.js:140", "[main.js] ✅ uniCloud initialized successfully");
  } catch (error) {
    common_vendor.index.__f__("error", "at main.js:142", "[main.js] ❌ uniCloud initialization failed:", error);
  }
} else {
  common_vendor.index.__f__("error", "at main.js:145", "[main.js] ❌ uniCloud 对象不存在");
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
