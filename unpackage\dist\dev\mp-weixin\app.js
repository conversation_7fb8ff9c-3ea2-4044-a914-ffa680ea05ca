"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const store_user = require("./store/user.js");
const store_wish = require("./store/wish.js");
const store_group = require("./store/group.js");
const store_message = require("./store/message.js");
const store_comment = require("./store/comment.js");
const services_authService = require("./services/authService.js");
const utils_syncManager = require("./utils/syncManager.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/login/login.js";
  "./pages/message/message.js";
  "./pages/profile/profile.js";
  "./subpkg-profile/pages/userEdit/userEdit.js";
  "./subpkg-profile/pages/addressManage/addressManage.js";
  "./subpkg-profile/pages/addressEdit/addressEdit.js";
  "./subpkg-profile/pages/historyWish/historyWish.js";
  "./subpkg-profile/pages/about/about.js";
  "./subpkg-wish/pages/wishDetail/wishDetail.js";
  "./subpkg-wish/pages/editWish/editWish.js";
  "./subpkg-wish/pages/groupManage/groupManage.js";
  "./subpkg-wish/pages/groupSort/groupSort.js";
}
const _sfc_main = {
  async onLaunch() {
    common_vendor.index.__f__("log", "at App.vue:12", "App Launch");
    services_authService.authService.setupRequestInterceptor();
    const userStore = store_user.useUserStore();
    store_wish.useWishStore();
    store_group.useGroupStore();
    store_message.useMessageStore();
    store_comment.initCommentStore();
    await userStore.loadUserFromStorage();
    common_vendor.index.__f__("log", "at App.vue:30", "[App.vue onLaunch] Initial login status after server verification: ", userStore.isLogin);
    if (userStore.isLogin) {
      try {
        common_vendor.index.__f__("log", "at App.vue:35", "[App.vue onLaunch] 🚀 Initializing uni-push 2.0 sync system...");
        await utils_syncManager.syncManager.init();
        common_vendor.index.__f__("log", "at App.vue:39", "[App.vue onLaunch] ✅ uni-push 2.0 sync system initialized successfully");
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:42", "[App.vue onLaunch] ❌ uni-push 2.0 sync system initialization failed:", error);
      }
    }
    common_vendor.index.__f__("log", "at App.vue:47", "[App.vue] 设置 uni-push 消息监听");
    common_vendor.index.onPushMessage((res) => {
      common_vendor.index.__f__("log", "at App.vue:49", "[App.vue] 收到推送消息:", res);
      if (utils_syncManager.syncManager && utils_syncManager.syncManager.handlePushMessage) {
        utils_syncManager.syncManager.handlePushMessage(res);
      }
    });
    Promise.resolve().then(async () => {
      try {
        common_vendor.index.__f__("log", "at App.vue:59", "[App.vue onLaunch] Initializing database collections in background...");
        const commentCenter = common_vendor.nr.importObject("comment-center");
        const initResult = await commentCenter.ensureCollectionsExist();
        common_vendor.index.__f__("log", "at App.vue:62", "[App.vue onLaunch] Database collections initialized:", initResult);
      } catch (error) {
        common_vendor.index.__f__("warn", "at App.vue:64", "[App.vue onLaunch] Database initialization failed (non-critical):", error);
      }
    });
    common_vendor.index.__f__("log", "at App.vue:70", "[App.vue onLaunch] User login status checked. Store initialization will be handled by pages or loginSuccess.");
    const strictlyAuthRequiredPages = [
      "/pages/profile/profile",
      // 用户中心主页
      "/pages/message/message",
      // 消息中心
      "/subpkg-profile/pages/addressManage/addressManage",
      // 地址管理
      "/subpkg-profile/pages/addressEdit/addressEdit",
      // 地址编辑
      "/subpkg-profile/pages/historyWish/historyWish"
      // 历史心愿
      // 添加其他必须先登录才能进入的页面
    ];
    const handleNavigation = (args, navigationType) => {
      const targetUrl = args.url.split("?")[0];
      let absoluteTargetUrl = targetUrl.startsWith("/") ? targetUrl : `/${targetUrl}`;
      if (navigationType === "switchTab") {
        absoluteTargetUrl = args.url;
      }
      common_vendor.index.__f__("log", "at App.vue:91", `[App NavInterceptor (${navigationType})] Attempting to navigate to: ${absoluteTargetUrl}`);
      if (strictlyAuthRequiredPages.includes(absoluteTargetUrl)) {
        if (!userStore.checkLoginStatus()) {
          common_vendor.index.__f__("log", "at App.vue:101", `[App NavInterceptor (${navigationType})] Strictly required page ${absoluteTargetUrl} and user not logged in. Redirecting.`);
          common_vendor.index.navigateTo({
            url: "/pages/login/login?redirect=" + encodeURIComponent(args.url)
          });
          return false;
        }
      }
      return true;
    };
    common_vendor.index.addInterceptor("navigateTo", {
      invoke(args) {
        return handleNavigation(args, "navigateTo");
      },
      fail(err) {
        common_vendor.index.__f__("error", "at App.vue:120", "[Interceptor navigateTo fail]:", err.errMsg);
      }
    });
    common_vendor.index.addInterceptor("redirectTo", {
      invoke(args) {
        return handleNavigation(args, "redirectTo");
      },
      fail(err) {
        common_vendor.index.__f__("error", "at App.vue:129", "[Interceptor redirectTo fail]:", err.errMsg);
      }
    });
    common_vendor.index.addInterceptor("reLaunch", {
      invoke(args) {
        return handleNavigation(args, "reLaunch");
      },
      fail(err) {
        common_vendor.index.__f__("error", "at App.vue:138", "[Interceptor reLaunch fail]:", err.errMsg);
      }
    });
    common_vendor.index.addInterceptor("switchTab", {
      async invoke(args) {
        store_user.useUserStore();
        return handleNavigation(args, "switchTab");
      },
      fail(err) {
        common_vendor.index.__f__("error", "at App.vue:151", "[Interceptor switchTab fail]:", err.errMsg);
      }
    });
    common_vendor.index.__f__("log", "at App.vue:155", "[App.vue onLaunch] App launch sequence finished.");
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:160", "App Show");
    if (utils_syncManager.syncManager && utils_syncManager.syncManager.onAppShow) {
      utils_syncManager.syncManager.onAppShow();
    }
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:167", "App Hide");
    if (utils_syncManager.syncManager && utils_syncManager.syncManager.onAppHide) {
      utils_syncManager.syncManager.onAppHide();
    }
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  const pinia = common_vendor.createPinia();
  app.use(pinia);
  setTimeout(async () => {
    if (typeof common_vendor.index !== "undefined") {
      try {
        await utils_syncManager.syncManager.init();
        common_vendor.index.__f__("log", "at main.js:32", "[main.js] 🚀 实时推送同步系统启动成功");
      } catch (error) {
        common_vendor.index.__f__("error", "at main.js:34", "[main.js] 同步管理器初始化失败:", error);
      }
    } else {
      common_vendor.index.__f__("warn", "at main.js:37", "[main.js] uni 对象不可用，跳过同步管理器初始化");
    }
  }, 100);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
