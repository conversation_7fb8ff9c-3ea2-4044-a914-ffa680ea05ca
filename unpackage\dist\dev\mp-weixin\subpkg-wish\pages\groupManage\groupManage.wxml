<view class="group-manage-container"><view class="group-list"><view class="section-title"><text>分组管理</text><view class="add-btn" bindtap="{{b}}"><uni-icons wx:if="{{a}}" u-i="63c2859b-0" bind:__l="__l" u-p="{{a}}"></uni-icons><text class="add-text">添加分组</text></view></view><view wx:if="{{c}}" class="empty-tip"><text>暂无分组，点击上方按钮添加</text></view><view wx:else class="group-items"><view wx:for="{{d}}" wx:for-item="group" wx:key="k" class="group-item card"><view class="group-info"><view class="group-name">{{group.a}}</view><view wx:if="{{group.b}}" class="default-tag">默认</view></view><view class="group-actions"><view class="{{['action-btn', group.e && 'disabled']}}" bindtap="{{group.f}}"><uni-icons wx:if="{{group.d}}" u-i="{{group.c}}" bind:__l="__l" u-p="{{group.d}}"></uni-icons></view><view class="{{['action-btn', 'delete', group.i && 'disabled']}}" bindtap="{{group.j}}"><uni-icons wx:if="{{group.h}}" u-i="{{group.g}}" bind:__l="__l" u-p="{{group.h}}"></uni-icons></view></view></view></view></view><uni-popup wx:if="{{i}}" class="r" u-s="{{['d']}}" u-r="addGroupPopup" u-i="63c2859b-3" bind:__l="__l" u-p="{{i}}"><uni-popup-dialog wx:if="{{g}}" bindconfirm="{{e}}" bindclose="{{f}}" u-i="63c2859b-4,63c2859b-3" bind:__l="__l" u-p="{{g}}"></uni-popup-dialog></uni-popup><uni-popup wx:if="{{n}}" class="r" u-s="{{['d']}}" u-r="editGroupPopup" u-i="63c2859b-5" bind:__l="__l" u-p="{{n}}"><uni-popup-dialog wx:if="{{l}}" bindconfirm="{{j}}" bindclose="{{k}}" u-i="63c2859b-6,63c2859b-5" bind:__l="__l" u-p="{{l}}"></uni-popup-dialog></uni-popup><uni-popup wx:if="{{s}}" class="r" u-s="{{['d']}}" u-r="deletePopup" u-i="63c2859b-7" bind:__l="__l" u-p="{{s}}"><uni-popup-dialog wx:if="{{q}}" bindconfirm="{{o}}" bindclose="{{p}}" u-i="63c2859b-8,63c2859b-7" bind:__l="__l" u-p="{{q}}"></uni-popup-dialog></uni-popup></view>