/**
 * 分组数据管理 Store
 * 
 * 重要说明：已彻底移除所有自动同步功能
 * - 不再有定时同步器
 * - 网络恢复时不会自动同步
 * - 所有同步都需要手动触发
 * 
 * 手动同步方法：
 * - manualSync(): 完整同步（用户主动调用）
 * - syncPendingData(): 同步待上传数据
 * 
 * 同步状态检查：
 * - hasPendingSync: 是否有待同步数据
 * - pendingSyncCount: 待同步数量
 * - pendingSyncGroups: 待同步分组列表
 */

import { defineStore } from 'pinia'
import { useUserStore } from './user.js' // Import user store
import { devLog } from '@/utils/envUtils.js'

export const useGroupStore = defineStore('group', {
  state: () => ({
    groups: [],
    isLoading: false,
    lastSyncTime: null,
    isOnline: true, // 网络状态

    // 🚀 增强同步状态管理
    _syncOperations: new Set(), // 正在进行的同步操作
    _lastSyncTime: 0 // 上次同步时间
  }),
  
  getters: {
    // 获取所有分组
    getAllGroups: (state) => state.groups,
    
    // 根据ID获取分组
    getGroupById: (state) => (id) => {
      return state.groups.find(group => group.id === id) || null
    },
    
    // 检查是否有待同步的数据
    hasPendingSync: (state) => {
      return state.groups.some(group => group._needSync)
    },
    
    // 获取待同步的分组数量
    pendingSyncCount: (state) => {
      return state.groups.filter(group => group._needSync).length
    },
    
    // 获取待同步的分组列表
    pendingSyncGroups: (state) => {
      return state.groups.filter(group => group._needSync)
    }
  },
  
  actions: {
    // 初始化分组数据
    async initGroups() {
      // 尝试从本地存储加载
      const storedGroups = uni.getStorageSync('groups')
      if (storedGroups) {
        const parsed = JSON.parse(storedGroups)
        // 确保每个分组对象都有 id 字段
        this.groups = parsed.map(group => ({
          ...group,
          id: group.id || group._id // 如果没有 id 字段，使用 _id
        }))
      }
      
      // 初始化网络监听
      this.initNetworkMonitor()
      
      // 尝试从云端同步数据
      await this.syncFromCloud()
      
      // 确保默认分组存在
      this.ensureDefaultGroups()
    },
    
    // 初始化网络监听
    initNetworkMonitor() {
      // 检查当前网络状态
      this.checkNetworkStatus()
      
      // 监听网络状态变化
      uni.onNetworkStatusChange((res) => {
        const wasOnline = this.isOnline
        this.isOnline = res.isConnected
        
        devLog.log(`分组Store - 网络状态变化: ${wasOnline ? '在线' : '离线'} -> ${this.isOnline ? '在线' : '离线'}`)
        
        // 网络恢复时只记录状态，不自动同步
        if (!wasOnline && this.isOnline) {
          devLog.log('分组Store - 网络恢复，但不启用自动同步')
        }
      })
    },
    
    // 检查网络状态
    async checkNetworkStatus() {
      try {
        const networkType = await uni.getNetworkType()
        this.isOnline = networkType.networkType !== 'none'
        devLog.log('分组Store - 当前网络状态:', this.isOnline ? '在线' : '离线')
      } catch (error) {
        devLog.error('分组Store - 检查网络状态失败:', error)
        this.isOnline = false
      }
    },

    // 🚀 新增：检查同步操作是否可以执行
    _canStartSync(operation) {
      const now = Date.now()

      // 检查是否有相同操作正在进行
      if (this._syncOperations.has(operation)) {
        devLog.log(`[groupStore] 同步操作 ${operation} 已在进行中`)
        return false
      }

      // 检查是否距离上次同步太近（防抖）
      if (now - this._lastSyncTime < 1000) {
        devLog.log(`[groupStore] 同步操作过于频繁，跳过`)
        return false
      }

      return true
    },

    // 🚀 新增：开始同步操作
    _startSyncOperation(operation) {
      this._syncOperations.add(operation)
      this._lastSyncTime = Date.now()
      devLog.log(`[groupStore] 开始同步操作: ${operation}`)
    },

    // 🚀 新增：结束同步操作
    _endSyncOperation(operation) {
      this._syncOperations.delete(operation)
      devLog.log(`[groupStore] 结束同步操作: ${operation}`)
    },
    

    
    // 从云端同步数据 - 🚀 增强防重复机制
    async syncFromCloud() {
      const operation = 'syncFromCloud'

      // 🚀 检查是否可以开始同步
      if (!this._canStartSync(operation)) {
        return
      }

      const userStore = useUserStore() // Get user store instance
      if (!userStore.isLogin) {
        devLog.log('[store/group.js syncFromCloud] User not logged in, skipping cloud sync.');
        // 如果未登录，可以选择是否加载默认数据或清空列表
        if (this.groups.length === 0) {
            // this._initDefaultGroups(); // 根据产品需求决定
        }
        return; // Do not proceed if not logged in
      }

      try {
        this._startSyncOperation(operation)
        this.isLoading = true
        devLog.log('[groupStore] Starting intelligent group sync...');
        
        // 第一步：轻量级同步检查
        const syncNeeded = await this._checkGroupSyncNeeded();
        
        if (!syncNeeded) {
          devLog.log('[groupStore] Local group data is up to date, no sync needed');
          // 确保默认分组存在
          this.ensureDefaultGroups()
          return;
        }
        
        // 第二步：如果需要同步，获取完整数据并进行智能合并
        await this._performIntelligentGroupSync();
        
      } catch (error) {
        devLog.error('[groupStore] 智能分组同步失败:', error)
        // 如果云端同步失败，确保至少有默认分组
        devLog.log('[groupStore] Initializing default groups due to sync failure...');
          this._initDefaultGroups()
      } finally {
        this.isLoading = false
        this._endSyncOperation(operation)

        // 最终安全检查：确保至少有默认分组
        if (!this.groups || this.groups.length === 0) {
          devLog.log('[groupStore] Final safety check: no groups found, initializing defaults');
          this._initDefaultGroups();
        }
      }
    },
    
    // 检查分组是否需要同步（轻量级调用）
    async _checkGroupSyncNeeded() {
      // 临时特性开关：如果云端API未准备好，直接返回需要同步
      const enableLightweightCheck = false; // TODO: 等云端API就绪后设为true
      
      if (!enableLightweightCheck) {
        devLog.log('[groupStore] Lightweight group sync check disabled, performing full sync');
        return true;
      }
      
      try {
        const groupCenter = uniCloud.importObject('group-center')
        
        // 调用轻量级API，只获取同步摘要信息
        const result = await groupCenter.getGroupSyncSummary({
          lastSyncTime: this.lastSyncTime,
          localDataCount: this.groups.filter(g => !g.isDefault).length, // 只计算非默认分组
          localLastModified: this._getLocalGroupLastModified()
        })
        
        if (result.errCode === 0) {
          const { needFullSync, cloudLastModified, cloudDataCount } = result.data;
          
          // 比较时间戳和数据量来判断是否需要同步
          const localLastModified = this._getLocalGroupLastModified();
          const localNonDefaultCount = this.groups.filter(g => !g.isDefault).length;
          const needSync = needFullSync || 
                          !localLastModified || 
                          !cloudLastModified ||
                          new Date(cloudLastModified) > new Date(localLastModified) ||
                          cloudDataCount !== localNonDefaultCount;
          
          devLog.log('[groupStore] Group sync check result:', {
            needSync,
            localLastModified,
            cloudLastModified,
            localNonDefaultCount,
            cloudCount: cloudDataCount
          });
          
          return needSync;
        } else {
          // 如果轻量级检查失败，默认进行同步
          devLog.warn('[groupStore] Group sync check failed, defaulting to sync');
          return true;
        }
      } catch (error) {
        devLog.error('[groupStore] Group sync check error:', error);
        // 网络错误时，不进行同步
        if (this._isNetworkError(error)) {
          return false;
        }
        // 其他错误时，进行同步
        return true;
      }
    },
    
    // 获取本地分组数据的最后修改时间
    _getLocalGroupLastModified() {
      const nonDefaultGroups = this.groups.filter(group => !group.isDefault);
      if (nonDefaultGroups.length === 0) return null;
      
      const latestGroup = nonDefaultGroups.reduce((latest, current) => {
        const currentTime = new Date(current.updateDate || current.createDate || 0);
        const latestTime = new Date(latest.updateDate || latest.createDate || 0);
        return currentTime > latestTime ? current : latest;
      });
      
      return latestGroup.updateDate || latestGroup.createDate;
    },
    
    // 执行智能分组同步（合并数据而不是覆盖）
    async _performIntelligentGroupSync() {
      devLog.log('[groupStore] Performing intelligent group data merge...');
      
      const groupCenter = uniCloud.importObject('group-center')
      const result = await groupCenter.getGroupList({
        includeDeleted: true // 包含已删除的数据用于冲突解决
      })
      
      if (result.errCode === 0) {
        const cloudGroups = result.data || [];
        devLog.log('[groupStore] Cloud group data received:', cloudGroups.length, 'items');
        
        // 执行智能数据合并
        const mergedData = this._mergeGroupData(this.groups, cloudGroups);
        
        // 同步前数据状态记录
        devLog.log('[groupStore] 开始数据合并:', {
          localCount: this.groups.length,
          cloudCount: cloudGroups.length
        });
        
        // 安全更新本地数据，避免DOM错误
        try {
          // 使用 nextTick 和温和的更新策略
          await new Promise((resolve) => {
            // 如果支持nextTick，使用它
            if (this.$nextTick) {
              this.$nextTick(() => {
        this.groups = mergedData;
                resolve();
              });
            } else {
              // 降级：延迟更新
              setTimeout(() => {
                this.groups = mergedData;
                resolve();
              }, 0);
            }
          });
        
        // 同步后数据状态记录
          devLog.log('[groupStore] 数据合并完成:', {
          mergedCount: mergedData.length
        });
        
        this.lastSyncTime = new Date().toISOString()
        this._saveToStorage()
        
        // 确保默认分组存在
        this.ensureDefaultGroups()
        
          devLog.log('[groupStore] Intelligent group sync completed, merged data:', this.groups.length, 'items');
        } catch (error) {
          devLog.error('[groupStore] Error updating groups data:', error);
          // 降级：直接更新
          this.groups = mergedData;
          this.lastSyncTime = new Date().toISOString()
          this._saveToStorage()
          this.ensureDefaultGroups()
        }
      } else {
        throw new Error(result.errMsg || '获取云端分组数据失败');
      }
    },
    
    // 智能合并本地和云端分组数据
    _mergeGroupData(localGroups, cloudGroups) {
      devLog.log('[groupStore] Merging local and cloud group data...');
      
      // 安全处理空数据
      const safeLocalGroups = Array.isArray(localGroups) ? localGroups : [];
      const safeCloudGroups = Array.isArray(cloudGroups) ? cloudGroups : [];
      
      devLog.log('[groupStore] Local groups before merge:', safeLocalGroups.map(g => ({ id: g.id, name: g.name, isDefault: g.isDefault, order: g.order })));
      devLog.log('[groupStore] Cloud groups before merge:', safeCloudGroups.map(g => ({ id: g.id, name: g.name, isDefault: g.isDefault, order: g.order })));
      
      // 如果两边都是空数据，直接返回空数组，不触发响应式更新
      if (safeLocalGroups.length === 0 && safeCloudGroups.length === 0) {
        devLog.log('[groupStore] Both local and cloud groups are empty, returning empty array');
        return [];
      }
      
      const mergedMap = new Map();
      
      // 首先保留所有默认分组（本地优先）
      const defaultGroups = safeLocalGroups.filter(group => group.isDefault);
      defaultGroups.forEach(group => {
        if (group.id) { // 确保有有效的ID
        mergedMap.set(group.id, { 
          ...group,
          _source: 'local-default'
        });
        }
      });
      
      // 处理云端非默认数据（确保id字段映射）
      safeCloudGroups.forEach(cloudGroup => {
        if (!cloudGroup.isDefault && cloudGroup.id) {
          // 确保云端数据有id字段
          const groupWithId = {
            ...cloudGroup,
            id: cloudGroup.id || cloudGroup._id, // 确保id字段存在
            _source: 'cloud'
          };
          mergedMap.set(groupWithId.id, groupWithId);
        }
      });
      
      // 处理本地非默认数据，确保不丢失
      const localNonDefaultGroups = safeLocalGroups.filter(group => !group.isDefault);
      localNonDefaultGroups.forEach(localGroup => {
        const id = localGroup.id;
        const existingGroup = mergedMap.get(id);
        
        if (!existingGroup) {
          // 本地独有的数据（可能是新创建的或离线创建的）
          devLog.log(`[groupStore] Preserving local-only group: ${localGroup.name}`);
          mergedMap.set(id, {
            ...localGroup,
            _source: 'local-only',
            _needSync: true // 标记需要同步到云端
          });
        } else {
          // 存在冲突，比较时间戳
          const localTime = new Date(localGroup.updateDate || localGroup.createDate || 0);
          const cloudTime = new Date(existingGroup.updateDate || existingGroup.createDate || 0);
          
          if (localTime > cloudTime) {
            // 本地数据更新，使用本地数据
            devLog.log(`[groupStore] Local group data newer for ${localGroup.name}, using local version`);
            mergedMap.set(id, {
              ...localGroup,
              _source: 'local-newer',
              _needSync: true // 需要同步到云端
            });
          } else if (localTime < cloudTime) {
            // 云端数据更新，使用云端数据
            devLog.log(`[groupStore] Cloud group data newer for ${existingGroup.name}, using cloud version`);
            // 保持云端数据，无需额外处理
          } else {
            // 时间戳相同，检查内容是否有差异
            if (this._hasGroupContentDifference(localGroup, existingGroup)) {
              devLog.log(`[groupStore] Group content difference detected for ${localGroup.name}, preferring cloud data`);
              // 内容有差异但时间戳相同，优先使用云端数据
            } else {
              // 时间戳相同且内容相同，保持云端数据（避免_needSync标记）
              devLog.log(`[groupStore] Groups are identical for ${localGroup.name}, keeping cloud version`);
            }
          }
        }
      });
      
      // 转换为数组并过滤已删除的项目
      let mergedArray = Array.from(mergedMap.values())
        .filter(group => !group._deleted);
      
      // 修复排序：确保新添加的分组有正确的order值
      mergedArray = this._fixGroupOrder(mergedArray);
      
      devLog.log('[groupStore] Group data merge completed:', {
        localCount: safeLocalGroups.length,
        cloudCount: safeCloudGroups.length,
        mergedCount: mergedArray.length
      });
      devLog.log('[groupStore] Merged groups:', mergedArray.map(g => ({ id: g.id, name: g.name, isDefault: g.isDefault, order: g.order, source: g._source })));
      
      return mergedArray;
    },
    
    // 修复分组排序
    _fixGroupOrder(groups) {
      // 为没有order的分组分配order值
      let maxOrder = Math.max(0, ...groups.filter(g => g.order !== undefined).map(g => g.order));
      
      groups.forEach(group => {
        if (group.order === undefined || group.order === null) {
          maxOrder += 1;
          group.order = maxOrder;
          devLog.log(`[groupStore] Assigned order ${maxOrder} to group: ${group.name}`);
        }
      });
      
      // 按order排序
      return groups.sort((a, b) => (a.order || 0) - (b.order || 0));
    },
    
    // 检查两个分组对象是否有内容差异
    _hasGroupContentDifference(group1, group2) {
      const keys = ['name', 'icon', 'color', 'description'];
      
      return keys.some(key => {
        return group1[key] !== group2[key];
      });
    },
    
    // 检查是否为网络错误
    _isNetworkError(error) {
      if (error.code === 'NETWORK_ERROR') return true;
      if (error.message && (
        error.message.includes('网络') || 
        error.message.includes('network') ||
        error.message.includes('timeout')
      )) return true;
      
      return false;
    },
    
    // 初始化默认分组
    _initDefaultGroups() {
      const defaultGroups = [
        {
          id: 'all',
          name: '全部',
          isDefault: true,
          order: 0
        },
        {
          id: 'gift',
          name: '礼物',
          isDefault: true,
          order: 1
        },
        {
          id: 'friend-visible',
          name: '朋友可见',
          isDefault: true,
          order: 2
        }
      ];
      
      // 安全地更新数组，避免DOM错误
      try {
        // 清空当前数组
        this.groups.splice(0, this.groups.length);
        // 添加新元素
        this.groups.push(...defaultGroups);
      } catch (error) {
        devLog.error('[groupStore] Error updating default groups safely, falling back to direct assignment:', error);
        // 降级：直接赋值
        this.groups = defaultGroups;
      }
      
      this._saveToStorage()
    },
    
    // 确保默认分组存在
    ensureDefaultGroups() {
      // 检查"全部"分组是否存在
      const allGroupExists = this.groups.some(group => group.id === 'all')
      if (!allGroupExists) {
        this.groups.push({
          id: 'all',
          name: '全部',
          isDefault: true,
          order: 0 // 确保始终在第一位
        })
      } else {
        // 确保"全部"分组始终order为0
        const allGroupIndex = this.groups.findIndex(group => group.id === 'all')
        if (allGroupIndex > -1) {
          this.groups[allGroupIndex].order = 0
        }
      }
      
      // 检查"礼物"分组是否存在
      const giftGroupExists = this.groups.some(group => group.id === 'gift')
      if (!giftGroupExists) {
        this.groups.push({
          id: 'gift',
          name: '礼物',
          isDefault: true,
          order: 1
        })
      }
      
      // 检查"朋友可见"分组是否存在
      const friendVisibleGroupExists = this.groups.some(group => group.id === 'friend-visible')
      if (!friendVisibleGroupExists) {
        this.groups.push({
          id: 'friend-visible',
          name: '朋友可见',
          isDefault: true,
          order: 2
        })
      }
      
      // 保存到本地存储
      this._saveToStorage()
    },
    
    // 添加新分组
    async addGroup(name) {
      const userStore = useUserStore(); // Get user store instance
      
      // 直接检查登录状态，不需要重新加载
      if (!userStore.isLogin) {
        devLog.error('[store/group.js addGroup] Critical: User not logged in before attempting to create group.');
        uni.showToast({
          title: '请先登录以创建分组',
          icon: 'none',
          duration: 3000
        });
        // Attempt to redirect to login. Note: This might be hard if called from a non-page context.
        // Consider if a global event or a different redirect mechanism is needed if this store action
        // can be called from contexts where uni.navigateTo isn't appropriate.
        // For now, assuming it's triggered from a page context that can handle this.
        /* No, let the page handler do the redirect. This store should just fail.
        uni.navigateTo({
            url: '/pages/login/login?redirect=' + encodeURIComponent(getCurrentPages().pop().route) // This is risky from a store
        });
        */
        return null; // Signal failure
      }

      if (!name || typeof name !== 'string') {
        devLog.error('分组名称不合法');
        uni.showToast({ title: '分组名称不合法', icon: 'none' });
        return null;
      }
      
      // 前端预检查，避免不必要的云函数调用
      if (this._checkGroupNameExists(name)) {
        devLog.warn('前端检查：分组名称已存在 - ', name);
        uni.showToast({ title: '分组名称已存在', icon: 'none' });
        return null;
      }
      
      try {
        // 添加时间戳
        const timestamp = Date.now();

        // 先调用云函数创建
        const groupCenter = uniCloud.importObject('group-center');
        const result = await groupCenter.createGroup({
          name: name,
          icon: '',
          color: '#8a2be2',
          // 时间戳支持
          timestamp,
          deviceId: uni.getSystemInfoSync().deviceId || 'unknown'
        });
        
        if (result.errCode === 0) {
          // 云端创建成功，确保新分组有正确的排序位置
          // 计算最大order值，但要考虑默认分组的排序
          const nonDefaultGroups = this.groups.filter(g => !g.isDefault);
          const defaultGroupsMaxOrder = 2; // 默认分组最大order是2（朋友可见）
          const maxOrder = Math.max(defaultGroupsMaxOrder, ...nonDefaultGroups.map(g => g.order || 0));
          
          const newGroup = {
            ...result.data,
            id: result.data.id || result.data._id, // 确保id字段存在
            order: result.data.order || maxOrder + 1 // 确保新分组排在所有分组之后
          };
          
          // 添加到本地列表
          this.groups.push(newGroup);
          
          // 重新排序以确保顺序正确
          this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));
          
          // 立即保存到本地存储，确保新分组被保存
          this._saveToStorage();
          
          // 分组添加成功记录
          devLog.log('[groupStore] 分组添加成功:', {
            groupName: newGroup.name,
            groupId: result.data.id,
            groupOrder: newGroup.order
          });
          
          // 触发响应式更新
          this._triggerReactiveUpdate();
          
          // 数据已保存到本地，等待网络恢复后自动同步

          // 数据变化通知已通过云函数推送处理
          
          uni.showToast({ title: '分组创建成功', icon: 'success' });
          
          devLog.log('[groupStore] New group added:', newGroup.name, 'with order:', newGroup.order);
          devLog.log('[groupStore] Current groups after adding:', this.groups.map(g => ({ id: g.id, name: g.name, order: g.order })));
          
          return result.data.id;
        } else {
          // 云函数返回了业务错误 (例如：名称已存在，权限不足等)
          devLog.error('云函数创建分组失败:', result.errMsg, '(errCode:', result.errCode, ')');
          uni.showToast({
            title: result.errMsg || '创建分组失败',
            icon: 'none',
            duration: 3000
          });
          return null; //明确告知创建失败
        }
      } catch (error) {
        // 这个 catch 主要处理网络错误或 uniCloud.importObject/调用 本身抛出的JS错误
        devLog.error('调用云函数添加分组时发生异常:', error);
        
        const isNetworkIssue = error.message && (error.message.includes('network') || error.message.includes('超时'));

        if (isNetworkIssue) {
          devLog.log('网络问题，尝试离线保存分组:', name);
        const id = 'group_' + Date.now();
        // 计算最大order值，但要考虑默认分组的排序
        const nonDefaultGroups = this.groups.filter(g => !g.isDefault);
        const defaultGroupsMaxOrder = 2; // 默认分组最大order是2（朋友可见）
        const maxOrder = Math.max(defaultGroupsMaxOrder, ...nonDefaultGroups.map(g => g.order || 0));
        
        const newGroup = {
          id,
          name,
          isDefault: false,
          order: maxOrder + 1,
            _needSync: true
        };
        this.groups.push(newGroup);
        
        // 立即保存到本地存储
        this._saveToStorage();
        
        // 触发响应式更新
        this._triggerReactiveUpdate();
        
        // 数据已标记为需要同步，等待网络恢复后自动同步
        
        uni.showToast({
            title: '网络似乎有问题，已保存到本地',
          icon: 'none'
          });
          
        devLog.log('[groupStore] Offline group added:', newGroup.name, 'with order:', newGroup.order);
        devLog.log('[groupStore] Current groups after offline adding:', this.groups.map(g => ({ id: g.id, name: g.name, order: g.order })));
        
        return id;
        } else {
          uni.showToast({
            title: '创建分组时发生未知错误',
            icon: 'none'
          });
          return null;
        }
      }
    },
    
    // 更新分组名称
    async updateGroup(id, name) {
      const group = this.groups.find(g => g.id === id)
      if (group && !group.isDefault) {
        // 检查是否重复（忽略大小写，排除当前分组）
        if (this._checkGroupNameExists(name, id)) {
          devLog.warn('[groupStore] updateGroup: 分组名称已存在 -', name);
          uni.showToast({
            title: '分组名称已存在',
            icon: 'none'
          });
          return;
        }
        
        try {
          // 先更新云端
          const groupCenter = uniCloud.importObject('group-center')
          const result = await groupCenter.updateGroup(id, { name })
          
          if (result.errCode === 0) {
            // 云端更新成功，更新本地数据
            group.name = name
            this._saveToStorage()
            this._triggerReactiveUpdate()
            
            // 数据已保存到本地，等待网络恢复后自动同步
            
                    // 数据变化通知已通过云函数推送处理
          } else {
            throw new Error(result.errMsg || '更新分组失败')
          }
        } catch (error) {
          devLog.error('更新分组失败:', error)
          
          // 云端失败时，先更新本地
          group.name = name
          group._needSync = true
          this._saveToStorage()
          this._triggerReactiveUpdate()
          
          // 数据已标记为需要同步，等待网络恢复后自动同步
          
          uni.showToast({
            title: '已保存到本地，稍后将同步到云端',
            icon: 'none'
          })
        }
      }
    },
    
    // 删除分组（优化版本：先本地删除，后台同步）
    // 
    // 优化后的删除流程：
    // 1. 立即删除本地数据，提供即时响应（用户体验好）
    // 2. 后台异步删除云端数据，不阻塞用户操作
    // 3. 避免调用loadUserFromStorage()造成的重复登录验证
    // 4. 避免触发完整的数据重新同步
    async deleteGroup(id) {
      // 验证参数
      if (!id) {
        devLog.error('[groupStore] deleteGroup: 分组ID不能为空');
        uni.showToast({
          title: '分组ID不能为空',
          icon: 'none'
        });
        return false;
      }
      
      devLog.log('[groupStore] deleteGroup called with id:', id);
      
      // 不能删除默认分组
      const group = this.groups.find(g => g.id === id)
      if (!group) {
        devLog.error('[groupStore] deleteGroup: 找不到指定的分组, id:', id);
        uni.showToast({
          title: '找不到指定的分组',
          icon: 'none'
        });
        return false;
      }
      
      if (group.isDefault) {
        devLog.error('[groupStore] deleteGroup: 不能删除默认分组, id:', id);
        uni.showToast({
          title: '不能删除默认分组',
          icon: 'none'
        });
        return false;
      }
      
      if (group && !group.isDefault) {
        // 立即删除本地数据，提供即时响应
        this.groups = this.groups.filter(g => g.id !== id)
        
        // 触发响应式更新
        this._triggerReactiveUpdate()
        
        this._saveToStorage()
        
        devLog.log('[groupStore] deleteGroup: 本地删除成功, id:', id);
        
        // 后台异步删除云端数据，不阻塞用户操作
        this._deleteGroupFromCloud(id);
        
        return true;
      }
      
      devLog.log('[groupStore] deleteGroup: 不符合删除条件, id:', id);
      return false;
    },
    
    // 后台删除云端分组数据
    async _deleteGroupFromCloud(id) {
      try {
        devLog.log('[groupStore] 后台删除云端分组, id:', id);
        
        const groupCenter = uniCloud.importObject('group-center')
        const result = await groupCenter.deleteGroup(id)
        
        if (result.errCode === 0) {
          devLog.log('[groupStore] 云端分组删除成功, id:', id);
          
                  // 数据变化通知已通过云函数推送处理
        } else {
          devLog.warn('[groupStore] 云端分组删除失败, id:', id, 'error:', result.errMsg);
          // 云端删除失败，但本地已删除，用户已得到即时响应
          // 可以在后续同步时处理这种不一致情况
        }
      } catch (error) {
        devLog.error('[groupStore] 后台删除云端分组失败, id:', id, 'error:', error);
        // 静默处理错误，不影响用户体验
        // 删除操作从用户角度看已经成功完成
      }
    },
    
    // 更新分组顺序
    async updateGroupOrder(id, order) {
      // 查找目标分组
      const groupIndex = this.groups.findIndex(group => group.id === id)
      
      if (groupIndex !== -1) {
        try {
          // 先更新云端
          const groupCenter = uniCloud.importObject('group-center')
          const result = await groupCenter.updateGroup(id, { order })
          
          if (result.errCode === 0) {
            // 云端更新成功，更新本地数据
            this.groups[groupIndex] = {
              ...this.groups[groupIndex],
              order
            }
            this._saveToStorage()
            
                    // 数据变化通知已通过云函数推送处理
            
            return true
          } else {
            throw new Error(result.errMsg || '更新排序失败')
          }
        } catch (error) {
          devLog.error('更新分组排序失败:', error)
          
          // 云端失败时，先更新本地
          this.groups[groupIndex] = {
            ...this.groups[groupIndex],
            order,
            _needSync: true
          }
          this._saveToStorage()
          return true
        }
      }
      
      return false
    },
    
    // 重置分组数据
    resetGroups() {
      // 清除本地存储
      uni.removeStorageSync('groups')
      
      // 重新初始化默认分组
      this._initDefaultGroups()
      
      // 返回通知
      return {
        success: true,
        message: '分组数据已重置'
      }
    },
    
    // 手动同步待上传的本地数据到云端（仅在用户主动操作时调用）
    async syncPendingData() {
      const pendingGroups = this.groups.filter(group => group._needSync)
      
      if (pendingGroups.length === 0) {
        devLog.log('[groupStore] No pending groups to sync');
        return;
      }
      
      devLog.log(`[groupStore] Manual sync triggered for ${pendingGroups.length} pending groups`);
      
      for (const group of pendingGroups) {
        try {
          const groupCenter = uniCloud.importObject('group-center')
          
          if (group.id.startsWith('group_')) {
            // 临时ID，需要创建
            const result = await groupCenter.createGroup({
              name: group.name,
              icon: group.icon || '',
              color: group.color || '#8a2be2'
            })
            if (result.errCode === 0) {
              // 更新本地ID
              group.id = result.data.id
              delete group._needSync
              devLog.log(`[groupStore] Successfully synced new group: ${group.name}`);
            }
          } else {
            // 已有ID，需要更新
            const result = await groupCenter.updateGroup(group.id, {
              name: group.name,
              order: group.order
            })
            if (result.errCode === 0) {
              delete group._needSync
              devLog.log(`[groupStore] Successfully synced updated group: ${group.name}`);
            }
          }
        } catch (error) {
          // 特殊处理：分组不存在或无权限操作（通常是多设备同步冲突）
          if (error.message && (
            error.message.includes('分组不存在') || 
            error.message.includes('无权限') ||
            error.message.includes('not found') ||
            error.message.includes('permission')
          )) {
            // 多设备冲突处理 - 不显示错误，只输出信息日志
            devLog.log(`[groupStore] 多设备同步冲突处理: 分组 "${group.name}" 在云端不存在，可能已被其他设备删除`);
            
            // 移除本地的同步标记，避免重复尝试
            delete group._needSync;
            
            // 如果分组在云端不存在，从本地也移除（但保留默认分组）
            if (!group.isDefault) {
              const localIndex = this.groups.findIndex(g => g.id === group.id);
              if (localIndex !== -1) {
                devLog.log(`[groupStore] 清理本地已删除的分组: ${group.name}`);
                this.groups.splice(localIndex, 1);
              }
            }
          } else {
            // 其他类型的错误才输出错误日志
            devLog.error('同步分组失败:', group.name, error)
          }
        }
      }
      
      this._saveToStorage()
    },
    
    // 智能同步 - 只同步有差异的数据（用于下拉刷新）
    async smartSync() {
      devLog.log('[groupStore] Starting smart sync...');
      
      if (!this.isOnline) {
        devLog.warn('[groupStore] Network unavailable, skipping smart sync');
        return { hasUpdates: false, updatedCount: 0, reason: 'offline' };
      }

      try {
        // 1. 先上传本地待同步的数据
        const pendingGroups = this.groups.filter(group => group._needSync);
        if (pendingGroups.length > 0) {
          devLog.log(`[groupStore] Uploading ${pendingGroups.length} pending groups...`);
          await this.syncPendingData();
        }

        // 2. 获取云端数据摘要信息
        const groupCenter = uniCloud.importObject('group-center');
        const summaryResult = await groupCenter.getGroupSyncSummary();
        
        if (summaryResult.errCode !== 0) {
          throw new Error(summaryResult.errMsg || '获取云端分组摘要失败');
        }

        const cloudSummary = summaryResult.data || {};
        devLog.log('[groupStore] Cloud summary received:', cloudSummary);

        // 3. 对比本地数据摘要
        const localSummary = this._generateLocalSummary();
        devLog.log('[groupStore] Local summary:', localSummary);

        // 4. 判断是否需要同步
        const needsSync = this._compareDataSummaries(localSummary, cloudSummary);
        
        if (!needsSync) {
          devLog.log('[groupStore] Data is up to date, no sync needed');
          return { hasUpdates: false, updatedCount: 0, reason: 'up_to_date' };
        }

        // 5. 执行增量同步
        devLog.log('[groupStore] Data differences detected, performing incremental sync...');
        const syncResult = await this._performIncrementalSync(cloudSummary);
        
        // 6. 清理可能的无效同步标记
        this.cleanupInvalidSyncMarkers();
        
        devLog.log('[groupStore] Smart sync completed:', syncResult);
        return syncResult;

      } catch (error) {
        devLog.error('[groupStore] Smart sync failed:', error);
        throw error;
      }
    },

    // 生成本地数据摘要
    _generateLocalSummary() {
      const nonDefaultGroups = this.groups.filter(g => !g.isDefault);
      
      return {
        count: nonDefaultGroups.length,
        lastModified: this._getLocalGroupLastModified(),
        ids: nonDefaultGroups.map(g => g.id).sort(),
        checksum: this._calculateGroupsChecksum(nonDefaultGroups)
      };
    },

    // 计算分组数据校验和
    _calculateGroupsChecksum(groups) {
      const dataStr = groups
        .map(g => `${g._id || g.id}:${g.name}:${g.updateDate || g.createDate}:${g.order}`)
        .sort()
        .join('|');
      
      // 简单的字符串哈希（用于快速对比）
      let hash = 0;
      for (let i = 0; i < dataStr.length; i++) {
        const char = dataStr.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }
      return hash.toString(36);
    },

    // 对比数据摘要
    _compareDataSummaries(localSummary, cloudSummary) {
      // 如果数量不同，需要同步
      if (localSummary.count !== cloudSummary.count) {
        devLog.log('[groupStore] Count difference detected:', localSummary.count, 'vs', cloudSummary.count);
        return true;
      }

      // 如果校验和不同，需要同步
      if (localSummary.checksum !== cloudSummary.checksum) {
        devLog.log('[groupStore] Checksum difference detected:', localSummary.checksum, 'vs', cloudSummary.checksum);
        return true;
      }

      // 如果云端最后修改时间更新，需要同步
      if (cloudSummary.lastModified && localSummary.lastModified) {
        const cloudTime = new Date(cloudSummary.lastModified).getTime();
        const localTime = new Date(localSummary.lastModified).getTime();
        
        if (cloudTime > localTime) {
          devLog.log('[groupStore] Cloud data is newer:', cloudSummary.lastModified, 'vs', localSummary.lastModified);
          return true;
        }
      }

      return false;
    },

    // 执行增量同步
    async _performIncrementalSync(cloudSummary) {
      // 获取完整的云端数据
      const groupCenter = uniCloud.importObject('group-center');
      const result = await groupCenter.getGroupList({ includeDeleted: true });
      
      if (result.errCode !== 0) {
        throw new Error(result.errMsg || '获取云端分组数据失败');
      }

      const cloudGroups = result.data || [];
      const beforeCount = this.groups.length;

      // 使用现有的智能合并逻辑
      const mergedData = this._mergeGroupData(this.groups, cloudGroups);
      
      // 安全更新数据
      await this._safeUpdateGroups(mergedData);
      
      const afterCount = this.groups.length;
      const updatedCount = Math.abs(afterCount - beforeCount);

      this.lastSyncTime = new Date().toISOString();
      this._saveToStorage();

      return {
        hasUpdates: true,
        updatedCount: updatedCount,
        beforeCount: beforeCount,
        afterCount: afterCount,
        reason: 'incremental_sync'
      };
    },

    // 安全更新分组数据
    async _safeUpdateGroups(newData) {
      try {
        // 使用已有的安全更新策略
        await new Promise((resolve) => {
          if (this.$nextTick) {
            this.$nextTick(() => {
              this.groups = newData;
              resolve();
            });
          } else {
            setTimeout(() => {
              this.groups = newData;
              resolve();
            }, 0);
          }
        });
      } catch (error) {
        devLog.error('[groupStore] Error in safe update, falling back to direct assignment:', error);
        this.groups = newData;
      }
    },

    // 清理无效的同步标记（多设备冲突后的清理）
    cleanupInvalidSyncMarkers() {
      let cleanedCount = 0;
      
      this.groups.forEach(group => {
        // 如果分组有同步标记但缺少必要字段，清除标记
        if (group._needSync && (!group.id || !group.name)) {
          devLog.log(`[groupStore] 清理无效的同步标记: ${group.name || '未知分组'}`);
          delete group._needSync;
          cleanedCount++;
        }
      });
      
      if (cleanedCount > 0) {
        devLog.log(`[groupStore] 已清理 ${cleanedCount} 个无效的同步标记`);
        this._saveToStorage();
      }
      
      return cleanedCount;
    },
    
    // 手动触发完整同步（供用户主动调用）
    async manualSync() {
      if (!this.isOnline) {
        console.log('[groupStore] 当前网络不可用，跳过同步');
        return;
      }

      try {
        // 🔧 删除加载弹窗，静默同步
        // 先同步待上传的数据
        await this.syncPendingData();

        // 再从云端同步最新数据
        await this.syncFromCloud();

        // 🔧 删除同步完成弹窗
        devLog.log('[groupStore] Manual sync completed');

      } catch (error) {
        devLog.error('[groupStore] Manual sync failed:', error);
        // 🔧 删除同步失败弹窗
        console.log('[groupStore] 同步失败');
      }
    },
    
    // 保存到本地存储
    _saveToStorage() {
      uni.setStorageSync('groups', JSON.stringify(this.groups))
      uni.setStorageSync('groupsLastSyncTime', this.lastSyncTime)
    },
    
    // 清理本地数据（用于重新登录时清除旧用户数据）
    clearLocalData() {
      devLog.log('[groupStore] Clearing user-specific data...');
      
      // 只清理用户自定义的分组，保留默认分组
      this.groups = this.groups.filter(group => group.isDefault === true);
      
      // 确保默认分组的完整性
      this.ensureDefaultGroups();
      
      this.lastSyncTime = null;
      this.isLoading = false;
      
      // 清除本地存储，但会通过 _saveToStorage 重新保存默认分组
      uni.removeStorageSync('groups');
      uni.removeStorageSync('groupsLastSyncTime');
      
      // 保存清理后的数据（包含默认分组）
      this._saveToStorage();
      
      devLog.log('[groupStore] User-specific data cleared, default groups preserved');
    },
    
    // 强制重新初始化（用于登录后重新同步）
    async forceInit() {
      devLog.log('[groupStore] Force initialization...');
      
      // 清空当前数据
      this.groups = [];
      this.lastSyncTime = null;
      this.isLoading = false;
      
      // 清理本地存储
      uni.removeStorageSync('groups');
      uni.removeStorageSync('groupsLastSyncTime');
      
      // 强制从云端同步
      await this.syncFromCloud();
      
      // 确保默认分组存在
      this.ensureDefaultGroups();
      
      devLog.log('[groupStore] Force initialization completed');
    },
    
    // 清理重复分组和修复排序
    async cleanupAndFixGroups() {
      devLog.log('[groupStore] Starting group cleanup and fix...');
      
      try {
        // 检查是否有重复的分组名称
        const groupNames = new Map();
        const duplicates = [];
        
        this.groups.forEach((group, index) => {
          if (!group.isDefault) { // 只检查非默认分组
            const lowerName = group.name.toLowerCase();
            if (groupNames.has(lowerName)) {
              duplicates.push({
                index,
                group,
                originalIndex: groupNames.get(lowerName).index
              });
            } else {
              groupNames.set(lowerName, { group, index });
            }
          }
        });
        
        // 移除重复的分组（保留最新的）
        if (duplicates.length > 0) {
          devLog.log('[groupStore] Found and removing', duplicates.length, 'duplicate groups');

          // 从后往前移除，避免索引变化
          duplicates.sort((a, b) => b.index - a.index);
          duplicates.forEach(duplicate => {
            this.groups.splice(duplicate.index, 1);
          });
        }
        
        // 修复排序
        const fixedGroups = this._fixGroupOrder(this.groups);
        this.groups = fixedGroups;
        
        // 保存修复后的数据
        this._saveToStorage();
        
        devLog.log('[groupStore] Group cleanup completed');
        
        // 如果有修复，显示提示
        if (duplicates.length > 0) {
          uni.showToast({
            title: `已清理${duplicates.length}个重复分组`,
            icon: 'success',
            duration: 2000
          });
        }
        
        return {
          duplicatesRemoved: duplicates.length,
          totalGroups: this.groups.length
        };
        
      } catch (error) {
        devLog.error('[groupStore] Group cleanup failed:', error);
        return { error: error.message };
      }
    },
    
    // 手动触发数据修复（供用户或开发者调用）
    async repairGroupData() {
      try {
        // 🔧 删除加载弹窗，静默修复

        // 执行清理
        const result = await this.cleanupAndFixGroups();

        // 重新同步确保数据一致性
        if (this.isOnline) {
          await this.syncFromCloud();
        }

        // 🔧 删除修复完成弹窗
        devLog.log('[groupStore] Data repair completed');

        return result;

      } catch (error) {
        devLog.error('[groupStore] Repair failed:', error);
        // 🔧 删除修复失败弹窗
        console.log('[groupStore] 数据修复失败');
        
        throw error;
      }
    },
    
    // 触发响应式更新（确保UI立即响应）
    _triggerReactiveUpdate() {
      // 优化响应式更新，避免DOM错误
      try {
        // 方法1：使用 nextTick 确保DOM更新在合适的时机
        this.$nextTick && this.$nextTick(() => {
          // 在nextTick中触发更新可以避免DOM操作冲突
          devLog.log('[groupStore] Reactive update triggered via nextTick, groups count:', this.groups.length);
        });
        
        // 方法2：温和的响应式更新 - 避免强制数组重建
        // 只在必要时创建新数组引用
        if (this.groups && this.groups.length > 0) {
          // 确保数组引用变化但不破坏现有对象引用
          const updatedGroups = this.groups.map(group => ({ ...group }));
          this.groups = updatedGroups;
        }
        
        devLog.log('[groupStore] Gentle reactive update completed, groups count:', this.groups.length);
      } catch (error) {
        devLog.error('[groupStore] Reactive update failed:', error);
        // 降级到基本更新
        this.groups = [...(this.groups || [])];
      }
    },
    

    
    // 调试方法：检查分组数据完整性（仅开发环境）
    debugGroupData() {
      // 生产环境直接返回空对象，节省资源
      if (process.env.NODE_ENV === 'production') {
        return { message: 'Debug功能在生产环境中禁用' };
      }
      
      devLog.log('[groupStore] === 分组数据调试信息 ===');
      devLog.log('[groupStore] 总分组数量:', this.groups.length);
      
      this.groups.forEach((group, index) => {
        devLog.log(`[groupStore] 分组 ${index}:`, {
          id: group.id,
          _id: group._id,
          name: group.name,
          isDefault: group.isDefault,
          _needSync: group._needSync,
          _source: group._source
        });
      });
      
      const pendingGroups = this.groups.filter(g => g._needSync);
      devLog.log('[groupStore] 待同步分组数量:', pendingGroups.length);
      
      if (pendingGroups.length > 0) {
        devLog.log('[groupStore] 待同步分组详情:', pendingGroups.map(g => ({
          id: g.id,
          name: g.name
        })));
      }
      
      return {
        totalCount: this.groups.length,
        pendingCount: pendingGroups.length,
        groups: this.groups.map(g => ({
          id: g.id,
          _id: g._id,
          name: g.name,
          isDefault: g.isDefault,
          _needSync: g._needSync
        }))
      };
    },

    /**
     * 🚀 处理实时数据更新
     * 当收到实时推送时调用此方法
     */
    async _updateLocalFromRealtimeData(realtimeData) {
      try {
        devLog.log('[groupStore] 📂 收到实时分组数据更新:', realtimeData.length, '个分组');
        
        // 合并实时数据到本地
        const mergedData = this._mergeGroupData(this.groups, realtimeData);
        
        // 更新本地数据
        this.groups = mergedData;
        
        // 保存到本地存储
        this._saveToStorage();
        
        // 触发响应式更新
        this._triggerReactiveUpdate();
        
        devLog.log('[groupStore] ✅ 实时分组数据更新完成');
        
      } catch (error) {
        devLog.error('[groupStore] 处理实时分组数据更新失败:', error);
      }
    },
    
    // 重新整理分组顺序，确保正确的排序
    reorganizeGroupOrder() {
      devLog.log('[groupStore] Reorganizing group order...');
      
      // 分离默认分组和用户分组
      const defaultGroups = this.groups.filter(g => g.isDefault);
      const userGroups = this.groups.filter(g => !g.isDefault);
      
      // 确保默认分组有正确的order
      defaultGroups.forEach((group, index) => {
        if (group.id === 'all') {
          group.order = 0;
        } else if (group.id === 'gift') {
          group.order = 1;
        } else if (group.id === 'friend-visible') {
          group.order = 2;
        } else {
          group.order = index;
        }
      });
      
      // 重新分配用户分组的order，从3开始
      userGroups.sort((a, b) => (a.order || 999) - (b.order || 999));
      userGroups.forEach((group, index) => {
        group.order = 3 + index; // 默认分组占用0-2，用户分组从3开始
      });
      
      // 合并并排序
      this.groups = [...defaultGroups, ...userGroups];
      this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));
      
      // 保存到本地存储
      this._saveToStorage();
      
      devLog.log('[groupStore] Group order reorganized:', this.groups.map(g => ({ 
        id: g.id, 
        name: g.name, 
        order: g.order, 
        isDefault: g.isDefault 
      })));
      
      // 触发响应式更新
      this._triggerReactiveUpdate();
    },

    // 🚀 新增：支持新同步架构的方法

    /**
     * 添加分组到列表（供实时同步调用）
     */
    addGroupToList(group) {
      const existingIndex = this.groups.findIndex(g => g.id === group.id || g._id === group._id);
      if (existingIndex === -1) {
        this.groups.push({
          ...group,
          id: group.id || group._id // 确保有 id 字段
        });
        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));
        this._saveToStorage();
        devLog.log('[groupStore] 🚀 添加分组到列表:', group.name);
      }
    },

    /**
     * 更新列表中的分组（供实时同步调用）
     */
    updateGroupInList(group) {
      const index = this.groups.findIndex(g => g.id === group.id || g._id === group._id);
      if (index !== -1) {
        this.groups[index] = {
          ...group,
          id: group.id || group._id // 确保有 id 字段
        };
        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0));
        this._saveToStorage();
        devLog.log('[groupStore] 🚀 更新列表中的分组:', group.name);
      }
    },

    /**
     * 从列表中移除分组（供实时同步调用）
     */
    removeGroupFromList(groupId) {
      const index = this.groups.findIndex(g => g.id === groupId || g._id === groupId);
      if (index !== -1) {
        const groupName = this.groups[index].name;
        this.groups.splice(index, 1);
        this._saveToStorage();
        devLog.log('[groupStore] 🚀 从列表中移除分组:', groupName);
      }
    },

    // 注意：syncFromCloud 方法已在上面定义，这里移除重复定义

    // ==================== 新增同步优化方法 ====================

    /**
     * 通用分组名称检查方法（避免重复逻辑）
     */
    _checkGroupNameExists(name, excludeId = null) {
      if (!name) return false;

      const lowerName = name.toLowerCase();
      return this.groups.some(group => {
        // 排除指定ID的分组（用于更新时检查）
        if (excludeId && (group.id === excludeId || group._id === excludeId)) {
          return false;
        }
        return group.name.toLowerCase() === lowerName;
      });
    },

    /**
     * 从同步中添加分组（避免触发推送）
     */
    addGroupFromSync(group) {
      const existingIndex = this.groups.findIndex(g => g.id === group.id || g._id === group._id)
      if (existingIndex === -1) {
        this.groups.push({
          ...group,
          id: group._id || group.id
        })
        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0))
        this._saveToStorage()
        devLog.log(`[groupStore] 从同步添加分组: ${group.name}`)
      }
    },

    /**
     * 从同步中更新分组（避免触发推送）
     */
    updateGroupFromSync(group) {
      const index = this.groups.findIndex(g => g.id === group.id || g._id === group._id)
      if (index !== -1) {
        this.groups[index] = {
          ...group,
          id: group._id || group.id
        }
        this.groups.sort((a, b) => (a.order || 0) - (b.order || 0))
        this._saveToStorage()
        devLog.log(`[groupStore] 从同步更新分组: ${group.name}`)
      }
    },

    /**
     * 从同步中删除分组（避免触发推送）
     */
    removeGroupById(groupId) {
      const index = this.groups.findIndex(g => g.id === groupId || g._id === groupId)
      if (index !== -1) {
        const removedGroup = this.groups.splice(index, 1)[0]
        this._saveToStorage()
        devLog.log(`[groupStore] 从同步删除分组: ${removedGroup.name}`)
      }
    }
  }
})
