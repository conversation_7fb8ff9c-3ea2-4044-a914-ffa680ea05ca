{"version": 3, "file": "toastInterceptor.js", "sources": ["utils/toastInterceptor.js"], "sourcesContent": ["/**\n * 全局Toast拦截器\n * 用于拦截和过滤版本冲突相关的弹窗\n */\n\nclass ToastInterceptor {\n  constructor() {\n    this.originalShowToast = null\n    this.originalShowModal = null\n    this.isInitialized = false\n    \n    // 版本冲突相关的关键词\n    this.conflictKeywords = [\n      '版本冲突', '数据冲突', '逻辑时钟冲突', 'version conflict', 'conflict',\n      '数据版本冲突', '冲突', 'conflict detected', 'conflict resolution',\n      '同步冲突', 'sync conflict', '更新冲突', 'update conflict'\n    ]\n    \n    // 拦截统计\n    this.interceptCount = 0\n    this.lastInterceptTime = 0\n  }\n\n  // 初始化拦截器\n  init() {\n    if (this.isInitialized) return\n    \n    try {\n      // 保存原始方法\n      this.originalShowToast = uni.showToast\n      this.originalShowModal = uni.showModal\n      \n      // 替换uni.showToast\n      uni.showToast = (options) => {\n        return this.interceptShowToast(options)\n      }\n      \n      // 替换uni.showModal\n      uni.showModal = (options) => {\n        return this.interceptShowModal(options)\n      }\n      \n      this.isInitialized = true\n      console.log('[ToastInterceptor] 🚀 全局弹窗拦截器已启动')\n    } catch (error) {\n      console.error('[ToastInterceptor] 初始化失败:', error)\n    }\n  }\n\n  // 检查是否是冲突相关的消息\n  isConflictMessage(message) {\n    if (!message || typeof message !== 'string') return false\n    \n    const lowerMessage = message.toLowerCase()\n    return this.conflictKeywords.some(keyword => \n      lowerMessage.includes(keyword.toLowerCase())\n    )\n  }\n\n  // 拦截showToast\n  interceptShowToast(options) {\n    if (!options || !options.title) {\n      return this.originalShowToast.call(uni, options)\n    }\n    \n    if (this.isConflictMessage(options.title)) {\n      this.interceptCount++\n      this.lastInterceptTime = Date.now()\n      \n      console.log(`[ToastInterceptor] 🛡️ 拦截版本冲突Toast: \"${options.title}\"`)\n      console.log(`[ToastInterceptor] 📊 累计拦截次数: ${this.interceptCount}`)\n      \n      // 不显示弹窗，直接返回成功\n      return Promise.resolve()\n    }\n    \n    // 非冲突消息正常显示\n    return this.originalShowToast.call(uni, options)\n  }\n\n  // 拦截showModal\n  interceptShowModal(options) {\n    if (!options) {\n      return this.originalShowModal.call(uni, options)\n    }\n    \n    const title = options.title || ''\n    const content = options.content || ''\n    \n    if (this.isConflictMessage(title) || this.isConflictMessage(content)) {\n      this.interceptCount++\n      this.lastInterceptTime = Date.now()\n      \n      console.log(`[ToastInterceptor] 🛡️ 拦截版本冲突Modal: \"${title}\" - \"${content}\"`)\n      console.log(`[ToastInterceptor] 📊 累计拦截次数: ${this.interceptCount}`)\n      \n      // 不显示弹窗，模拟用户点击确认\n      if (options.success) {\n        setTimeout(() => {\n          options.success({ confirm: true, cancel: false })\n        }, 0)\n      }\n      \n      return Promise.resolve()\n    }\n    \n    // 非冲突消息正常显示\n    return this.originalShowModal.call(uni, options)\n  }\n\n  // 恢复原始方法\n  restore() {\n    if (!this.isInitialized) return\n    \n    try {\n      if (this.originalShowToast) {\n        uni.showToast = this.originalShowToast\n      }\n      \n      if (this.originalShowModal) {\n        uni.showModal = this.originalShowModal\n      }\n      \n      this.isInitialized = false\n      console.log('[ToastInterceptor] 🔄 全局弹窗拦截器已恢复')\n    } catch (error) {\n      console.error('[ToastInterceptor] 恢复失败:', error)\n    }\n  }\n\n  // 获取拦截统计\n  getStats() {\n    return {\n      interceptCount: this.interceptCount,\n      lastInterceptTime: this.lastInterceptTime,\n      isActive: this.isInitialized\n    }\n  }\n\n  // 重置统计\n  resetStats() {\n    this.interceptCount = 0\n    this.lastInterceptTime = 0\n    console.log('[ToastInterceptor] 📊 统计已重置')\n  }\n\n  // 添加自定义关键词\n  addConflictKeyword(keyword) {\n    if (keyword && !this.conflictKeywords.includes(keyword)) {\n      this.conflictKeywords.push(keyword)\n      console.log(`[ToastInterceptor] ➕ 添加冲突关键词: \"${keyword}\"`)\n    }\n  }\n\n  // 移除关键词\n  removeConflictKeyword(keyword) {\n    const index = this.conflictKeywords.indexOf(keyword)\n    if (index > -1) {\n      this.conflictKeywords.splice(index, 1)\n      console.log(`[ToastInterceptor] ➖ 移除冲突关键词: \"${keyword}\"`)\n    }\n  }\n}\n\n// 创建全局实例\nconst toastInterceptor = new ToastInterceptor()\n\n// 导出实例和类\nexport default toastInterceptor\nexport { ToastInterceptor }\n"], "names": ["uni"], "mappings": ";;AAKA,MAAM,iBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AAGrB,SAAK,mBAAmB;AAAA,MACtB;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAoB;AAAA,MAC9C;AAAA,MAAU;AAAA,MAAM;AAAA,MAAqB;AAAA,MACrC;AAAA,MAAQ;AAAA,MAAiB;AAAA,MAAQ;AAAA,IAClC;AAGD,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AAAA,EAC1B;AAAA;AAAA,EAGD,OAAO;AACL,QAAI,KAAK;AAAe;AAExB,QAAI;AAEF,WAAK,oBAAoBA,cAAAA,MAAI;AAC7B,WAAK,oBAAoBA,cAAAA,MAAI;AAG7BA,0BAAI,YAAY,CAAC,YAAY;AAC3B,eAAO,KAAK,mBAAmB,OAAO;AAAA,MACvC;AAGDA,0BAAI,YAAY,CAAC,YAAY;AAC3B,eAAO,KAAK,mBAAmB,OAAO;AAAA,MACvC;AAED,WAAK,gBAAgB;AACrBA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,kCAAkC;AAAA,IAC/C,SAAQ,OAAO;AACdA,oBAAAA,MAAA,MAAA,SAAA,mCAAc,6BAA6B,KAAK;AAAA,IACjD;AAAA,EACF;AAAA;AAAA,EAGD,kBAAkB,SAAS;AACzB,QAAI,CAAC,WAAW,OAAO,YAAY;AAAU,aAAO;AAEpD,UAAM,eAAe,QAAQ,YAAa;AAC1C,WAAO,KAAK,iBAAiB;AAAA,MAAK,aAChC,aAAa,SAAS,QAAQ,aAAa;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA,EAGD,mBAAmB,SAAS;AAC1B,QAAI,CAAC,WAAW,CAAC,QAAQ,OAAO;AAC9B,aAAO,KAAK,kBAAkB,KAAKA,cAAAA,OAAK,OAAO;AAAA,IAChD;AAED,QAAI,KAAK,kBAAkB,QAAQ,KAAK,GAAG;AACzC,WAAK;AACL,WAAK,oBAAoB,KAAK,IAAK;AAEnCA,0BAAA,MAAA,OAAA,mCAAY,wCAAwC,QAAQ,KAAK,GAAG;AACpEA,0BAAA,MAAA,OAAA,mCAAY,iCAAiC,KAAK,cAAc,EAAE;AAGlE,aAAO,QAAQ,QAAS;AAAA,IACzB;AAGD,WAAO,KAAK,kBAAkB,KAAKA,cAAAA,OAAK,OAAO;AAAA,EAChD;AAAA;AAAA,EAGD,mBAAmB,SAAS;AAC1B,QAAI,CAAC,SAAS;AACZ,aAAO,KAAK,kBAAkB,KAAKA,cAAAA,OAAK,OAAO;AAAA,IAChD;AAED,UAAM,QAAQ,QAAQ,SAAS;AAC/B,UAAM,UAAU,QAAQ,WAAW;AAEnC,QAAI,KAAK,kBAAkB,KAAK,KAAK,KAAK,kBAAkB,OAAO,GAAG;AACpE,WAAK;AACL,WAAK,oBAAoB,KAAK,IAAK;AAEnCA,0BAAY,MAAA,OAAA,mCAAA,wCAAwC,KAAK,QAAQ,OAAO,GAAG;AAC3EA,0BAAA,MAAA,OAAA,mCAAY,iCAAiC,KAAK,cAAc,EAAE;AAGlE,UAAI,QAAQ,SAAS;AACnB,mBAAW,MAAM;AACf,kBAAQ,QAAQ,EAAE,SAAS,MAAM,QAAQ,OAAO;AAAA,QACjD,GAAE,CAAC;AAAA,MACL;AAED,aAAO,QAAQ,QAAS;AAAA,IACzB;AAGD,WAAO,KAAK,kBAAkB,KAAKA,cAAAA,OAAK,OAAO;AAAA,EAChD;AAAA;AAAA,EAGD,UAAU;AACR,QAAI,CAAC,KAAK;AAAe;AAEzB,QAAI;AACF,UAAI,KAAK,mBAAmB;AAC1BA,4BAAI,YAAY,KAAK;AAAA,MACtB;AAED,UAAI,KAAK,mBAAmB;AAC1BA,4BAAI,YAAY,KAAK;AAAA,MACtB;AAED,WAAK,gBAAgB;AACrBA,oBAAAA,MAAA,MAAA,OAAA,oCAAY,kCAAkC;AAAA,IAC/C,SAAQ,OAAO;AACdA,oBAAAA,yDAAc,4BAA4B,KAAK;AAAA,IAChD;AAAA,EACF;AAAA;AAAA,EAGD,WAAW;AACT,WAAO;AAAA,MACL,gBAAgB,KAAK;AAAA,MACrB,mBAAmB,KAAK;AAAA,MACxB,UAAU,KAAK;AAAA,IAChB;AAAA,EACF;AAAA;AAAA,EAGD,aAAa;AACX,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AACzBA,kBAAAA,MAAA,MAAA,OAAA,oCAAY,6BAA6B;AAAA,EAC1C;AAAA;AAAA,EAGD,mBAAmB,SAAS;AAC1B,QAAI,WAAW,CAAC,KAAK,iBAAiB,SAAS,OAAO,GAAG;AACvD,WAAK,iBAAiB,KAAK,OAAO;AAClCA,oBAAY,MAAA,MAAA,OAAA,oCAAA,kCAAkC,OAAO,GAAG;AAAA,IACzD;AAAA,EACF;AAAA;AAAA,EAGD,sBAAsB,SAAS;AAC7B,UAAM,QAAQ,KAAK,iBAAiB,QAAQ,OAAO;AACnD,QAAI,QAAQ,IAAI;AACd,WAAK,iBAAiB,OAAO,OAAO,CAAC;AACrCA,oBAAY,MAAA,MAAA,OAAA,oCAAA,kCAAkC,OAAO,GAAG;AAAA,IACzD;AAAA,EACF;AACH;AAGK,MAAC,mBAAmB,IAAI,iBAAgB;;"}